# Papermill Parameters - Override these when running with papermill

site_name = "trino_enel" # Project type for data organization
project_type = "ENEL" # Site name for output file naming
point_cloud_path = None               # Optional: specific point cloud file path

input_point_cloud = "Trino_Fly_2_Shifted.las"
search_path = "../../../data/raw/trino_enel/pointcloud"
output_dir = "../../../data/output_runs/denoising"
coordinate_system = "EPSG:32632"

# Statistical Outlier Removal Parameters
# Registration-friendly)
stat_nb_neighbors = 25    # Number of neighbors for statistical analysis, Reduced from 50
stat_std_ratio = 2.0      # Standard deviation threshold (higher = more permissive), Increased from 1.5 (less aggressive)
radius_nb_points = 2      # Minimum neighbors required within radius, Reduced from 5
radius_search = 1.2       # Search radius in meters ,Reduced from 0.1 (preserve more detail)

enable_voxel_filtering = True         # Enable/disable voxel grid filtering
voxel_size = 0.05         # Voxel size in meters (10cm default), Reduced from 1.0


# Processing Parameters
max_points_processing = 1000000       # Maximum points to process (0 = no limit) , 10M point limit for memory management
downsample_factor = 0.3               # Initial downsampling factor (1.0 = no downsampling)
save_intermediate_results = False     # Save intermediate denoising steps
generate_comparison_plots = True      # Create before/after visualizations
use_mlflow = True                     # Enable MLflow tracking
enable_radius_filtering = False       # Disable radius filtering for registration


# Import required libraries
import numpy as np
import json
import matplotlib.pyplot as plt
import time
import logging
from pathlib import Path
from datetime import datetime
from tqdm import tqdm
import open3d as o3d
import laspy
import pandas as pd

# MLflow for experiment tracking
import mlflow
import mlflow.sklearn

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

use_mlflow = True

logger.info("Point Cloud Denoising - Preprocessing Stage 1 - Starting...")
logger.info(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Setup paths following project data organization
project_root = Path('../../../')  # Navigate to project root from notebooks/preprocessing/
data_path = project_root / 'data'
raw_path = data_path / 'raw' / site_name 
processed_path = data_path / 'processed'  / site_name / 'denoising'
output_path = data_path / 'output_runs'/site_name
analysis_output_path = data_path / 'analysis_output'/site_name

# Create directories
processed_path.mkdir(parents=True, exist_ok=True)
output_path.mkdir(parents=True, exist_ok=True)
analysis_output_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Project root: {project_root.resolve()}")
logger.info(f"Raw data path: {raw_path.resolve()}")
logger.info(f"Processed data path: {processed_path.resolve()}")
logger.info(f"Output runs path: {output_path.resolve()}")
logger.info(f"Analysis output path: {analysis_output_path.resolve()}")

# Initialize MLflow if enabled
if use_mlflow:
    if mlflow.active_run():
        mlflow.end_run()

    mlflow.set_experiment(f"point_cloud_denoising_{project_type}")
    mlflow.start_run(run_name=f"denoising_{site_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    
    mlflow.log_param("project_type", project_type)
    mlflow.log_param("site_name", site_name)
    mlflow.log_param("stat_nb_neighbors", stat_nb_neighbors)
    mlflow.log_param("stat_std_ratio", stat_std_ratio)
    mlflow.log_param("radius_nb_points", radius_nb_points)
    mlflow.log_param("radius_search", radius_search)
    mlflow.log_param("voxel_size", voxel_size)

    mlflow.log_param("pipeline_stage", "denoising")
    mlflow.log_param("next_stage", "registration")
    mlflow.log_param("registration_optimized", True)


# Load point cloud data
start_time = time.time()
logger.info("Loading point cloud data...")


input_point_cloud = "Trino_Fly_2_Shifted.las"
coordinate_system = "EPSG:32633"

point_cloud_path = f'{raw_path}/pointcloud/{input_point_cloud}'

input_path = Path(point_cloud_path)
logger.info(f"Using point cloud file: {input_path}")

if not input_path.exists():
    raise FileNotFoundError(f"Input path does not exist: {input_path}")

# Load LAS file
#las_file = laspy.read(input_path)
#points = np.vstack((las_file.x, las_file.y, las_file.z)).transpose()
#logger.info(f"Loaded {len(points):,} points from {input_path}")

las = laspy.read(input_path)
points = las.xyz  # This is already a (N, 3) NumPy array
logger.info(f"Loaded {points.shape[0]:,} points in {time.time() - start_time:.2f} seconds")


# Apply initial downsampling 
if downsample_factor < 1.0:
    n_points = int(len(points) * downsample_factor)
    indices = np.random.choice(len(points), n_points, replace=False)
    points = points[indices]
    logger.info(f"Initial downsampling to {len(points)} points (factor: {downsample_factor})")
else:
    logger.info("No initial downsampling applied")

# Apply max points limit 
if max_points_processing > 0 and len(points) > max_points_processing:
    indices = np.random.choice(len(points), max_points_processing, replace=False)
    points = points[indices]
    logger.info(f"Limited to {len(points)} points for processing")
else:
    logger.info("No max points limit applied")

logger.info(f"Bounds: X[{points[:, 0].min():.2f}, {points[:, 0].max():.2f}], "
      f"Y[{points[:, 1].min():.2f}, {points[:, 1].max():.2f}], "
      f"Z[{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")

# Convert to Open3D point cloud
original_pcd = o3d.geometry.PointCloud()
original_pcd.points = o3d.utility.Vector3dVector(points)

# Log to MLflow
if use_mlflow:
    mlflow.log_metric("input_points", len(points))
    mlflow.log_param("input_file", str(input_path))

# Initialize denoising pipeline
denoising_results = {}
current_pcd = original_pcd
pipeline_start = time.time()

logger.info("STARTING DENOISING PIPELINE:")
logger.info("=" * 50)
logger.info(f"Original point count: {len(current_pcd.points):,}")
logger.info("=" * 50)

# Step 1: Statistical Outlier Removal
logger.info(f"Step 1: Statistical Outlier Removal")
logger.info(f"  Parameters: {stat_nb_neighbors} neighbors, {stat_std_ratio} std ratio")

step1_start = time.time()

stat_filtered_pcd, stat_outlier_indices = current_pcd.remove_statistical_outlier(
    nb_neighbors=stat_nb_neighbors,
    std_ratio=stat_std_ratio
)
step1_time = time.time() - step1_start

stat_outliers_removed = len(current_pcd.points) - len(stat_filtered_pcd.points)
stat_removal_ratio = stat_outliers_removed / len(current_pcd.points)

logger.info(f"  Outliers removed: {stat_outliers_removed:,} ({stat_removal_ratio*100:.2f}%)")
logger.info(f"  Remaining points: {len(stat_filtered_pcd.points):,}")
logger.info(f"  Processing time: {step1_time:.2f} seconds")

denoising_results['statistical_outlier_removal'] = {
    'outliers_removed': stat_outliers_removed,
    'removal_ratio': stat_removal_ratio,
    'remaining_points': len(stat_filtered_pcd.points),
    'processing_time': step1_time
}

# Save intermediate result 
if save_intermediate_results:
    step1_path = processed_path / f"{site_name}_step1_statistical_filtered.ply"
    o3d.io.write_point_cloud(str(step1_path), stat_filtered_pcd)
    logger.info(f"  Intermediate result saved: {step1_path}")

current_pcd = stat_filtered_pcd

# Step 2: Radius Outlier Removal
if enable_radius_filtering:

    logger.info(f"Step 2: Radius Outlier Removal")
    logger.info("=" * 50)

    logger.info(f"  Parameters: {radius_nb_points} min neighbors, {radius_search}m radius")

    step2_start = time.time()
    radius_filtered_pcd, radius_outlier_indices = current_pcd.remove_radius_outlier(
        nb_points=radius_nb_points,
        radius=radius_search
    )
    step2_time = time.time() - step2_start

    radius_outliers_removed = len(current_pcd.points) - len(radius_filtered_pcd.points)
    radius_removal_ratio = radius_outliers_removed / len(current_pcd.points)

    logger.info(f"  Outliers removed: {radius_outliers_removed:,} ({radius_removal_ratio*100:.2f}%)")
    logger.info(f"  Remaining points: {len(radius_filtered_pcd.points):,}")
    logger.info(f"  Processing time: {step2_time:.2f} seconds")

    denoising_results['radius_outlier_removal'] = {
        'outliers_removed': radius_outliers_removed,
        'removal_ratio': radius_removal_ratio,
        'remaining_points': len(radius_filtered_pcd.points),
        'processing_time': step2_time
    }

    # Save intermediate result if requested
    if save_intermediate_results:
        step2_path = processed_path / f"{site_name}_step2_radius_filtered.ply"
        o3d.io.write_point_cloud(str(step2_path), radius_filtered_pcd)
        logger.info(f"  Intermediate result saved: {step2_path}")

    current_pcd = radius_filtered_pcd
else:
    logger.info(f"\nStep 2: Radius Outlier Removal - SKIPPED")
    denoising_results['radius_outlier_removal'] = {'skipped': True}
    

# Step 3: Voxel Grid Filtering (logger.info)
if enable_voxel_filtering:
    logger.info(f"Step 3: Voxel Grid Filtering")
    logger.info("=" * 50)

    logger.info(f"  Parameters: {voxel_size}m voxel size")
    
    step3_start = time.time()
    voxel_filtered_pcd = current_pcd.voxel_down_sample(voxel_size=voxel_size)
    step3_time = time.time() - step3_start
    
    #voxel_points_removed = len(current_pcd.points) - len(voxel_filtered_pcd.points)
    #voxel_reduction_ratio = voxel_points_removed / len(current_pcd.points)

    voxel_points_removed = len(current_pcd.points) - len(voxel_filtered_pcd.points)
    original_point_count = len(current_pcd.points)

    if original_point_count > 0:
        voxel_reduction_ratio = voxel_points_removed / original_point_count
    else:
        voxel_reduction_ratio = 0.0  # or float('nan') if you want to explicitly flag it

    logger.info(f"  Points reduced: {voxel_points_removed:,} ({voxel_reduction_ratio*100:.2f}%)")
    logger.info(f"  Remaining points: {len(voxel_filtered_pcd.points):,}")
    logger.info(f"  Processing time: {step3_time:.2f} seconds")
    logger.info("=" * 50)
    
    denoising_results['voxel_grid_filtering'] = {
        'points_reduced': voxel_points_removed,
        'reduction_ratio': voxel_reduction_ratio,
        'remaining_points': len(voxel_filtered_pcd.points),
        'processing_time': step3_time
    }
    
    # Save intermediate result 
    if save_intermediate_results:
        step3_path = processed_path / f"{site_name}_step3_voxel_filtered.ply"
        o3d.io.write_point_cloud(str(step3_path), voxel_filtered_pcd)
        logger.info(f"  Intermediate result saved: {step3_path}")
    
    current_pcd = voxel_filtered_pcd
else:
    logger.info(f"\nStep 3: Voxel Grid Filtering - SKIPPED")
    denoising_results['voxel_grid_filtering'] = {'skipped': True}

# Final denoised point cloud
final_pcd = current_pcd
pipeline_time = time.time() - pipeline_start

logger.info(f"DENOISING PIPELINE COMPLETED:")
logger.info("=" * 50)

logger.info(f"Original points: {len(original_pcd.points):,}")
logger.info(f"Final points: {len(final_pcd.points):,}")

total_removed = len(original_pcd.points) - len(final_pcd.points)
total_removal_ratio = total_removed / len(original_pcd.points)

logger.info(f"Total removed: {total_removed:,} ({total_removal_ratio*100:.2f}%)")
logger.info(f"Total pipeline time: {pipeline_time:.2f} seconds")

# Point Cloud Analysis Utilities & Execution
def safe_density(pts): return len(pts) / ((np.ptp(pts[:,0]) * np.ptp(pts[:,1])) or 1)
def safe_std_z(pts): return float(np.std(pts[:, 2])) if len(pts) > 0 else 0.0

# Execute quality analysis
orig_pts, final_pts = np.asarray(original_pcd.points), np.asarray(final_pcd.points)
orig_density, final_density = safe_density(orig_pts), safe_density(final_pts)
orig_z_std, final_z_std = safe_std_z(orig_pts), safe_std_z(final_pts)
removal_ratio = 1 - (len(final_pts) / len(orig_pts))
density_change = ((final_density - orig_density) / orig_density * 100) if orig_density > 0 else 0.0
noise_reduction = ((orig_z_std - final_z_std) / orig_z_std * 100) if orig_z_std > 0 else 0.0
registration_ready = removal_ratio < 0.6 and final_density > 0.05

# Consolidated output
logger.info("DENOISING QUALITY ANALYSIS:")
logger.info("=" * 50)
logger.info(f"Points: {len(orig_pts):,} → {len(final_pts):,} ({removal_ratio*100:.1f}% removed)")
logger.info(f"Density: {orig_density:.2f} → {final_density:.2f} points/m² ({density_change:.1f}%)")
logger.info(f"Noise: {orig_z_std:.3f} → {final_z_std:.3f}m std ({noise_reduction:.1f}% reduction)")
logger.info(f"Registration ready: {'YES' if registration_ready else 'NO'}")
logger.info("=" * 50)


# Visualization
if generate_comparison_plots:
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    # Simple sampling for visualization
    viz_size = 25000
    orig_viz = orig_pts[::len(orig_pts)//min(viz_size, len(orig_pts))] if len(orig_pts) > viz_size else orig_pts
    final_viz = final_pts[::len(final_pts)//min(viz_size, len(final_pts))] if len(final_pts) > viz_size else final_pts
    
    # Before/After comparison (top view)
    axes[0].scatter(orig_viz[:, 0], orig_viz[:, 1], c=orig_viz[:, 2], cmap='terrain', s=0.5, alpha=0.7)
    axes[0].set_title(f'Original: {len(orig_pts):,} points')
    axes[0].axis('equal')
    
    axes[1].scatter(final_viz[:, 0], final_viz[:, 1], c=final_viz[:, 2], cmap='terrain', s=0.5, alpha=0.7)
    axes[1].set_title(f'Denoised: {len(final_pts):,} points ({removal_ratio*100:.1f}% removed)')
    axes[1].axis('equal')
    
    plt.tight_layout()
    
    viz_path = output_path / f"{site_name}_denoising_comparison.png"
    plt.savefig(viz_path, dpi=150, bbox_inches='tight')
    logger.info(f"Visualization saved: {viz_path}")
    plt.show()

from laspy import LasHeader, LasData
from pyproj import CRS

# === Save as PLY ===
final_output_path = processed_path / f"{site_name}_denoised_for_registration.ply"
o3d.io.write_point_cloud(str(final_output_path), final_pcd)
logger.info(f"Registration-ready point cloud saved (.ply): {final_output_path}")

# Save timestamped version
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
batch_output_path = processed_path / f"{site_name}_denoised_{timestamp}.ply"
o3d.io.write_point_cloud(str(batch_output_path), final_pcd)
logger.info(f"Batch processing file saved (.ply): {batch_output_path}")

# === Save as LAS with CRS ===
las_output_path = processed_path / f"{site_name}_denoised_for_registration.las"

# Create header with CRS from EPSG code
epsg_code = int(coordinate_system.split(":")[1])  # e.g., "EPSG:32633" → 32633
header = LasHeader(point_format=3, version="1.2")
header.parse_crs(CRS.from_epsg(epsg_code))

# Write points
points_np = np.asarray(final_pcd.points)
las = LasData(header)
las.x = points_np[:, 0]
las.y = points_np[:, 1]
las.z = points_np[:, 2]
las.write(str(las_output_path))

logger.info(f"Registration-ready point cloud saved (.las): {las_output_path} with CRS EPSG:{epsg_code}")



# Single JSON export - Fixed and simplified
denoising_summary = {
    'timestamp': datetime.now().isoformat(),
    'site_name': site_name,
    'project_type': project_type,
    'input_file': str(input_path),
    'output_file': str(final_output_path),
    'points': {
        'original': len(orig_pts), 
        'final': len(final_pts),
        'removed': len(orig_pts) - len(final_pts)
    },
    'metrics': {
        'removal_ratio': float(removal_ratio),
        'density_change_pct': float(density_change),
        'noise_reduction_pct': float(noise_reduction),
        'processing_time_seconds': float(pipeline_time)
    },
    'quality': {
        'original_density': float(orig_density),
        'final_density': float(final_density),
        'original_z_std': float(orig_z_std),
        'final_z_std': float(final_z_std)
    },
    'registration_ready': bool(registration_ready),
    'denoising_steps': denoising_results
}

# Save to output_runs with timestamp
results_path = output_path / f"{site_name}_denoising_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
with open(results_path, 'w') as f:
    json.dump(denoising_summary, f, indent=2)

logger.info(f"Denoising results saved: {results_path}")

# Cleaned MLflow Logging - No Duplicates
if use_mlflow:
    # Core metrics (using updated variable names from condensed code)
    mlflow.log_metric("original_density", orig_density)
    mlflow.log_metric("final_density", final_density)
    mlflow.log_metric("density_change_pct", density_change)
    mlflow.log_metric("original_z_std", orig_z_std)
    mlflow.log_metric("final_z_std", final_z_std)
    mlflow.log_metric("z_std_reduction_pct", noise_reduction)
    mlflow.log_metric("removal_ratio", removal_ratio)
    mlflow.log_metric("registration_ready", int(registration_ready))  # Convert bool to int
    
    # Quality flags
    if orig_z_std > 0 and final_z_std > 0.9 * orig_z_std:
        mlflow.log_param("flag_under_cleaning", "Z variance not sufficiently reduced")
    if orig_density > 0 and final_density < 0.5 * orig_density:
        mlflow.log_param("flag_over_cleaning", "Point density dropped too much")
    
    # Summary tag
    mlflow.set_tag("denoising_summary",
        f"Points: {len(orig_pts):,}→{len(final_pts):,} | "
        f"Noise: {orig_z_std:.2f}→{final_z_std:.2f}m | "
        f"Ready: {'Yes' if registration_ready else 'No'}")

# Final Summary 
logger.info("DENOISING PIPELINE COMPLETED SUCCESSFULLY")
logger.info("=" * 50)
logger.info(f"Site: {site_name}")
logger.info(f"Input: {len(orig_pts):,} points")
logger.info(f"Output: {len(final_pts):,} points")
logger.info(f"Removed: {len(orig_pts) - len(final_pts):,} points ({removal_ratio*100:.1f}%)")
logger.info(f"Processing time: {pipeline_time:.1f} seconds")
logger.info(f"Registration ready: {'YES' if registration_ready else 'NO'}")
logger.info(f"Output file: {final_output_path}")
logger.info(f"Completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
logger.info("=" * 50)