# Parameters (Papermill)
ground_method = "ransac_pmf"  # Ground segmentation method: csf, pmf, ransac
site_name = "trino_enel"

icp_max_iterations = 50
icp_tolerance = 1e-6
voxel_size = 0.02  # For downsampling if needed

# Hybrid parameters
xy_refinement_threshold = 10.0  # Only accept small XY refinements
max_correspondence_distance = 2.0  # Conservative for 2D ICP

STANDARD_SAMPLE_SIZE = 50000
RANDOM_SEED = 42              

output_dir = "../../../data/output_runs/icp_alignment"
enable_visualization = True
save_results = True

# Imports
import numpy as np
import open3d as o3d
import matplotlib.pyplot as plt
import pandas as pd
import time
import json
from pathlib import Path
from datetime import datetime
from scipy.spatial import cKDTree

# Setup
np.random.seed(42)
output_path = Path(output_dir) / ground_method
output_path.mkdir(parents=True, exist_ok=True)

print("=== HYBRID XY-COORDINATE ALIGNMENT ===")
print(f"Strategy: Coordinate-only Z + Selective XY refinement")
print(f"Site: {site_name}")
print(f"Output: {output_path}")
print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# Define file paths
drone_file = f"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply"
ifc_metadata_file = f"../../../data/processed/{site_name}/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv"
ifc_pointcloud_file = f"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply"

print("Loading data with corrected approach...")
print(f"Drone file: {drone_file}")
print(f"IFC metadata file: {ifc_metadata_file}")
print(f"IFC point cloud file: {ifc_pointcloud_file} (for comparison)")



def load_and_analyze_data():
    # Load point clouds
    drone_points = np.asarray(o3d.io.read_point_cloud(drone_file).points)
    ifc_points = np.asarray(o3d.io.read_point_cloud(ifc_file).points)
    
    print(f"Loaded drone scan: {len(drone_points):,} points")
    print(f"Loaded IFC model: {len(ifc_points):,} points")
    
    # Compute coordinate-only offset
    drone_center = np.mean(drone_points, axis=0)
    ifc_center = np.mean(ifc_points, axis=0)
    full_offset = ifc_center - drone_center
    
    print(f"Coordinate Analysis:")
    print(f"  Full 3D offset: [{full_offset[0]:.2f}, {full_offset[1]:.2f}, {full_offset[2]:.2f}]")
    print(f"  XY component: {np.linalg.norm(full_offset[:2]):.2f}m")
    print(f"  Z component: {full_offset[2]:.2f}m")
    
    return drone_points, ifc_points, full_offset

drone_points, ifc_points, coordinate_offset = load_and_analyze_data()


# Display initial statistics
print("\nINITIAL POINT CLOUD STATISTICS")
print("\nDrone scan (ground truth):")
print(f"  Points: {drone_points.shape[0]:,}")
print(f"  X range: [{drone_points[:, 0].min():.2f}, {drone_points[:, 0].max():.2f}]")
print(f"  Y range: [{drone_points[:, 1].min():.2f}, {drone_points[:, 1].max():.2f}]")
print(f"  Z range: [{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]")

print("\nIFC model (to be aligned):")
print(f"  Points: {ifc_points.shape[0]:,}")
print(f"  X range: [{ifc_points[:, 0].min():.2f}, {ifc_points[:, 0].max():.2f}]")
print(f"  Y range: [{ifc_points[:, 1].min():.2f}, {ifc_points[:, 1].max():.2f}]")
print(f"  Z range: [{ifc_points[:, 2].min():.2f}, {ifc_points[:, 2].max():.2f}]")

# def offset_based_normalization(points):
#     """
#     Subtracts the minimum Z-value from a point cloud to bring it to a Z=0 base frame.
#     Returns the normalized points and the offset applied.
#     """
#     if points.shape[0] == 0:
#         print("Warning: Empty point cloud provided for offset-based normalization")
#         return points, 0.0
    
#     # Find minimum Z-value
#     min_z = np.min(points[:, 2])
    
#     # Subtract min Z from all points
#     normalized_points = points.copy()
#     normalized_points[:, 2] -= min_z
    
#     return normalized_points, min_z

# drone_points, drone_z_offset = offset_based_normalization(drone_points)
# ifc_points, ifc_z_offset = offset_based_normalization(ifc_points)

# print(f"Drone Z-offset applied: {drone_z_offset:.4f}m")
# print(f"IFC Z-offset applied: {ifc_z_offset:.4f}m")

# # Display initial statistics
# print("\nDrone scan (ground truth):")
# print(f"  Points: {drone_points.shape[0]:,}")
# print(f"  X range: [{drone_points[:, 0].min():.2f}, {drone_points[:, 0].max():.2f}]")
# print(f"  Y range: [{drone_points[:, 1].min():.2f}, {drone_points[:, 1].max():.2f}]")
# print(f"  Z range: [{drone_points[:, 2].min():.2f}, {drone_points[:, 2].max():.2f}]")

# print("\nIFC model (to be aligned):")
# print(f"  Points: {ifc_points.shape[0]:,}")
# print(f"  X range: [{ifc_points[:, 0].min():.2f}, {ifc_points[:, 0].max():.2f}]")
# print(f"  Y range: [{ifc_points[:, 1].min():.2f}, {ifc_points[:, 1].max():.2f}]")
# print(f"  Z range: [{ifc_points[:, 2].min():.2f}, {ifc_points[:, 2].max():.2f}]")

# Validate CRS compatibility
def load_crs_metadata(pcd_file):
    """Load CRS metadata with multiple fallback strategies"""
    stem = pcd_file.stem
    base_stem = stem.split("_data_driven")[0] if "_data_driven" in stem else stem
    
    candidates = [
        pcd_file.with_name(f"{stem}_crs.json"),
        pcd_file.with_name(f"{base_stem}_crs.json"),
        pcd_file.parent / "crs_metadata.json",
        pcd_file.parent / f"{base_stem}_crs_metadata.json"
    ]
    
    for crs_file in candidates:
        if crs_file.exists():
            try:
                with open(crs_file) as f:
                    return json.load(f)
            except json.JSONDecodeError:
                print(f"Warning: Failed to parse CRS metadata in {crs_file}")
    return None
print("\n=== CRS VALIDATION ===")
drone_crs = load_crs_metadata(Path(drone_file))
ifc_crs = load_crs_metadata(Path(ifc_pointcloud_file))

drone_epsg = drone_crs.get('epsg') if drone_crs else None
ifc_epsg = ifc_crs.get('epsg') if ifc_crs else None

print(f"Drone CRS: {drone_epsg or 'Not Found'}")
print(f"IFC CRS:   {ifc_epsg or 'Not Found'}")

if drone_epsg and ifc_epsg:
    if drone_epsg != ifc_epsg:
        raise ValueError(f"CRS Mismatch: Drone EPSG {drone_epsg} ≠ IFC EPSG {ifc_epsg}")
    print("CRS match confirmed")
else:
    print("Warning: CRS metadata missing. Proceeding with assumption of matching coordinate systems.")

print(f"\nReady for alignment with {len(drone_points):,} drone + {len(ifc_points):,} IFC points")

def apply_coordinate_alignment(drone_pts, offset):
    # Apply full coordinate offset
    drone_aligned = drone_pts + offset
    
    # Verify perfect coordinate alignment
    aligned_center = np.mean(drone_aligned, axis=0)
    ifc_center = np.mean(ifc_points, axis=0)
    residual_error = np.linalg.norm(aligned_center - ifc_center)
    
    print(f"Coordinate Alignment Results:")
    print(f"  Residual centroid error: {residual_error:.6f}m")
    print(f"  Status: Centroid alignment achieved (translation only)")
    print(f"  Note: This does not guarantee good point-to-point alignment")
    
    return drone_aligned

# def apply_coordinate_alignment(drone_pts, ifc_pts):
#     """
#     Aligns drone and IFC point clouds by matching XY centroids, preserving Z=0 from normalization.
#     Returns the aligned drone points and the XY offset applied.
#     """
#     # Calculate XY centroids
#     drone_center = np.mean(drone_pts[:, :2], axis=0)
#     ifc_center = np.mean(ifc_pts[:, :2], axis=0)
    
#     # Compute XY offset
#     xy_offset = ifc_center - drone_center
    
#     # Apply XY offset, preserve Z
#     drone_aligned = drone_pts.copy()
#     drone_aligned[:, :2] += xy_offset
    
#     # Verify alignment
#     aligned_center = np.mean(drone_aligned[:, :2], axis=0)
#     residual_error = np.linalg.norm(aligned_center - ifc_center)
    
#     print(f"Coordinate Alignment Results:")
#     print(f"  XY offset applied: [{xy_offset[0]:.3f}, {xy_offset[1]:.3f}]m")
#     print(f"  Residual XY centroid error: {residual_error:.6f}m")
#     print(f"  Status: XY centroid alignment achieved, Z preserved at 0")
    
#     return drone_aligned, xy_offset

drone_coordinate_aligned = apply_coordinate_alignment(drone_points, coordinate_offset)
# drone_coordinate_aligned, coordinate_offset = apply_coordinate_alignment(drone_points, ifc_points)

def prepare_xy_icp_test(drone_aligned, ifc_pts):
    # Extract XY coordinates only (set Z=0 for 2D ICP)
    drone_xy = drone_aligned[:, :2]
    ifc_xy = ifc_pts[:, :2]
    
    # Create 2D point clouds (Z=0)
    drone_xy_3d = np.column_stack([drone_xy, np.zeros(len(drone_xy))])
    ifc_xy_3d = np.column_stack([ifc_xy, np.zeros(len(ifc_xy))])
    
    # Create Open3D point clouds
    source_pcd = o3d.geometry.PointCloud()
    source_pcd.points = o3d.utility.Vector3dVector(drone_xy_3d)
    
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(ifc_xy_3d)
    
    # Downsample for performance
    source_pcd = source_pcd.voxel_down_sample(0.15)
    target_pcd = target_pcd.voxel_down_sample(0.15)
    
    # Remove outliers
    source_pcd, _ = source_pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
    target_pcd, _ = target_pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
    
    print(f"XY-only point clouds prepared:")
    print(f"  Source (Drone XY): {len(source_pcd.points):,} points")
    print(f"  Target (IFC XY): {len(target_pcd.points):,} points")
    
    return source_pcd, target_pcd

source_xy_pcd, target_xy_pcd = prepare_xy_icp_test(drone_coordinate_aligned, ifc_points)


def calculate_xy_offset(source_pcd, target_pcd):
    source_center = np.mean(np.asarray(source_pcd.points), axis=0)
    target_center = np.mean(np.asarray(target_pcd.points), axis=0)
    
    xy_offset = target_center[:2] - source_center[:2]  # Only XY components
    xy_offset_magnitude = np.linalg.norm(xy_offset)
    
    print(f"XY Offset Analysis:")
    print(f"  Source center: [{source_center[0]:.3f}, {source_center[1]:.3f}]")
    print(f"  Target center: [{target_center[0]:.3f}, {target_center[1]:.3f}]")
    print(f"  XY offset: [{xy_offset[0]:.3f}, {xy_offset[1]:.3f}]")
    print(f"  XY offset magnitude: {xy_offset_magnitude:.3f}m")
    
    return xy_offset, xy_offset_magnitude

xy_offset, xy_offset_magnitude = calculate_xy_offset(source_xy_pcd, target_xy_pcd)


# See what you're trying to align:
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import numpy as np

# Sample points for visualization
drone_sample = np.asarray(source_xy_pcd.points)[::100]  # Every 100th point
ifc_sample = np.asarray(target_xy_pcd.points)[::50]     # Every 50th point

fig = plt.figure(figsize=(15, 5))

# Side by side view
ax1 = fig.add_subplot(131, projection='3d')
ax1.scatter(drone_sample[:, 0], drone_sample[:, 1], drone_sample[:, 2], 
           c='red', s=1, alpha=0.6, label='Drone')
ax1.set_title('Drone Points')

ax2 = fig.add_subplot(132, projection='3d')
ax2.scatter(ifc_sample[:, 0], ifc_sample[:, 1], ifc_sample[:, 2], 
           c='blue', s=1, alpha=0.6, label='IFC')
ax2.set_title('IFC Points')

# Overlay view
ax3 = fig.add_subplot(133, projection='3d')
ax3.scatter(drone_sample[:, 0], drone_sample[:, 1], drone_sample[:, 2], 
           c='red', s=1, alpha=0.4, label='Drone')
ax3.scatter(ifc_sample[:, 0], ifc_sample[:, 1], ifc_sample[:, 2], 
           c='blue', s=1, alpha=0.4, label='IFC')
ax3.set_title('Overlay')
ax3.legend()

plt.tight_layout()
plt.show()


def test_xy_icp_refinement(source_pcd, target_pcd):
    print("=== TESTING XY-ONLY ICP REFINEMENT ===")
    
    # Run 2D ICP (point-to-point for 2D data)
    icp_result = o3d.pipelines.registration.registration_icp(
        source_pcd, target_pcd,
        max_correspondence_distance=max_correspondence_distance,
        init=np.eye(4),  # Start from aligned position
        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(),
        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=50)
    )
    
    # Extract XY refinement (ignore any Z movement)
    xy_refinement = icp_result.transformation[:2, 3]
    xy_refinement_magnitude = np.linalg.norm(xy_refinement)
    
    print(f"XY ICP Results:")
    print(f"  Fitness: {icp_result.fitness:.6f}")
    print(f"  RMSE: {icp_result.inlier_rmse:.6f}")
    print(f"  XY refinement: [{xy_refinement[0]:.3f}, {xy_refinement[1]:.3f}]")
    print(f"  Refinement magnitude: {xy_refinement_magnitude:.3f}m")
    
    return xy_refinement, xy_refinement_magnitude, icp_result.fitness

xy_refinement, xy_refinement_mag, xy_fitness = test_xy_icp_refinement(source_xy_pcd, target_xy_pcd)


def apply_hybrid_alignment(drone_aligned, xy_refinement, xy_magnitude):
    print("=== HYBRID ALIGNMENT DECISION ===")
    
    if xy_magnitude < xy_refinement_threshold:
        print(f"ACCEPTING XY refinement: {xy_magnitude:.3f}m < {xy_refinement_threshold}m threshold")
        
        # Apply XY refinement while preserving Z from coordinate alignment
        drone_hybrid = drone_aligned.copy()
        drone_hybrid[:, 0] += xy_refinement[0]  # Refine X
        drone_hybrid[:, 1] += xy_refinement[1]  # Refine Y
        # Z stays from coordinate-only alignment
        
        method_used = "hybrid_coordinate_xy"
        print(f"Applied hybrid transformation: XY refinement + coordinate Z")
        
    else:
        print(f"REJECTING XY refinement: {xy_magnitude:.3f}m > {xy_refinement_threshold}m threshold")
        print(f"Using coordinate-only alignment (more reliable)")
        
        drone_hybrid = drone_aligned.copy()
        method_used = "coordinate_only"
    
    return drone_hybrid, method_used

drone_final, alignment_method = apply_hybrid_alignment(drone_coordinate_aligned, xy_refinement, xy_refinement_mag)


def evaluate_final_alignment(drone_final, ifc_pts, method):
    # Calculate final centroid separation
    final_drone_center = np.mean(drone_final, axis=0)
    final_ifc_center = np.mean(ifc_pts, axis=0)
    final_separation = np.linalg.norm(final_drone_center - final_ifc_center)
    
    # Quality assessment with sampling
    # sample_size = 5000
    # drone_sample = drone_final[np.random.choice(len(drone_final), min(sample_size, len(drone_final)), replace=False)]
    # ifc_sample = ifc_pts[np.random.choice(len(ifc_pts), min(sample_size, len(ifc_pts)), replace=False)]
    
    np.random.seed(RANDOM_SEED)  # Ensure reproducible sampling
    
    # Use same sample size across all methods
    n_drone = min(STANDARD_SAMPLE_SIZE, len(drone_final))
    n_ifc = min(STANDARD_SAMPLE_SIZE, len(ifc_pts))
    
    # Fixed sampling indices for reproducibility
    drone_indices = np.random.choice(len(drone_final), n_drone, replace=False)
    ifc_indices = np.random.choice(len(ifc_pts), n_ifc, replace=False)

    drone_sample = drone_final[drone_indices]
    ifc_sample = ifc_pts[ifc_indices]

    # Nearest neighbor distances
    tree = cKDTree(ifc_sample)
    distances, _ = tree.query(drone_sample)
    
    # Calculate metrics
    rmse = np.sqrt(np.mean(distances**2))
    median_dist = np.median(distances)
    good_pct = np.sum(distances < 2.0) / len(distances) * 100
    
    print(f"=== FINAL ALIGNMENT QUALITY ===")
    print(f"Method used: {method}")
    print(f"Final centroid separation: {final_separation:.6f}m")
    print(f"RMSE: {rmse:.2f}m")
    print(f"Median distance: {median_dist:.2f}m")
    print(f"Good points (<2m): {good_pct:.1f}%")
    
    # Add quality assessment warnings
    if rmse > 10.0 and good_pct < 20.0:
        print(f"\n⚠️  WARNING: Poor alignment quality detected!")
        print(f"   RMSE {rmse:.1f}m is very high, only {good_pct:.1f}% points well-aligned")
        print(f"   Consider: rotation correction, scale adjustment, or different method")
    elif rmse > 5.0:
        print(f"\n⚠️  CAUTION: Moderate alignment issues detected")
        print(f"   RMSE {rmse:.1f}m suggests structural misalignment")
    else:
        print(f"\n✅ Good alignment quality achieved")
    
    return {
        'method': method,
        'final_separation': final_separation,
        'rmse': rmse,
        'median_distance': median_dist,
        'good_pct': good_pct,
        'xy_refinement_applied': method == "hybrid_coordinate_xy",
        'xy_refinement_magnitude': xy_refinement_mag if method == "hybrid_coordinate_xy" else 0.0
    }

final_results = evaluate_final_alignment(drone_final, ifc_points, alignment_method)

def compare_alignment_approaches():
    print("=== APPROACH COMPARISON ===")
    
    # Coordinate-only (perfect centroid alignment by definition)
    coord_only_error = 0.000
    
    # Current results
    current_rmse = final_results['rmse']
    current_separation = final_results['final_separation']
    
    print(f"Coordinate-only centroid error: {coord_only_error:.3f}m")
    print(f"Hybrid approach centroid error: {current_separation:.6f}m")
    print(f"Hybrid approach RMSE: {current_rmse:.2f}m")
    
    if final_results['xy_refinement_applied']:
        print(f"XY refinement of {xy_refinement_mag:.3f}m was applied")
        if current_separation < 0.01:
            assessment = "SUCCESSFUL REFINEMENT"
        else:
            assessment = "REFINEMENT WITH TRADE-OFFS"
    else:
        print(f"XY refinement of {xy_refinement_mag:.3f}m was rejected (too large)")
        assessment = "COORDINATE-ONLY PREFERRED"
    
    print(f"Assessment: {assessment}")
    
    # Add overall quality assessment based on RMSE
    print(f"\n=== OVERALL ALIGNMENT QUALITY ===")
    if current_rmse > 20.0:
        print(f"POOR: RMSE {current_rmse:.1f}m indicates significant alignment failure")
        print(f"   Recommendation: Try different alignment method or check coordinate systems")
    elif current_rmse > 10.0:
        print(f"MODERATE: RMSE {current_rmse:.1f}m shows alignment issues")
        print(f"   Recommendation: Consider rotation/scale correction")
    elif current_rmse > 5.0:
        print(f"ACCEPTABLE: RMSE {current_rmse:.1f}m is usable but could be improved")
    else:
        print(f"GOOD: RMSE {current_rmse:.1f}m shows successful alignment")
    
    return assessment

comparison_result = compare_alignment_approaches()


if save_results:
    output_path = Path(output_dir) / ground_method
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save final aligned point cloud
    final_pcd = o3d.geometry.PointCloud()
    final_pcd.points = o3d.utility.Vector3dVector(drone_final)
    aligned_file = output_path / f"{site_name}_hybrid_aligned.ply"
    o3d.io.write_point_cloud(str(aligned_file), final_pcd)
    
    # Save comprehensive results
    results_data = {
        'coordinate_offset': coordinate_offset.tolist(),
        'xy_offset_after_downsampling': xy_offset.tolist(),
        'xy_refinement_proposed': xy_refinement.tolist(),
        'xy_refinement_magnitude': xy_refinement_mag,
        'xy_fitness': xy_fitness,
        'refinement_threshold': xy_refinement_threshold,
        'final_results': final_results,
        'comparison_assessment': comparison_result,
        'decision_logic': {
            'xy_magnitude_vs_threshold': f"{xy_refinement_mag:.3f}m vs {xy_refinement_threshold}m",
            'decision': "ACCEPT" if final_results['xy_refinement_applied'] else "REJECT",
            'reason': "Small refinement accepted" if final_results['xy_refinement_applied'] 
                     else "Large refinement rejected - coordinate-only more reliable"
        }
    }
    
    with open(output_path / f"{site_name}_hybrid_alignment_results.json", 'w') as f:
        json.dump(results_data, f, indent=2)
    
    print(f"Results saved to: {output_path}")

# Quick diagnostic for poor alignment
if final_results['rmse'] > 10.0:
    print("\n=== ALIGNMENT DIAGNOSTIC ===")
    drone_bounds = np.ptp(drone_final, axis=0)
    ifc_bounds = np.ptp(ifc_points, axis=0)
    scale_ratios = drone_bounds / ifc_bounds
    print(f"Drone extent: [{drone_bounds[0]:.1f}, {drone_bounds[1]:.1f}, {drone_bounds[2]:.1f}]m")
    print(f"IFC extent:   [{ifc_bounds[0]:.1f}, {ifc_bounds[1]:.1f}, {ifc_bounds[2]:.1f}]m")
    print(f"Scale ratios: [{scale_ratios[0]:.2f}, {scale_ratios[1]:.2f}, {scale_ratios[2]:.2f}]")
    if np.any(scale_ratios > 1.5) or np.any(scale_ratios < 0.67):
        print(" Scale mismatch detected - consider scale correction")
    print(f"Possible issues: rotation, scale, coordinate system, or coverage mismatch")

print("\n=== HYBRID XY-COORDINATE ALIGNMENT COMPLETE ===")
print(f"Final method: {alignment_method}")
print(f"Final RMSE: {final_results['rmse']:.2f}m")
print(f"Assessment: {comparison_result}")


def fair_comparison_evaluation(drone_aligned, ifc_pts, method_name):
    """Fair comparison with identical evaluation methodology"""
    
    # Use large consistent sample
    SAMPLE_SIZE = 20000
    np.random.seed(42)  # Fixed seed
    
    # Consistent sampling
    drone_sample = drone_aligned[np.random.choice(len(drone_aligned), 
                                                 min(SAMPLE_SIZE, len(drone_aligned)), 
                                                 replace=False)]
    ifc_sample = ifc_pts[np.random.choice(len(ifc_pts), 
                                         min(SAMPLE_SIZE, len(ifc_pts)), 
                                         replace=False)]
    
    # Standard evaluation
    tree = cKDTree(ifc_sample)
    distances, _ = tree.query(drone_sample)
    
    results = {
        'method': method_name,
        'rmse': np.sqrt(np.mean(distances**2)),
        'median_distance': np.median(distances),
        'sample_size': len(drone_sample),
        'evaluation': 'standardized_fair_comparison'
    }
    
    print(f"=== STANDARDIZED EVALUATION: {method_name.upper()} ===")
    print(f"RMSE: {results['rmse']:.2f}m")
    print(f"Sample size: {results['sample_size']:,} points")
    
    return results

fair_comparison_results = fair_comparison_evaluation(drone_final, ifc_points, "hybrid_xy_coordinate")
print(fair_comparison_results)