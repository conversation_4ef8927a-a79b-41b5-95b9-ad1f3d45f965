# Imports and Setup
import numpy as np
import cv2
import open3d as o3d
import matplotlib.pyplot as plt
from sklearn.cluster import DBSCAN
from scipy.spatial import cKDTree, distance, ConvexHull
import pandas as pd
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Parameters (Papermill)
ground_method = "ransac_pmf"  # Ground segmentation method
site_name = "trino_enel"
output_dir = "../../../data/processed/coordinate_alignment_corrected"
save_results = True
quality_sample_size = 5000  # For quality assessment sampling

# Define file paths
drone_file = f"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply"
ifc_metadata_file = f"../../../data/processed/{site_name}/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv"
ifc_pointcloud_file = f"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply"  # For comparison

print("Loading data with corrected approach...")
print(f"Drone file: {drone_file}")
print(f"IFC metadata file: {ifc_metadata_file}")
print(f"IFC point cloud file: {ifc_pointcloud_file} (for comparison)")



# Load IFC point cloud
ifc_pcd = o3d.io.read_point_cloud(ifc_pointcloud_file)
ifc_points = np.asarray(ifc_pcd.points)

# Match drone and IFC points using nearest neighbor search
def find_common_points(drone_pts, ifc_pts, max_dist=0.2):
    tree = cKDTree(ifc_pts)
    dists, indices = tree.query(drone_pts)
    mask = dists < max_dist
    matched_drone = drone_pts[mask]
    matched_ifc = ifc_pts[indices[mask]]
    return matched_drone, matched_ifc

# Filter mutually close points as GCP candidates
matched_drone, matched_ifc = find_common_points(drone_points, ifc_points, max_dist=0.2)
gcp_candidates = matched_drone  # Now these are near-IFC points


# Load drone point cloud (replace with your path or method)
pcd = o3d.io.read_point_cloud(drone_file)  # or .las, etc.
drone_points = np.asarray(pcd.points)

# Quick visualization (XY colored by Z)
plt.figure(figsize=(10, 8))
plt.scatter(drone_points[:, 0], drone_points[:, 1], c=drone_points[:, 2], s=0.5, cmap='terrain')
plt.colorbar(label="Height (Z)")
plt.title("Drone Point Cloud - XY View")
plt.xlabel("Easting")
plt.ylabel("Northing")
plt.axis("equal")
plt.show()


import numpy as np
import matplotlib.pyplot as plt

# Example input point cloud: drone_points
# Let's assume you already have this variable defined, shape: (N, 3)
# drone_points = np.load("your_loaded_pointcloud.npy") or similar

# Step 1: Select GCP candidates (random sampling from drone point cloud)
# num_candidates = 1000
# indices = np.random.choice(drone_points.shape[0], size=num_candidates, replace=False)
# gcp_candidates = drone_points[indices]

gcp_candidates = detect_gcp_candidates(drone_points)

# Step 2: Select well-spread GCPs using your greedy farthest-point selection
def select_well_spread_points(candidates, target_count=12):
    selected = []
    if len(candidates) == 0:
        return np.array([])
    
    idx = np.random.choice(len(candidates))
    selected.append(candidates[idx])
    remaining = list(range(len(candidates)))
    remaining.remove(idx)

    while len(selected) < target_count and remaining:
        max_dist = -1
        best_idx = None
        for idx in remaining:
            point = candidates[idx]
            dists = [np.linalg.norm(point[:2] - sel[:2]) for sel in selected]  # 2D distance (E/N)
            min_dist = min(dists)
            if min_dist > max_dist:
                max_dist = min_dist
                best_idx = idx
        selected.append(candidates[best_idx])
        remaining.remove(best_idx)
    return np.array(selected)

# Step 3: Final selection
selected_gcps = select_well_spread_points(gcp_candidates, target_count=12)

# Step 4: Print selected GCPs
for i, gcp in enumerate(selected_gcps):
    print(f"GCP {i+1:02d}: E={gcp[0]:.2f}, N={gcp[1]:.2f}, H={gcp[2]:.2f}")

# Step 5: Visualize
plt.figure(figsize=(10, 8))
plt.scatter(drone_points[:, 0], drone_points[:, 1], c=drone_points[:, 2], s=0.5, cmap='terrain', alpha=0.4)
plt.scatter(selected_gcps[:, 0], selected_gcps[:, 1], c='lime', marker='*', s=200, label='Final GCPs')
for i, gcp in enumerate(selected_gcps):
    plt.annotate(f'GCP{i+1}', (gcp[0], gcp[1]), textcoords='offset points', xytext=(5, 5), fontsize=9, color='green')
plt.legend()
plt.title("Final Selected GCPs")
plt.axis("equal")
plt.show()


def detect_gcp_candidates(drone_points):
    """
    Combine simple corner + high point + edge detection to propose GCP candidates
    """
    candidates = []

    # Height-based filtering
    z_thresh = np.percentile(drone_points[:, 2], 95)
    high_points = drone_points[drone_points[:, 2] > z_thresh]
    
    if len(high_points) > 0:
        clustering = DBSCAN(eps=5, min_samples=3).fit(high_points[:, :2])
        for cluster_id in np.unique(clustering.labels_):
            if cluster_id != -1:
                points = high_points[clustering.labels_ == cluster_id]
                centroid = np.mean(points, axis=0)
                candidates.append(centroid)

    # Optional: Add corners/edges later if needed
    return np.array(candidates)

# Run detection
gcp_candidates = detect_gcp_candidates(drone_points)

# Visualize candidates
plt.figure(figsize=(10, 8))
plt.scatter(drone_points[:, 0], drone_points[:, 1], c=drone_points[:, 2], s=0.5, cmap='terrain', alpha=0.4)
plt.scatter(gcp_candidates[:, 0], gcp_candidates[:, 1], c='red', marker='x', s=100, label='GCP Candidates')
plt.legend()
plt.title(f"Detected {len(gcp_candidates)} GCP Candidates")
plt.axis("equal")
plt.show()


gcp_df = pd.DataFrame({
    'GCP_ID': [f'GCP_{i+1:02d}' for i in range(len(selected_gcps))],
    'Easting': selected_gcps[:, 0],
    'Northing': selected_gcps[:, 1],
    'Height': selected_gcps[:, 2],
    'Source': 'Auto'
})

print("\n=== GCP Data ===")
print(gcp_df.to_string(index=False))

# Save to CSV
gcp_df.to_csv("selected_gcps.csv", index=False)


# %%
# ======================
# 📦 Imports and Setup
# ======================
import numpy as np
import open3d as o3d
import matplotlib.pyplot as plt
from scipy.spatial import cKDTree
import pandas as pd

# %%
# ======================
# 📁 Parameters & Paths
# ======================
ground_method = "ransac_pmf"
site_name = "trino_enel"
output_dir = "../../../data/processed/coordinate_alignment_corrected"
save_results = True

# File paths
drone_file = f"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply"
ifc_pointcloud_file = f"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply"

print("Loading data...")
print(f"📄 Drone point cloud: {drone_file}")
print(f"📄 IFC point cloud: {ifc_pointcloud_file}")

# %%
# ======================
# ☁️ Load Point Clouds
# ======================
pcd_drone = o3d.io.read_point_cloud(drone_file)
drone_points = np.asarray(pcd_drone.points)

pcd_ifc = o3d.io.read_point_cloud(ifc_pointcloud_file)
ifc_points = np.asarray(pcd_ifc.points)

# %%
# ==========================
# 🧠 Match Common GCP Points
# ==========================
def find_common_points(drone_pts, ifc_pts, max_dist=0.2):
    tree = cKDTree(ifc_pts)
    dists, indices = tree.query(drone_pts)
    mask = dists < max_dist
    matched_drone = drone_pts[mask]
    matched_ifc = ifc_pts[indices[mask]]
    return matched_drone, matched_ifc

# ==========================
# 🧠 Align & Match GCP Points
# ==========================

# Coarse alignment: shift drone cloud to IFC centroid
offset = np.mean(ifc_points, axis=0) - np.mean(drone_points, axis=0)
drone_points_aligned = drone_points + offset

# Now run nearest-neighbor search with aligned drone
def find_common_points(drone_pts, ifc_pts, max_dist=0.2):
    tree = cKDTree(ifc_pts)
    dists, indices = tree.query(drone_pts)
    mask = dists < max_dist
    matched_drone = drone_pts[mask]
    matched_ifc = ifc_pts[indices[mask]]
    return matched_drone, matched_ifc

matched_drone, matched_ifc = find_common_points(drone_points_aligned, ifc_points, max_dist=0.2)
gcp_candidates = matched_drone

print(f"✅ Found {len(gcp_candidates)} common GCP candidates.")


# %%
# ==============================================
# 📌 Select Well-Spread GCPs from Drone Matches
# ==============================================
def select_well_spread_points(candidates, target_count=12):
    selected = []
    if len(candidates) == 0:
        return np.array([])

    idx = np.random.choice(len(candidates))
    selected.append(candidates[idx])
    remaining = list(range(len(candidates)))
    remaining.remove(idx)

    while len(selected) < target_count and remaining:
        max_dist = -1
        best_idx = None
        for idx in remaining:
            point = candidates[idx]
            dists = [np.linalg.norm(point[:2] - sel[:2]) for sel in selected]
            min_dist = min(dists)
            if min_dist > max_dist:
                max_dist = min_dist
                best_idx = idx
        selected.append(candidates[best_idx])
        remaining.remove(best_idx)
    return np.array(selected)

selected_gcps = select_well_spread_points(gcp_candidates, target_count=12)

# %%
# ======================
# 🖼️ Visualize Results
# ======================
plt.figure(figsize=(10, 8))
plt.scatter(drone_points[:, 0], drone_points[:, 1], c=drone_points[:, 2], s=0.5, cmap='terrain', alpha=0.4)
plt.scatter(selected_gcps[:, 0], selected_gcps[:, 1], c='lime', marker='*', s=200, label='Final GCPs')
for i, gcp in enumerate(selected_gcps):
    plt.annotate(f'GCP{i+1}', (gcp[0], gcp[1]), textcoords='offset points', xytext=(5, 5), fontsize=9, color='green')
plt.legend()
plt.title("Final Selected GCPs (in Drone Point Cloud)")
plt.xlabel("Easting")
plt.ylabel("Northing")
plt.axis("equal")
plt.colorbar(label="Height (Z)")
plt.show()

# %%
# ======================
# 💾 Save to CSV
# ======================
gcp_df = pd.DataFrame({
    'GCP_ID': [f'GCP_{i+1:02d}' for i in range(len(selected_gcps))],
    'Easting': selected_gcps[:, 0],
    'Northing': selected_gcps[:, 1],
    'Height': selected_gcps[:, 2],
    'Source': 'Auto-Matched'
})

print("\n=== GCP Data ===")
print(gcp_df.to_string(index=False))

gcp_df.to_csv("selected_gcps.csv", index=False)
print("✅ Saved selected_gcps.csv")

# %%
# Optional: Save IFC-side match for validation
selected_ifc_gcps = find_common_points(selected_gcps, ifc_points, max_dist=0.2)[1]


import pandas as pd
import numpy as np
from typing import Tuple, Dict, Any
from scipy.spatial import cKDTree

def load_ground_control_points_from_csv(filepath: str = "selected_gcps.csv") -> Tuple[pd.DataFrame, np.ndarray]:
    """Load GCPs from CSV with Easting, Northing, and optional Height"""
    df = pd.read_csv(filepath)

    # Fallback if Height missing
    if 'Height' not in df.columns:
        print("[WARNING] 'Height' column missing. Assuming Z=0.")
        df['Height'] = 0.0

    required_cols = {'Easting', 'Northing', 'Height'}
    if not required_cols.issubset(df.columns):
        raise ValueError(f"CSV must contain columns: {required_cols}")

    coords = df[['Easting', 'Northing', 'Height']].values
    return df, coords

def find_corresponding_drone_points(
    drone_points: np.ndarray,
    ifc_gcp_coords: np.ndarray,
    search_radius: float = 10.0
) -> Tuple[np.ndarray, list]:
    """
    For each IFC GCP, find the closest drone point within a radius.
    Matching based on XY, with median-Z selection among nearby points.
    """
    tree = cKDTree(drone_points[:, :2])  # 2D search

    matched_drone_coords = []
    valid_pairs = []

    for i, gcp in enumerate(ifc_gcp_coords):
        indices = tree.query_ball_point(gcp[:2], search_radius)
        if indices:
            candidates = drone_points[indices]
            # Sort candidates by proximity in XY, break tie using Z diff
            dists = np.linalg.norm(candidates[:, :2] - gcp[:2], axis=1)
            best_idx = np.argmin(dists)
            best_match = candidates[best_idx]

            matched_drone_coords.append(best_match)
            valid_pairs.append(i)

            print(f"GCP {i}: IFC {gcp[:2]} -> Drone {best_match[:2]} (Z: {best_match[2]:.2f})")
        else:
            print(f"GCP {i}: No match within {search_radius}m")

    return np.array(matched_drone_coords), valid_pairs

def compute_similarity_transform(
    source_points: np.ndarray,
    target_points: np.ndarray
) -> Tuple[np.ndarray, np.ndarray, float]:
    """
    Compute similarity (rigid + scale) transformation using Procrustes analysis.
    """
    source_centroid = np.mean(source_points, axis=0)
    target_centroid = np.mean(target_points, axis=0)

    source_centered = source_points - source_centroid
    target_centered = target_points - target_centroid

    source_scale = np.linalg.norm(source_centered)
    target_scale = np.linalg.norm(target_centered)
    scale = target_scale / source_scale if source_scale > 0 else 1.0

    H = (source_centered / source_scale).T @ (target_centered / target_scale)
    U, _, Vt = np.linalg.svd(H)
    R = Vt.T @ U.T

    # Ensure a proper rotation (det(R)=1)
    if np.linalg.det(R) < 0:
        Vt[-1] *= -1
        R = Vt.T @ U.T

    translation = target_centroid - scale * (R @ source_centroid)
    return R, translation, scale

def apply_similarity_transform(
    points: np.ndarray,
    R: np.ndarray,
    translation: np.ndarray,
    scale: float
) -> np.ndarray:
    """Apply transformation (scale + rotation + translation)."""
    return scale * (points @ R.T) + translation

def gcp_based_alignment(
    drone_points: np.ndarray,
    gcp_csv_path: str = "selected_gcps.csv",
    output_residuals_csv: str = "gcp_alignment_residuals.csv"
) -> Tuple[np.ndarray, Dict[str, Any]]:
    """
    Align drone points to IFC ground control points using similarity transformation.
    """
    print("=== GCP-BASED ALIGNMENT START ===")

    gcp_df, ifc_gcp_coords = load_ground_control_points_from_csv(gcp_csv_path)
    print(f"Loaded {len(ifc_gcp_coords)} GCPs from {gcp_csv_path}")

    print("\nMatching drone points...")
    drone_gcp_coords, valid_indices = find_corresponding_drone_points(
        drone_points, ifc_gcp_coords, search_radius=10.0
    )

    if len(valid_indices) < 3:
        print("❌ ERROR: At least 3 valid GCP matches required.")
        return None, {}

    ifc_gcp_valid = ifc_gcp_coords[valid_indices]

    print("\nComputing transformation...")
    R, T, S = compute_similarity_transform(drone_gcp_coords, ifc_gcp_valid)

    print(f"\nTransform Parameters:\nScale: {S:.6f}\nTranslation: {T}\nRotation:\n{R}")

    drone_aligned = apply_similarity_transform(drone_points, R, T, S)
    drone_gcp_transformed = apply_similarity_transform(drone_gcp_coords, R, T, S)

    residuals = np.linalg.norm(drone_gcp_transformed - ifc_gcp_valid, axis=1)

    print("\nGCP Alignment Residuals:")
    print(f"  Mean: {np.mean(residuals):.3f} m")
    print(f"  Max : {np.max(residuals):.3f} m")
    print(f"  RMS : {np.sqrt(np.mean(residuals**2)):.3f} m")

    # Save residuals per GCP
    gcp_df = gcp_df.iloc[valid_indices].copy()
    gcp_df['Residual_m'] = residuals
    gcp_df.to_csv(output_residuals_csv, index=False)
    print(f"✅ Residuals saved to: {output_residuals_csv}")

    return drone_aligned, {
        "rotation_matrix": R,
        "translation": T,
        "scale": S,
        "gcp_residuals": residuals,
        "rms_error": np.sqrt(np.mean(residuals**2)),
        "gcp_df": gcp_df
    }

# Usage:
aligned_cloud, transform_info = gcp_based_alignment(drone_points, "selected_gcps.csv")


import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

def analyze_gcp_residuals(gcp_df, drone_gcp_coords, ifc_gcp_coords, residuals):
    """Analyze GCP alignment quality and identify problematic points"""
    
    print("=== GCP ALIGNMENT ANALYSIS ===")
    
    # Create detailed analysis DataFrame
    analysis_df = pd.DataFrame({
        'GCP_ID': range(len(residuals)),
        'Location': gcp_df['Location'][:len(residuals)],
        'Drone_X': drone_gcp_coords[:, 0],
        'Drone_Y': drone_gcp_coords[:, 1], 
        'Drone_Z': drone_gcp_coords[:, 2],
        'IFC_X': ifc_gcp_coords[:, 0],
        'IFC_Y': ifc_gcp_coords[:, 1],
        'IFC_Z': ifc_gcp_coords[:, 2],
        'Residual_m': residuals,
        'XY_Error': np.linalg.norm(drone_gcp_coords[:, :2] - ifc_gcp_coords[:, :2], axis=1)
    })
    
    print(f"\nGCP Residual Statistics:")
    print(f"Mean: {np.mean(residuals):.2f}m")
    print(f"Median: {np.median(residuals):.2f}m") 
    print(f"Std Dev: {np.std(residuals):.2f}m")
    print(f"95th percentile: {np.percentile(residuals, 95):.2f}m")
    
    # Identify problematic points
    threshold = np.mean(residuals) + 2 * np.std(residuals)
    outliers = analysis_df[analysis_df['Residual_m'] > threshold]
    
    print(f"\nProblematic GCPs (residual > {threshold:.2f}m):")
    for _, row in outliers.iterrows():
        print(f"  GCP {row['GCP_ID']} ({row['Location']}): {row['Residual_m']:.2f}m error")
    
    # Accuracy by location
    print(f"\nAccuracy by Location:")
    location_stats = analysis_df.groupby('Location')['Residual_m'].agg(['mean', 'max', 'count'])
    print(location_stats)
    
    return analysis_df

def compare_alignment_methods(centroid_rmse=28.57, gcp_rmse=5.101):
    """Compare the two alignment methods"""
    
    print(f"\n=== ALIGNMENT METHOD COMPARISON ===")
    
    methods = ['Centroid-Only', 'GCP-Based']
    rmse_values = [centroid_rmse, gcp_rmse]
    improvement = ((centroid_rmse - gcp_rmse) / centroid_rmse) * 100
    
    print(f"Centroid-Only Alignment: {centroid_rmse:.2f}m RMSE")
    print(f"GCP-Based Alignment:     {gcp_rmse:.2f}m RMSE")
    print(f"Improvement:             {improvement:.1f}% reduction in error")
    
    # Construction monitoring implications
    print(f"\n=== CONSTRUCTION MONITORING IMPACT ===")
    
    accuracy_levels = {
        'Survey Grade': 0.1,
        'Construction Layout': 0.5, 
        'Quality Control': 2.0,
        'Progress Monitoring': 5.0,
        'General Assessment': 10.0
    }
    
    print(f"Suitability for different applications:")
    for application, required_accuracy in accuracy_levels.items():
        centroid_suitable = "✅" if centroid_rmse <= required_accuracy else "❌"
        gcp_suitable = "✅" if gcp_rmse <= required_accuracy else "❌"
        print(f"  {application:20} (≤{required_accuracy:4.1f}m): Centroid {centroid_suitable} | GCP {gcp_suitable}")

def estimate_pile_detection_accuracy(gcp_rmse=5.101):
    """Estimate how this affects pile detection accuracy"""
    
    print(f"\n=== PILE DETECTION IMPACT ===")
    
    # Typical pile spacing and tolerances
    pile_spacing = 3.0  # meters between piles
    pile_diameter = 0.3  # meters
    
    print(f"With {gcp_rmse:.1f}m alignment accuracy:")
    print(f"  • Pile detection reliability: {'Good' if gcp_rmse < pile_spacing else 'Poor'}")
    print(f"  • Individual pile positioning: {'Adequate' if gcp_rmse < pile_spacing/2 else 'Challenging'}")
    print(f"  • Quality control capability: {'Suitable' if gcp_rmse < 2*pile_diameter else 'Limited'}")
    
    # Expected classification accuracy
    classification_confidence = max(0, min(100, (pile_spacing - gcp_rmse) / pile_spacing * 100))
    print(f"  • Expected classification confidence: {classification_confidence:.1f}%")

def recommend_next_steps(gcp_rmse=5.101):
    """Provide recommendations for further improvement"""
    
    print(f"\n=== RECOMMENDATIONS ===")
    
    if gcp_rmse > 2.0:
        print("🔧 Further Improvements Recommended:")
        print("  1. Check GCP identification accuracy")
        print("  2. Consider surveying actual GCP coordinates")
        print("  3. Add more GCPs in problem areas")
        print("  4. Use sub-meter GPS for GCP positioning")
        
    if gcp_rmse <= 5.0:
        print("✅ Current Accuracy Suitable For:")
        print("  • Construction progress monitoring")
        print("  • General quality assessment") 
        print("  • Foundation layout verification")
        
    if gcp_rmse <= 2.0:
        print("🎯 High Accuracy Achieved - Suitable For:")
        print("  • Detailed construction quality control")
        print("  • Precise pile positioning verification")
        print("  • Compliance documentation")

# Run the analysis
print("Running GCP alignment analysis...")

# Note: In actual implementation, you would pass the real data:
# analysis_df = analyze_gcp_residuals(gcp_df, drone_gcp_coords, ifc_gcp_coords, residuals)

compare_alignment_methods()
estimate_pile_detection_accuracy()
recommend_next_steps()