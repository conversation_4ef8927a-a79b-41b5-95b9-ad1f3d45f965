{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Control Point (GCP) Based Alignment with Z-Correction\n", "\n", "This notebook implements GCP-based alignment using manually identified ground control points to achieve more accurate alignment between drone and IFC point clouds, with special attention to Z-coordinate alignment.\n", "\n", "**Key Features:**\n", "- Manual GCP identification and matching\n", "- Proper Z-coordinate handling (IFC absolute vs drone relative elevations)\n", "- Similarity transformation (rotation, translation, scale)\n", "- Quality assessment with residual analysis\n", "- Comparison with coordinate-only alignment\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path\n", "\n", "# Parameters (Papermill)\n", "ground_method = \"ransac_pmf\"  # Ground segmentation method\n", "site_name = \"trino_enel\"\n", "save_results = True\n", "quality_sample_size = 5000  # For quality assessment sampling\n", "\n", "# Option to use detected coordinates instead of manual ones\n", "use_detected_gcp = False  # Set to True to use automated detection results"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== GROUND CONTROL POINT BASED ALIGNMENT WITH Z-CORRECTION ===\n", "Ground method: ransac_pmf\n", "Site: trino_enel\n", "Output directory: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment_z_corrected\n", "Using GCP-based similarity transformation with Z-correction\n"]}], "source": ["# Imports\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import laspy\n", "from pathlib import Path\n", "import json\n", "from scipy.spatial import cKDTree\n", "from scipy.spatial.transform import Rotation\n", "from scipy.optimize import least_squares\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "\n", "# Setup paths using shared config\n", "output_dir = get_processed_data_path(site_name, \"gcp_alignment_z_corrected\")\n", "\n", "print(\"=== GROUND CONTROL POINT BASED ALIGNMENT WITH Z-CORRECTION ===\")\n", "print(f\"Ground method: {ground_method}\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Output directory: {output_dir}\")\n", "print(f\"Using GCP-based similarity transformation with Z-correction\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading data for GCP-based alignment...\n", "Drone file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n", "IFC metadata file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "IFC point cloud file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n"]}], "source": ["# Define file paths using shared config\n", "ground_seg_path = get_processed_data_path(site_name, \"ground_segmentation\") / ground_method\n", "ifc_metadata_path = get_processed_data_path(site_name, \"ifc_metadata\")\n", "ifc_pointcloud_path = get_processed_data_path(site_name, \"ifc_pointclouds\")\n", "\n", "# Find the actual files\n", "drone_file = find_latest_file(ground_seg_path, f\"{site_name}_nonground.ply\")\n", "ifc_metadata_file = find_latest_file(ifc_metadata_path, \"*enhanced_metadata.csv\")\n", "ifc_pointcloud_file = find_latest_file(ifc_pointcloud_path, \"*data_driven.ply\")\n", "\n", "print(\"Loading data for GCP-based alignment...\")\n", "print(f\"Drone file: {drone_file}\")\n", "print(f\"IFC metadata file: {ifc_metadata_file}\")\n", "print(f\"IFC point cloud file: {ifc_pointcloud_file}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded drone scan: 517,002 points\n"]}], "source": ["def load_drone_points(drone_path):\n", "    \"\"\"Load drone point cloud\"\"\"\n", "    drone_file = Path(drone_path)\n", "    \n", "    if not drone_file.exists():\n", "        raise FileNotFoundError(f\"Drone file not found: {drone_path}\")\n", "    \n", "    if drone_file.suffix.lower() == \".las\":\n", "        drone_las = laspy.read(drone_file)\n", "        drone_points = drone_las.xyz\n", "    elif drone_file.suffix.lower() == \".ply\":\n", "        drone_pcd = o3d.io.read_point_cloud(str(drone_file))\n", "        drone_points = np.asarray(drone_pcd.points)\n", "    else:\n", "        raise ValueError(\"Unsupported drone file format. Use .las or .ply\")\n", "    \n", "    print(f\"Loaded drone scan: {drone_points.shape[0]:,} points\")\n", "    return drone_points\n", "\n", "# Load data\n", "drone_points = load_drone_points(drone_file)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading IFC metadata coordinates...\n", "Loaded IFC metadata: 14,460 records\n", "Valid IFC coordinates: 14,460 points\n", "Coordinate ranges:\n", "  X: 435267.20 to 436719.95\n", "  Y: 5010900.71 to 5012462.41\n", "  Z: 154.99 to 159.52\n"]}], "source": ["def load_ifc_points_from_metadata(metadata_csv_path):\n", "    \"\"\"Load IFC coordinates from metadata CSV\"\"\"\n", "    metadata_file = Path(metadata_csv_path)\n", "    \n", "    if not metadata_file.exists():\n", "        raise FileNotFoundError(f\"IFC metadata file not found: {metadata_csv_path}\")\n", "    \n", "    # Load metadata\n", "    df = pd.read_csv(metadata_file)\n", "    print(f\"Loaded IFC metadata: {len(df):,} records\")\n", "    \n", "    # Extract coordinates\n", "    coord_cols = ['X', 'Y', 'Z']\n", "    if not all(col in df.columns for col in coord_cols):\n", "        raise ValueError(f\"Missing coordinate columns. Found: {list(df.columns)}\")\n", "    \n", "    # Get valid coordinates\n", "    valid_coords = df[coord_cols].dropna()\n", "    ifc_points = valid_coords.values\n", "    \n", "    print(f\"Valid IFC coordinates: {len(ifc_points):,} points\")\n", "    print(f\"Coordinate ranges:\")\n", "    for i, col in enumerate(coord_cols):\n", "        print(f\"  {col}: {ifc_points[:, i].min():.2f} to {ifc_points[:, i].max():.2f}\")\n", "    \n", "    return ifc_points\n", "\n", "print(\"Loading IFC metadata coordinates...\")\n", "ifc_points = load_ifc_points_from_metadata(ifc_metadata_file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Ground Control Point Definition with Z-Correction"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GCP 1: Found IFC Z=158.47m at distance 0.08m\n", "GCP 2: Found IFC Z=159.17m at distance 4.97m\n", "GCP 3: Found IFC Z=158.77m at distance 0.03m\n", "GCP 4: Found IFC Z=158.75m at distance 3.88m\n", "GCP 5: Found IFC Z=158.92m at distance 0.04m\n", "GCP 6: Found IFC Z=158.92m at distance 2.16m\n", "GCP 4_sw: Found IFC Z=156.46m at distance 0.04m\n", "GCP 4_sw2: Found IFC Z=156.46m at distance 2.39m\n", "GCP 5_SM: Found IFC Z=155.91m at distance 0.04m\n", "GCP 5_SM2: Found IFC Z=155.91m at distance 3.40m\n", "GCP 6_SE: Found IFC Z=155.79m at distance 0.04m\n", "GCP 6_SE2: Found IFC Z=155.97m at distance 4.27m\n", "\n", "Loaded 12 ground control points with proper Z elevations\n", "   Point_ID     Easting     Northing Location\n", "0         1  435267.277  5011787.189       NE\n", "1         2  435280.920  5011792.074       NE\n", "2         3  435267.220  5011921.754       NE\n", "3         4  435280.438  5011925.654       NE\n", "4         5  436224.793  5012459.896       NW\n", "5         6  436226.921  5012459.942       NW\n", "6      4_sw  436719.919  5011825.557       SW\n", "7     4_sw2  436718.628  5011831.735       SW\n", "8      5_SM  436302.667  5011417.757       SM\n", "9     5_SM2  436305.828  5011410.901       SM\n", "10     6_SE  436112.667  5010904.990       SE\n", "11    6_SE2  436118.344  5010917.949       SE\n"]}], "source": ["def load_ground_control_points(ifc_points):\n", "    \"\"\"Load the ground control points from project data and match with IFC Z elevations\"\"\"\n", "    gcp_data = {\n", "        'Point_ID': ['1', '2', '3', '4', '5', '6'],\n", "        'Easting': [435267.277, 435280.92, 435267.22, 435280.438, 436224.793, 436226.921],\n", "        'Northing': [5011787.189, 5011792.074, 5011921.754, 5011925.654, 5012459.896, 5012459.942],\n", "        'Location': ['NE', 'NE', 'NE', 'NE', 'NW', 'NW']\n", "    }\n", "    \n", "    # Additional points from project data\n", "    additional_points = {\n", "        'Point_ID': ['4_sw', '4_sw2', '5_SM', '5_SM2', '6_SE', '6_SE2'],\n", "        'Easting': [436719.919, 436718.628, 436302.667, 436305.828, 436112.667, 436118.344],\n", "        'Northing': [5011825.557, 5011831.735, 5011417.757, 5011410.901, 5010904.99, 5010917.949],\n", "        'Location': ['SW', 'SW', 'SM', 'SM', 'SE', 'SE']\n", "    }\n", "    \n", "    # <PERSON><PERSON><PERSON> all points\n", "    all_data = {}\n", "    for key in gcp_data:\n", "        all_data[key] = gcp_data[key] + additional_points[key]\n", "    \n", "    df = pd.DataFrame(all_data)\n", "    \n", "    # Find corresponding Z elevations from IFC data\n", "    ifc_tree = cKDTree(ifc_points[:, :2])  # X,Y only for matching\n", "    \n", "    gcp_z_values = []\n", "    for i, row in df.iterrows():\n", "        gcp_xy = np.array([row['Easting'], row['Northing']])\n", "        # Find closest IFC point within 10m radius\n", "        distances, indices = ifc_tree.query(gcp_xy, k=5, distance_upper_bound=10.0)\n", "        \n", "        if distances[0] < np.inf:\n", "            # Use Z value from closest IFC point\n", "            closest_z = ifc_points[indices[0], 2]\n", "            gcp_z_values.append(closest_z)\n", "            print(f\"GCP {row['Point_ID']}: Found IFC Z={closest_z:.2f}m at distance {distances[0]:.2f}m\")\n", "        else:\n", "            # Fallback to median IFC Z if no close match\n", "            median_z = np.median(ifc_points[:, 2])\n", "            gcp_z_values.append(median_z)\n", "            print(f\"GCP {row['Point_ID']}: No close IFC match, using median Z={median_z:.2f}m\")\n", "    \n", "    # Convert to 3D coordinates using actual IFC Z elevations\n", "    ifc_gcp_coords = np.column_stack([\n", "        df['Easting'].values,\n", "        df['Northing'].values,\n", "        np.array(gcp_z_values)  # Use actual IFC Z elevations\n", "    ])\n", "    \n", "    return df, ifc_gcp_coords\n", "\n", "gcp_df, ifc_gcp_coords = load_ground_control_points(ifc_points)\n", "print(f\"\\nLoaded {len(ifc_gcp_coords)} ground control points with proper Z elevations\")\n", "print(gcp_df)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Using manual GCP coordinates\n"]}], "source": ["def load_detected_gcp_coordinates(json_file_path):\n", "    \"\"\"Load GCP coordinates from automated detection results\"\"\"\n", "    import json\n", "    from pathlib import Path\n", "    \n", "    json_file = Path(json_file_path)\n", "    \n", "    if not json_file.exists():\n", "        print(f\"Detected GCP file not found: {json_file_path}\")\n", "        print(\"Run 03_automated_gcp_detection.ipynb first to generate coordinates\")\n", "        return None, None\n", "    \n", "    with open(json_file, 'r') as f:\n", "        gcp_data = json.load(f)\n", "    \n", "    print(f\"Loading detected GCP coordinates from: {json_file.name}\")\n", "    print(f\"Detection metadata: {gcp_data['metadata']}\")\n", "    \n", "    drone_coords = []\n", "    ifc_coords = []\n", "    \n", "    for pair in gcp_data['gcp_pairs']:\n", "        drone_coords.append([\n", "            pair['drone_coordinates']['x'],\n", "            pair['drone_coordinates']['y'], \n", "            pair['drone_coordinates']['z']\n", "        ])\n", "        ifc_coords.append([\n", "            pair['ifc_coordinates']['x'],\n", "            pair['ifc_coordinates']['y'],\n", "            pair['ifc_coordinates']['z']\n", "        ])\n", "    \n", "    drone_gcp_coords = np.array(drone_coords)\n", "    ifc_gcp_coords = np.array(ifc_coords)\n", "    \n", "    print(f\"Loaded {len(drone_gcp_coords)} detected GCP pairs\")\n", "    \n", "    return drone_gcp_coords, ifc_gcp_coords\n", "\n", "\n", "if use_detected_gcp:\n", "    detected_file = f\"../../../data/processed/automated_gcp/{ground_method}/{site_name}_detected_gcp_coordinates.json\"\n", "    detected_drone_gcp, detected_ifc_gcp = load_detected_gcp_coordinates(detected_file)\n", "    \n", "    if detected_drone_gcp is not None:\n", "        print(\"\\nUsing detected GCP coordinates for alignment\")\n", "        # Override manual coordinates with detected ones\n", "        drone_gcp_coords = detected_drone_gcp\n", "        ifc_gcp_coords = detected_ifc_gcp\n", "    else:\n", "        print(\"\\nFalling back to manual GCP coordinates\")\n", "else:\n", "    print(\"\\nUsing manual GCP coordinates\")\n", "    # Use the manual coordinates loaded above\n", "    drone_gcp_coords = None  # Will be found from drone data\n", "    # ifc_gcp_coords already loaded from manual function"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## GCP Matching and Transformation"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def find_corresponding_drone_points(drone_points, ifc_gcp_coords, search_radii=[2.0, 5.0, 10.0]):\n", "    \"\"\"\n", "    Find corresponding points in drone data for each GCP using multi-radius (XY) then 3D search.\n", "    \"\"\"\n", "    tree_xy = cKDTree(drone_points[:, :2])\n", "    drone_gcp_coords = []\n", "    valid_pairs = []\n", "\n", "    for i, gcp in enumerate(ifc_gcp_coords):\n", "        matched = False\n", "        for radius in search_radii:\n", "            idx = tree_xy.query_ball_point(gcp[:2], radius)\n", "            if idx:\n", "                # Stage 2: pick best 3D match among candidates\n", "                candidates = drone_points[idx]\n", "                best_idx = np.argmin(np.linalg.norm(candidates - gcp, axis=1))\n", "                best_match = candidates[best_idx]\n", "\n", "                drone_gcp_coords.append(best_match)\n", "                valid_pairs.append(i)\n", "                print(f\"GCP {i}: IFC {gcp[:2]} (Z={gcp[2]:.2f}) -> Drone {best_match[:2]} (Z={best_match[2]:.2f}) using radius {radius}m\")\n", "                matched = True\n", "                break  # Stop at first successful radius\n", "        if not matched:\n", "            print(f\"GCP {i}: No drone match found in any of the radii {search_radii}\")\n", "\n", "    return np.array(drone_gcp_coords), valid_pairs"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def compute_similarity_transform(source_points, target_points):\n", "    \"\"\"Compute optimal similarity transformation using Procrustes analysis\"\"\"\n", "    # Center the points\n", "    source_centroid = np.mean(source_points, axis=0)\n", "    target_centroid = np.mean(target_points, axis=0)\n", "    \n", "    source_centered = source_points - source_centroid\n", "    target_centered = target_points - target_centroid\n", "    \n", "    # Compute scale\n", "    source_scale = np.sqrt(np.sum(source_centered**2))\n", "    target_scale = np.sqrt(np.sum(target_centered**2))\n", "    scale = target_scale / source_scale if source_scale > 0 else 1.0\n", "    \n", "    # Normalize for rotation calculation\n", "    source_norm = source_centered / source_scale if source_scale > 0 else source_centered\n", "    target_norm = target_centered / target_scale if target_scale > 0 else target_centered\n", "    \n", "    # Compute rotation using SVD\n", "    H = source_norm.T @ target_norm\n", "    U, _, Vt = np.linalg.svd(H)\n", "    R = Vt.T @ U.T\n", "    \n", "    # Ensure proper rotation (det(R) = 1)\n", "    if np.linalg.det(R) < 0:\n", "        Vt[-1, :] *= -1\n", "        R = Vt.T @ U.T\n", "    \n", "    # Compute translation\n", "    translation = target_centroid - scale * (R @ source_centroid)\n", "    \n", "    return R, translation, scale"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def apply_similarity_transform(points, R, translation, scale):\n", "    \"\"\"Apply similarity transformation to point cloud\"\"\"\n", "    return scale * (points @ R.T) + translation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Execute GCP Alignment with Z-Correction"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Searching for corresponding drone points...\n", "GCP 0: IFC [ 435267.277 5011787.189] (Z=158.47) -> Drone [ 435266.174 5011786.665] (Z=0.82) using radius 2.0m\n", "GCP 1: IFC [ 435280.92  5011792.074] (Z=159.17) -> Drone [ 435282.042 5011792.855] (Z=2.94) using radius 2.0m\n", "GCP 2: IFC [ 435267.22  5011921.754] (Z=158.77) -> Drone [ 435265.418 5011922.038] (Z=0.80) using radius 2.0m\n", "GCP 3: IFC [ 435280.438 5011925.654] (Z=158.75) -> Drone [ 435281.964 5011925.242] (Z=2.92) using radius 2.0m\n", "GCP 4: IFC [ 436224.793 5012459.896] (Z=158.92) -> Drone [ 436226.032 5012458.481] (Z=3.49) using radius 2.0m\n", "GCP 5: IFC [ 436226.921 5012459.942] (Z=158.92) -> Drone [ 436226.032 5012458.481] (Z=3.49) using radius 2.0m\n", "GCP 6: IFC [ 436719.919 5011825.557] (Z=156.46) -> Drone [ 436721.455 5011822.514] (Z=1.64) using radius 5.0m\n", "GCP 7: IFC [ 436718.628 5011831.735] (Z=156.46) -> Drone [ 436716.906 5011832.095] (Z=3.07) using radius 2.0m\n", "GCP 8: IFC [ 436302.667 5011417.757] (Z=155.91) -> Drone [ 436298.347 5011419.843] (Z=2.62) using radius 5.0m\n", "GCP 9: IFC [ 436305.828 5011410.901] (Z=155.91) -> Drone [ 436305.584 5011414.021] (Z=2.60) using radius 5.0m\n", "GCP 10: IFC [ 436112.667 5010904.99 ] (Z=155.79) -> Drone [ 436113.933 5010906.167] (Z=1.28) using radius 2.0m\n", "GCP 11: IFC [ 436118.344 5010917.949] (Z=155.97) -> Drone [ 436118.241 5010917.784] (Z=3.27) using radius 2.0m\n", "Found 12 valid GCP pairs\n", "\n", "Z-coordinate analysis:\n", "IFC GCP Z range: 155.79m to 159.17m\n", "Drone GCP Z range: 0.80m to 3.49m\n", "Estimated Z offset (IFC - Drone): 154.69m\n", "\n", "Computing similarity transformation...\n", "Scale factor: 1.000966\n", "Translation: [  866.20421674 -4951.08663264 -6549.4762205 ]\n", "Rotation matrix:\n", "[[ 9.99997534e-01 -2.56392428e-04  2.20615674e-03]\n", " [ 2.59763312e-04  9.99998799e-01 -1.52779317e-03]\n", " [-2.20576237e-03  1.52836248e-03  9.99996399e-01]]\n", "\n", "GCP Alignment Quality:\n", "Mean residual: 2.109m\n", "Max residual: 4.118m\n", "RMS residual: 2.352m\n"]}], "source": ["# Find corresponding points in drone data with multi-radius search\n", "print(\"Searching for corresponding drone points...\")\n", "search_radii = [2.0, 5.0, 10.0]  # Try tighter match first, fallback if needed\n", "drone_gcp_coords, valid_pairs = find_corresponding_drone_points(\n", "    drone_points, ifc_gcp_coords, search_radii\n", ")\n", "\n", "if len(valid_pairs) < 3:\n", "    print(f\"ERROR: Need at least 3 GCP pairs, found {len(valid_pairs)}\")\n", "else:\n", "    print(f\"Found {len(valid_pairs)} valid GCP pairs\")\n", "\n", "    ifc_gcp_valid = ifc_gcp_coords[valid_pairs]\n", "\n", "    print(\"\\nZ-coordinate analysis:\")\n", "    print(f\"IFC GCP Z range: {ifc_gcp_valid[:, 2].min():.2f}m to {ifc_gcp_valid[:, 2].max():.2f}m\")\n", "    print(f\"Drone GCP Z range: {drone_gcp_coords[:, 2].min():.2f}m to {drone_gcp_coords[:, 2].max():.2f}m\")\n", "    z_offset = np.median(ifc_gcp_valid[:, 2]) - np.median(drone_gcp_coords[:, 2])\n", "    print(f\"Estimated Z offset (IFC - Drone): {z_offset:.2f}m\")\n", "\n", "    print(\"\\nComputing similarity transformation...\")\n", "    R, translation, scale = compute_similarity_transform(drone_gcp_coords, ifc_gcp_valid)\n", "\n", "    print(f\"Scale factor: {scale:.6f}\")\n", "    print(f\"Translation: {translation}\")\n", "    print(f\"Rotation matrix:\\n{R}\")\n", "\n", "    drone_aligned = apply_similarity_transform(drone_points, R, translation, scale)\n", "    drone_gcp_transformed = apply_similarity_transform(drone_gcp_coords, R, translation, scale)\n", "    residuals = np.linalg.norm(drone_gcp_transformed - ifc_gcp_valid, axis=1)\n", "\n", "    print(f\"\\nGCP Alignment Quality:\")\n", "    print(f\"Mean residual: {np.mean(residuals):.3f}m\")\n", "    print(f\"Max residual: {np.max(residuals):.3f}m\")\n", "    print(f\"RMS residual: {np.sqrt(np.mean(residuals**2)):.3f}m\")\n", "\n", "    transform_params = {\n", "        'rotation_matrix': R,\n", "        'translation': translation,\n", "        'scale': scale,\n", "        'gcp_residuals': residuals,\n", "        'rms_error': np.sqrt(np.mean(residuals**2)),\n", "        'z_offset_estimated': z_offset\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Quality Analysis and Comparison"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ALIGNMENT METHOD COMPARISON ===\n", "Centroid-Only Alignment: 28.57m RMSE\n", "GCP-Based Alignment (Z-corrected): 2.35m RMSE\n", "Improvement: 91.8% reduction in error\n", "\n", "=== CONSTRUCTION MONITORING IMPACT ===\n", "Suitability for different applications:\n", "  Survey Grade         (≤ 0.1m): Centroid No | GCP No\n", "  Construction Layout  (≤ 0.5m): Centroid No | GCP No\n", "  Quality Control      (≤ 2.0m): Centroid No | GCP No\n", "  Progress Monitoring  (≤ 5.0m): Centroid No | GCP Yes\n", "  General Assessment   (≤10.0m): Centroid No | GCP Yes\n"]}], "source": ["def compare_alignment_methods(centroid_rmse=28.57, gcp_rmse=5.101):\n", "    \"\"\"Compare the two alignment methods\"\"\"\n", "    \n", "    print(f\"=== ALIGNMENT METHOD COMPARISON ===\")\n", "    \n", "    improvement = ((centroid_rmse - gcp_rmse) / centroid_rmse) * 100\n", "    \n", "    print(f\"Centroid-Only Alignment: {centroid_rmse:.2f}m RMSE\")\n", "    print(f\"GCP-Based Alignment (Z-corrected): {gcp_rmse:.2f}m RMSE\")\n", "    print(f\"Improvement: {improvement:.1f}% reduction in error\")\n", "    \n", "    # Construction monitoring implications\n", "    print(f\"\\n=== CONSTRUCTION MONITORING IMPACT ===\")\n", "    \n", "    accuracy_levels = {\n", "        'Survey Grade': 0.1,\n", "        'Construction Layout': 0.5, \n", "        'Quality Control': 2.0,\n", "        'Progress Monitoring': 5.0,\n", "        'General Assessment': 10.0\n", "    }\n", "    \n", "    print(f\"Suitability for different applications:\")\n", "    for application, required_accuracy in accuracy_levels.items():\n", "        centroid_suitable = \"Yes\" if centroid_rmse <= required_accuracy else \"No\"\n", "        gcp_suitable = \"Yes\" if gcp_rmse <= required_accuracy else \"No\"\n", "        print(f\"  {application:20} (≤{required_accuracy:4.1f}m): Centroid {centroid_suitable} | GCP {gcp_suitable}\")\n", "\n", "# Run comparison if transformation was successful\n", "if 'transform_params' in locals():\n", "    compare_alignment_methods(gcp_rmse=transform_params['rms_error'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Results"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SAVING RESULTS ===\n", "Saved GCP-aligned point cloud: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment_z_corrected/ransac_pmf/trino_enel_gcp_aligned_z_corrected.ply\n", "Saved transformation parameters: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment_z_corrected/ransac_pmf/trino_enel_gcp_transform_params_z_corrected.json\n", "Saved GCP residuals: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment_z_corrected/ransac_pmf/trino_enel_gcp_residuals_z_corrected.csv\n", "\n", "Saved files:\n", "  aligned_file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment_z_corrected/ransac_pmf/trino_enel_gcp_aligned_z_corrected.ply\n", "  transform_file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment_z_corrected/ransac_pmf/trino_enel_gcp_transform_params_z_corrected.json\n", "  residuals_file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment_z_corrected/ransac_pmf/trino_enel_gcp_residuals_z_corrected.csv\n"]}], "source": ["def save_gcp_alignment_results(drone_aligned, transform_params, output_dir, site_name, ground_method):\n", "    \"\"\"Save GCP alignment results including aligned point cloud and metadata\"\"\"\n", "    from pathlib import Path\n", "    import json\n", "    \n", "    # Create output directory\n", "    output_path = Path(output_dir) / ground_method\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # Save aligned point cloud\n", "    aligned_pcd = o3d.geometry.PointCloud()\n", "    aligned_pcd.points = o3d.utility.Vector3dVector(drone_aligned)\n", "    aligned_file = output_path / f\"{site_name}_gcp_aligned_z_corrected.ply\"\n", "    \n", "    success = o3d.io.write_point_cloud(str(aligned_file), aligned_pcd)\n", "    if success:\n", "        print(f\"Saved GCP-aligned point cloud: {aligned_file}\")\n", "    else:\n", "        print(f\"Failed to save aligned point cloud: {aligned_file}\")\n", "    \n", "    # Save transformation parameters\n", "    transform_data = {\n", "        'method': 'gcp_similarity_transform_z_corrected',\n", "        'rotation_matrix': transform_params['rotation_matrix'].tolist(),\n", "        'translation': transform_params['translation'].tolist(),\n", "        'scale': float(transform_params['scale']),\n", "        'gcp_residuals': transform_params['gcp_residuals'].tolist(),\n", "        'rms_error': float(transform_params['rms_error']),\n", "        'z_offset_estimated': float(transform_params['z_offset_estimated']),\n", "        'num_gcp_pairs': len(transform_params['gcp_residuals']),\n", "        'site_name': site_name,\n", "        'ground_method': ground_method\n", "    }\n", "    \n", "    transform_file = output_path / f\"{site_name}_gcp_transform_params_z_corrected.json\"\n", "    with open(transform_file, 'w') as f:\n", "        json.dump(transform_data, f, indent=2)\n", "    \n", "    print(f\"Saved transformation parameters: {transform_file}\")\n", "    \n", "    # Save GCP residuals as CSV for analysis\n", "    residuals_df = pd.DataFrame({\n", "        'gcp_id': range(len(transform_params['gcp_residuals'])),\n", "        'residual_m': transform_params['gcp_residuals']\n", "    })\n", "    \n", "    residuals_file = output_path / f\"{site_name}_gcp_residuals_z_corrected.csv\"\n", "    residuals_df.to_csv(residuals_file, index=False)\n", "    print(f\"Saved GCP residuals: {residuals_file}\")\n", "    \n", "    return {\n", "        'aligned_file': str(aligned_file),\n", "        'transform_file': str(transform_file),\n", "        'residuals_file': str(residuals_file)\n", "    }\n", "\n", "# Save results if transformation was successful\n", "if 'transform_params' in locals() and 'drone_aligned' in locals() and save_results:\n", "    print(\"\\n=== SAVING RESULTS ===\")\n", "    saved_files = save_gcp_alignment_results(\n", "        drone_aligned, transform_params, output_dir, site_name, ground_method\n", "    )\n", "    \n", "    print(f\"\\nSaved files:\")\n", "    for key, filepath in saved_files.items():\n", "        print(f\"  {key}: {filepath}\")\n", "else:\n", "    print(\"\\nSkipping save - transformation failed or save_results=False\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Outlier Removal and Re-alignment\n", "\n", "Remove GCPs with residuals >3.5m and re-run the alignment to improve accuracy."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== OUTLIER REMOVAL AND RE-ALIGNMENT ===\n", "Original GCP pairs: 12\n", "Outliers (>3.5m): 2\n", "\n", "Outlier GCPs:\n", "  GCP 6: 3.974m\n", "  GCP 8: 4.118m\n", "\n", "Filtered GCP pairs: 10\n", "\n", "Re-computing similarity transformation without outliers...\n", "Filtered scale factor: 1.000921\n", "Filtered translation: [-1945.64563886 -4478.17952676 -5699.63115815]\n", "\n", "=== FILTERED ALIGNMENT QUALITY ===\n", "Mean residual: 1.688m\n", "Max residual: 2.551m\n", "RMS residual: 1.771m\n", "\n", "=== IMPROVEMENT ANALYSIS ===\n", "Original RMSE: 2.352m\n", "Filtered RMSE: 1.771m\n", "Improvement: 24.7% reduction in error\n", "\n", "=== CONSTRUCTION MONITORING SUITABILITY (FILTERED) ===\n", "Suitability for different applications:\n", "  Survey Grade         (≤ 0.1m): Original No | Filtered No\n", "  Construction Layout  (≤ 0.5m): Original No | Filtered No\n", "  Quality Control      (≤ 2.0m): Original No | Filtered Yes\n", "  Progress Monitoring  (≤ 5.0m): Original Yes | Filtered Yes\n", "  General Assessment   (≤10.0m): Original Yes | Filtered Yes\n", "\n", "=== SAVING FILTERED RESULTS ===\n", "Saved filtered aligned point cloud: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment_z_corrected/ransac_pmf/filtered/trino_enel_gcp_aligned_z_corrected_filtered.ply\n"]}, {"ename": "TypeError", "evalue": "Object of type int64 is not JSON serializable", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[14]\u001b[39m\u001b[32m, line 128\u001b[39m\n\u001b[32m    126\u001b[39m transform_file_filtered = output_path_filtered / \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00msite_name\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m_gcp_transform_params_z_corrected_filtered.json\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    127\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(transform_file_filtered, \u001b[33m'\u001b[39m\u001b[33mw\u001b[39m\u001b[33m'\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[32m--> \u001b[39m\u001b[32m128\u001b[39m     \u001b[43mjson\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdump\u001b[49m\u001b[43m(\u001b[49m\u001b[43mtransform_data_filtered\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindent\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m2\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    130\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mSaved filtered transformation parameters: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mtransform_file_filtered\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m    132\u001b[39m \u001b[38;5;66;03m# Save filtered residuals\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/json/__init__.py:179\u001b[39m, in \u001b[36mdump\u001b[39m\u001b[34m(obj, fp, skipkeys, ensure_ascii, check_circular, allow_nan, cls, indent, separators, default, sort_keys, **kw)\u001b[39m\n\u001b[32m    173\u001b[39m     iterable = \u001b[38;5;28mcls\u001b[39m(skipkeys=skipkeys, ensure_ascii=ensure_ascii,\n\u001b[32m    174\u001b[39m         check_circular=check_circular, allow_nan=allow_nan, indent=indent,\n\u001b[32m    175\u001b[39m         separators=separators,\n\u001b[32m    176\u001b[39m         default=default, sort_keys=sort_keys, **kw).iterencode(obj)\n\u001b[32m    177\u001b[39m \u001b[38;5;66;03m# could accelerate with writelines in some versions of Python, at\u001b[39;00m\n\u001b[32m    178\u001b[39m \u001b[38;5;66;03m# a debuggability cost\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m179\u001b[39m \u001b[43m\u001b[49m\u001b[38;5;28;43;01mfor\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;129;43;01min\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43miterable\u001b[49m\u001b[43m:\u001b[49m\n\u001b[32m    180\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfp\u001b[49m\u001b[43m.\u001b[49m\u001b[43mwrite\u001b[49m\u001b[43m(\u001b[49m\u001b[43mchunk\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/json/encoder.py:432\u001b[39m, in \u001b[36m_make_iterencode.<locals>._iterencode\u001b[39m\u001b[34m(o, _current_indent_level)\u001b[39m\n\u001b[32m    430\u001b[39m     \u001b[38;5;28;01<PERSON><PERSON> from\u001b[39;00m _iterencode_list(o, _current_indent_level)\n\u001b[32m    431\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(o, \u001b[38;5;28mdict\u001b[39m):\n\u001b[32m--> \u001b[39m\u001b[32m432\u001b[39m     \u001b[38;5;28;01<PERSON><PERSON> from\u001b[39;00m _iterencode_dict(o, _current_indent_level)\n\u001b[32m    433\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    434\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m markers \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/json/encoder.py:406\u001b[39m, in \u001b[36m_make_iterencode.<locals>._iterencode_dict\u001b[39m\u001b[34m(dct, _current_indent_level)\u001b[39m\n\u001b[32m    404\u001b[39m         \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    405\u001b[39m             chunks = _iterencode(value, _current_indent_level)\n\u001b[32m--> \u001b[39m\u001b[32m406\u001b[39m         \u001b[38;5;28;01<PERSON><PERSON> from\u001b[39;00m chunks\n\u001b[32m    407\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m newline_indent \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    408\u001b[39m     _current_indent_level -= \u001b[32m1\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/json/encoder.py:439\u001b[39m, in \u001b[36m_make_iterencode.<locals>._iterencode\u001b[39m\u001b[34m(o, _current_indent_level)\u001b[39m\n\u001b[32m    437\u001b[39m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mCircular reference detected\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    438\u001b[39m     markers[markerid] = o\n\u001b[32m--> \u001b[39m\u001b[32m439\u001b[39m o = \u001b[43m_default\u001b[49m\u001b[43m(\u001b[49m\u001b[43mo\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    440\u001b[39m \u001b[38;5;28;01myield from\u001b[39;00m _iterencode(o, _current_indent_level)\n\u001b[32m    441\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m markers \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/json/encoder.py:180\u001b[39m, in \u001b[36mJSONEncoder.default\u001b[39m\u001b[34m(self, o)\u001b[39m\n\u001b[32m    161\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mdefault\u001b[39m(\u001b[38;5;28mself\u001b[39m, o):\n\u001b[32m    162\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Implement this method in a subclass such that it returns\u001b[39;00m\n\u001b[32m    163\u001b[39m \u001b[33;03m    a serializable object for ``o``, or calls the base implementation\u001b[39;00m\n\u001b[32m    164\u001b[39m \u001b[33;03m    (to raise a ``TypeError``).\u001b[39;00m\n\u001b[32m   (...)\u001b[39m\u001b[32m    178\u001b[39m \n\u001b[32m    179\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m180\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mTypeError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m'\u001b[39m\u001b[33mObject of type \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mo.\u001b[34m__class__\u001b[39m.\u001b[34m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m \u001b[39m\u001b[33m'\u001b[39m\n\u001b[32m    181\u001b[39m                     \u001b[33mf\u001b[39m\u001b[33m'\u001b[39m\u001b[33mis not JSON serializable\u001b[39m\u001b[33m'\u001b[39m)\n", "\u001b[31mTypeError\u001b[39m: Object of type int64 is not JSON serializable"]}], "source": ["# Outlier removal and re-alignment\n", "if 'transform_params' in locals() and 'drone_gcp_coords' in locals():\n", "    print(\"=== OUTLIER REMOVAL AND RE-ALIGNMENT ===\")\n", "    \n", "    # Identify outliers (residuals > 3.5m)\n", "    outlier_threshold = 3.5\n", "    residuals = transform_params['gcp_residuals']\n", "    outlier_mask = np.array(residuals) > outlier_threshold\n", "    \n", "    print(f\"Original GCP pairs: {len(residuals)}\")\n", "    print(f\"Outliers (>{outlier_threshold}m): {np.sum(outlier_mask)}\")\n", "    \n", "    if np.sum(outlier_mask) > 0:\n", "        print(f\"\\nOutlier GCPs:\")\n", "        for i, (is_outlier, residual) in enumerate(zip(outlier_mask, residuals)):\n", "            if is_outlier:\n", "                print(f\"  GCP {valid_pairs[i]}: {residual:.3f}m\")\n", "        \n", "        # Remove outliers\n", "        inlier_mask = ~outlier_mask\n", "        drone_gcp_filtered = drone_gcp_coords[inlier_mask]\n", "        ifc_gcp_filtered = ifc_gcp_valid[inlier_mask]\n", "        valid_pairs_filtered = [valid_pairs[i] for i in range(len(valid_pairs)) if inlier_mask[i]]\n", "        \n", "        print(f\"\\nFiltered GCP pairs: {len(drone_gcp_filtered)}\")\n", "        \n", "        if len(drone_gcp_filtered) >= 3:\n", "            print(\"\\nRe-computing similarity transformation without outliers...\")\n", "            R_filtered, translation_filtered, scale_filtered = compute_similarity_transform(\n", "                drone_gcp_filtered, ifc_gcp_filtered\n", "            )\n", "            \n", "            print(f\"Filtered scale factor: {scale_filtered:.6f}\")\n", "            print(f\"Filtered translation: {translation_filtered}\")\n", "            \n", "            # Apply filtered transformation\n", "            drone_aligned_filtered = apply_similarity_transform(\n", "                drone_points, R_filtered, translation_filtered, scale_filtered\n", "            )\n", "            drone_gcp_transformed_filtered = apply_similarity_transform(\n", "                drone_gcp_filtered, R_filtered, translation_filtered, scale_filtered\n", "            )\n", "            \n", "            # Calculate new residuals\n", "            residuals_filtered = np.linalg.norm(\n", "                drone_gcp_transformed_filtered - ifc_gcp_filtered, axis=1\n", "            )\n", "            \n", "            print(f\"\\n=== FILTERED ALIGNMENT QUALITY ===\")\n", "            print(f\"Mean residual: {np.mean(residuals_filtered):.3f}m\")\n", "            print(f\"Max residual: {np.max(residuals_filtered):.3f}m\")\n", "            print(f\"RMS residual: {np.sqrt(np.mean(residuals_filtered**2)):.3f}m\")\n", "            \n", "            # Compare with original\n", "            original_rmse = transform_params['rms_error']\n", "            filtered_rmse = np.sqrt(np.mean(residuals_filtered**2))\n", "            improvement = ((original_rmse - filtered_rmse) / original_rmse) * 100\n", "            \n", "            print(f\"\\n=== IMPROVEMENT ANALYSIS ===\")\n", "            print(f\"Original RMSE: {original_rmse:.3f}m\")\n", "            print(f\"Filtered RMSE: {filtered_rmse:.3f}m\")\n", "            print(f\"Improvement: {improvement:.1f}% reduction in error\")\n", "            \n", "            # Construction monitoring assessment\n", "            print(f\"\\n=== CONSTRUCTION MONITORING SUITABILITY (FILTERED) ===\")\n", "            accuracy_levels = {\n", "                'Survey Grade': 0.1,\n", "                'Construction Layout': 0.5, \n", "                'Quality Control': 2.0,\n", "                'Progress Monitoring': 5.0,\n", "                'General Assessment': 10.0\n", "            }\n", "            \n", "            print(f\"Suitability for different applications:\")\n", "            for application, required_accuracy in accuracy_levels.items():\n", "                original_suitable = \"Yes\" if original_rmse <= required_accuracy else \"No\"\n", "                filtered_suitable = \"Yes\" if filtered_rmse <= required_accuracy else \"No\"\n", "                print(f\"  {application:20} (≤{required_accuracy:4.1f}m): Original {original_suitable} | Filtered {filtered_suitable}\")\n", "            \n", "            # Store filtered results\n", "            transform_params_filtered = {\n", "                'rotation_matrix': R_filtered,\n", "                'translation': translation_filtered,\n", "                'scale': scale_filtered,\n", "                'gcp_residuals': residuals_filtered,\n", "                'rms_error': filtered_rmse,\n", "                'z_offset_estimated': np.median(ifc_gcp_filtered[:, 2]) - np.median(drone_gcp_filtered[:, 2]),\n", "                'num_gcp_pairs': len(drone_gcp_filtered),\n", "                'outliers_removed': np.sum(outlier_mask),\n", "                'outlier_threshold': outlier_threshold\n", "            }\n", "            \n", "            # Save filtered results if requested\n", "            if save_results:\n", "                print(f\"\\n=== SAVING FILTERED RESULTS ===\")\n", "                \n", "                # Create filtered output directory\n", "                output_path_filtered = Path(output_dir) / ground_method / \"filtered\"\n", "                output_path_filtered.mkdir(parents=True, exist_ok=True)\n", "                \n", "                # Save filtered aligned point cloud\n", "                aligned_pcd_filtered = o3d.geometry.PointCloud()\n", "                aligned_pcd_filtered.points = o3d.utility.Vector3dVector(drone_aligned_filtered)\n", "                aligned_file_filtered = output_path_filtered / f\"{site_name}_gcp_aligned_z_corrected_filtered.ply\"\n", "                \n", "                success = o3d.io.write_point_cloud(str(aligned_file_filtered), aligned_pcd_filtered)\n", "                if success:\n", "                    print(f\"Saved filtered aligned point cloud: {aligned_file_filtered}\")\n", "                \n", "                # Save filtered transformation parameters\n", "                transform_data_filtered = {\n", "                    'method': 'gcp_similarity_transform_z_corrected_filtered',\n", "                    'rotation_matrix': transform_params_filtered['rotation_matrix'].tolist(),\n", "                    'translation': transform_params_filtered['translation'].tolist(),\n", "                    'scale': float(transform_params_filtered['scale']),\n", "                    'gcp_residuals': transform_params_filtered['gcp_residuals'].tolist(),\n", "                    'rms_error': float(transform_params_filtered['rms_error']),\n", "                    'z_offset_estimated': float(transform_params_filtered['z_offset_estimated']),\n", "                    'num_gcp_pairs': transform_params_filtered['num_gcp_pairs'],\n", "                    'outliers_removed': transform_params_filtered['outliers_removed'],\n", "                    'outlier_threshold': transform_params_filtered['outlier_threshold'],\n", "                    'site_name': site_name,\n", "                    'ground_method': ground_method\n", "                }\n", "                \n", "                transform_file_filtered = output_path_filtered / f\"{site_name}_gcp_transform_params_z_corrected_filtered.json\"\n", "                with open(transform_file_filtered, 'w') as f:\n", "                    json.dump(transform_data_filtered, f, indent=2)\n", "                \n", "                print(f\"Saved filtered transformation parameters: {transform_file_filtered}\")\n", "                \n", "                # Save filtered residuals\n", "                residuals_df_filtered = pd.DataFrame({\n", "                    'gcp_id': valid_pairs_filtered,\n", "                    'residual_m': transform_params_filtered['gcp_residuals']\n", "                })\n", "                \n", "                residuals_file_filtered = output_path_filtered / f\"{site_name}_gcp_residuals_z_corrected_filtered.csv\"\n", "                residuals_df_filtered.to_csv(residuals_file_filtered, index=False)\n", "                print(f\"Saved filtered residuals: {residuals_file_filtered}\")\n", "        \n", "        else:\n", "            print(f\"ERROR: After removing outliers, only {len(drone_gcp_filtered)} GCP pairs remain (need ≥3)\")\n", "    \n", "    else:\n", "        print(f\"No outliers found with threshold >{outlier_threshold}m\")\n", "\n", "else:\n", "    print(\"No transformation results available for outlier removal\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}