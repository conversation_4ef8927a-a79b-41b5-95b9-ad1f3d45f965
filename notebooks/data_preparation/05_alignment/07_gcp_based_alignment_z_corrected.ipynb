import sys
from pathlib import Path

# Add the `notebooks` folder to sys.path
notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]

if str(notebooks_root) not in sys.path:
    sys.path.insert(0, str(notebooks_root))

# Now import from shared package
from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path

# Parameters (Papermill)
ground_method = "csf"  # Ground segmentation method
site_name = "trino_enel"
save_results = True
quality_sample_size = 5000  # For quality assessment sampling

# Option to use detected coordinates instead of manual ones
use_detected_gcp = False  # Set to True to use automated detection results

# Imports
import numpy as np
import pandas as pd
import open3d as o3d
import laspy
from pathlib import Path
import json
from scipy.spatial import cKDTree
from scipy.spatial.transform import Rotation
from scipy.optimize import least_squares
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')


# Setup paths using shared config
output_dir = get_processed_data_path(site_name, "gcp_alignment_z_corrected")

print("=== GROUND CONTROL POINT BASED ALIGNMENT WITH Z-CORRECTION ===")
print(f"Ground method: {ground_method}")
print(f"Site: {site_name}")
print(f"Output directory: {output_dir}")
print(f"Using GCP-based similarity transformation with Z-correction")

# Define file paths using shared config
ground_seg_path = get_processed_data_path(site_name, "ground_segmentation") / ground_method
ifc_metadata_path = get_processed_data_path(site_name, "ifc_metadata")
ifc_pointcloud_path = get_processed_data_path(site_name, "ifc_pointclouds")

# Find the actual files
drone_file = find_latest_file(ground_seg_path, f"{site_name}_nonground.ply")
ifc_metadata_file = find_latest_file(ifc_metadata_path, "*enhanced_metadata.csv")
ifc_pointcloud_file = find_latest_file(ifc_pointcloud_path, "*data_driven.ply")

print("Loading data for GCP-based alignment...")
print(f"Drone file: {drone_file}")
print(f"IFC metadata file: {ifc_metadata_file}")
print(f"IFC point cloud file: {ifc_pointcloud_file}")

def load_drone_points(drone_path):
    """Load drone point cloud"""
    drone_file = Path(drone_path)
    
    if not drone_file.exists():
        raise FileNotFoundError(f"Drone file not found: {drone_path}")
    
    if drone_file.suffix.lower() == ".las":
        drone_las = laspy.read(drone_file)
        drone_points = drone_las.xyz
    elif drone_file.suffix.lower() == ".ply":
        drone_pcd = o3d.io.read_point_cloud(str(drone_file))
        drone_points = np.asarray(drone_pcd.points)
    else:
        raise ValueError("Unsupported drone file format. Use .las or .ply")
    
    print(f"Loaded drone scan: {drone_points.shape[0]:,} points")
    return drone_points

# Load data
drone_points = load_drone_points(drone_file)

def load_ifc_points_from_metadata(metadata_csv_path):
    """Load IFC coordinates from metadata CSV"""
    metadata_file = Path(metadata_csv_path)
    
    if not metadata_file.exists():
        raise FileNotFoundError(f"IFC metadata file not found: {metadata_csv_path}")
    
    # Load metadata
    df = pd.read_csv(metadata_file)
    print(f"Loaded IFC metadata: {len(df):,} records")
    
    # Extract coordinates
    coord_cols = ['X', 'Y', 'Z']
    if not all(col in df.columns for col in coord_cols):
        raise ValueError(f"Missing coordinate columns. Found: {list(df.columns)}")
    
    # Get valid coordinates
    valid_coords = df[coord_cols].dropna()
    ifc_points = valid_coords.values
    
    print(f"Valid IFC coordinates: {len(ifc_points):,} points")
    print(f"Coordinate ranges:")
    for i, col in enumerate(coord_cols):
        print(f"  {col}: {ifc_points[:, i].min():.2f} to {ifc_points[:, i].max():.2f}")
    
    return ifc_points

print("Loading IFC metadata coordinates...")
ifc_points = load_ifc_points_from_metadata(ifc_metadata_file)

def load_ground_control_points(ifc_points):
    """Load the ground control points from project data and match with IFC Z elevations"""
    gcp_data = {
        'Point_ID': ['1', '2', '3', '4', '5', '6'],
        'Easting': [435267.277, 435280.92, 435267.22, 435280.438, 436224.793, 436226.921],
        'Northing': [5011787.189, 5011792.074, 5011921.754, 5011925.654, 5012459.896, 5012459.942],
        'Location': ['NE', 'NE', 'NE', 'NE', 'NW', 'NW']
    }
    
    # Additional points from project data
    additional_points = {
        'Point_ID': ['4_sw', '4_sw2', '5_SM', '5_SM2', '6_SE', '6_SE2'],
        'Easting': [436719.919, 436718.628, 436302.667, 436305.828, 436112.667, 436118.344],
        'Northing': [5011825.557, 5011831.735, 5011417.757, 5011410.901, 5010904.99, 5010917.949],
        'Location': ['SW', 'SW', 'SM', 'SM', 'SE', 'SE']
    }
    
    # Combine all points
    all_data = {}
    for key in gcp_data:
        all_data[key] = gcp_data[key] + additional_points[key]
    
    df = pd.DataFrame(all_data)
    
    # Find corresponding Z elevations from IFC data
    ifc_tree = cKDTree(ifc_points[:, :2])  # X,Y only for matching
    
    gcp_z_values = []
    for i, row in df.iterrows():
        gcp_xy = np.array([row['Easting'], row['Northing']])
        # Find closest IFC point within 10m radius
        distances, indices = ifc_tree.query(gcp_xy, k=5, distance_upper_bound=10.0)
        
        if distances[0] < np.inf:
            # Use Z value from closest IFC point
            closest_z = ifc_points[indices[0], 2]
            gcp_z_values.append(closest_z)
            print(f"GCP {row['Point_ID']}: Found IFC Z={closest_z:.2f}m at distance {distances[0]:.2f}m")
        else:
            # Fallback to median IFC Z if no close match
            median_z = np.median(ifc_points[:, 2])
            gcp_z_values.append(median_z)
            print(f"GCP {row['Point_ID']}: No close IFC match, using median Z={median_z:.2f}m")
    
    # Convert to 3D coordinates using actual IFC Z elevations
    ifc_gcp_coords = np.column_stack([
        df['Easting'].values,
        df['Northing'].values,
        np.array(gcp_z_values)  # Use actual IFC Z elevations
    ])
    
    return df, ifc_gcp_coords

gcp_df, ifc_gcp_coords = load_ground_control_points(ifc_points)
print(f"\nLoaded {len(ifc_gcp_coords)} ground control points with proper Z elevations")
print(gcp_df)

def load_detected_gcp_coordinates(json_file_path):
    """Load GCP coordinates from automated detection results"""
    import json
    from pathlib import Path
    
    json_file = Path(json_file_path)
    
    if not json_file.exists():
        print(f"Detected GCP file not found: {json_file_path}")
        print("Run 03_automated_gcp_detection.ipynb first to generate coordinates")
        return None, None
    
    with open(json_file, 'r') as f:
        gcp_data = json.load(f)
    
    print(f"Loading detected GCP coordinates from: {json_file.name}")
    print(f"Detection metadata: {gcp_data['metadata']}")
    
    drone_coords = []
    ifc_coords = []
    
    for pair in gcp_data['gcp_pairs']:
        drone_coords.append([
            pair['drone_coordinates']['x'],
            pair['drone_coordinates']['y'], 
            pair['drone_coordinates']['z']
        ])
        ifc_coords.append([
            pair['ifc_coordinates']['x'],
            pair['ifc_coordinates']['y'],
            pair['ifc_coordinates']['z']
        ])
    
    drone_gcp_coords = np.array(drone_coords)
    ifc_gcp_coords = np.array(ifc_coords)
    
    print(f"Loaded {len(drone_gcp_coords)} detected GCP pairs")
    
    return drone_gcp_coords, ifc_gcp_coords


if use_detected_gcp:
    detected_file = f"../../../data/processed/automated_gcp/{ground_method}/{site_name}_detected_gcp_coordinates.json"
    detected_drone_gcp, detected_ifc_gcp = load_detected_gcp_coordinates(detected_file)
    
    if detected_drone_gcp is not None:
        print("\nUsing detected GCP coordinates for alignment")
        # Override manual coordinates with detected ones
        drone_gcp_coords = detected_drone_gcp
        ifc_gcp_coords = detected_ifc_gcp
    else:
        print("\nFalling back to manual GCP coordinates")
else:
    print("\nUsing manual GCP coordinates")
    # Use the manual coordinates loaded above
    drone_gcp_coords = None  # Will be found from drone data
    # ifc_gcp_coords already loaded from manual function

def find_corresponding_drone_points(drone_points, ifc_gcp_coords, search_radii=[2.0, 5.0, 10.0]):
    """
    Find corresponding points in drone data for each GCP using multi-radius (XY) then 3D search.
    """
    tree_xy = cKDTree(drone_points[:, :2])
    drone_gcp_coords = []
    valid_pairs = []

    for i, gcp in enumerate(ifc_gcp_coords):
        matched = False
        for radius in search_radii:
            idx = tree_xy.query_ball_point(gcp[:2], radius)
            if idx:
                # Stage 2: pick best 3D match among candidates
                candidates = drone_points[idx]
                best_idx = np.argmin(np.linalg.norm(candidates - gcp, axis=1))
                best_match = candidates[best_idx]

                drone_gcp_coords.append(best_match)
                valid_pairs.append(i)
                print(f"GCP {i}: IFC {gcp[:2]} (Z={gcp[2]:.2f}) -> Drone {best_match[:2]} (Z={best_match[2]:.2f}) using radius {radius}m")
                matched = True
                break  # Stop at first successful radius
        if not matched:
            print(f"GCP {i}: No drone match found in any of the radii {search_radii}")

    return np.array(drone_gcp_coords), valid_pairs

def compute_similarity_transform(source_points, target_points):
    """Compute optimal similarity transformation using Procrustes analysis"""
    # Center the points
    source_centroid = np.mean(source_points, axis=0)
    target_centroid = np.mean(target_points, axis=0)
    
    source_centered = source_points - source_centroid
    target_centered = target_points - target_centroid
    
    # Compute scale
    source_scale = np.sqrt(np.sum(source_centered**2))
    target_scale = np.sqrt(np.sum(target_centered**2))
    scale = target_scale / source_scale if source_scale > 0 else 1.0
    
    # Normalize for rotation calculation
    source_norm = source_centered / source_scale if source_scale > 0 else source_centered
    target_norm = target_centered / target_scale if target_scale > 0 else target_centered
    
    # Compute rotation using SVD
    H = source_norm.T @ target_norm
    U, _, Vt = np.linalg.svd(H)
    R = Vt.T @ U.T
    
    # Ensure proper rotation (det(R) = 1)
    if np.linalg.det(R) < 0:
        Vt[-1, :] *= -1
        R = Vt.T @ U.T
    
    # Compute translation
    translation = target_centroid - scale * (R @ source_centroid)
    
    return R, translation, scale

def apply_similarity_transform(points, R, translation, scale):
    """Apply similarity transformation to point cloud"""
    return scale * (points @ R.T) + translation

# Find corresponding points in drone data with multi-radius search
print("Searching for corresponding drone points...")
search_radii = [2.0, 5.0, 10.0]  # Try tighter match first, fallback if needed
drone_gcp_coords, valid_pairs = find_corresponding_drone_points(
    drone_points, ifc_gcp_coords, search_radii
)

if len(valid_pairs) < 3:
    print(f"ERROR: Need at least 3 GCP pairs, found {len(valid_pairs)}")
else:
    print(f"Found {len(valid_pairs)} valid GCP pairs")

    ifc_gcp_valid = ifc_gcp_coords[valid_pairs]

    print("\nZ-coordinate analysis:")
    print(f"IFC GCP Z range: {ifc_gcp_valid[:, 2].min():.2f}m to {ifc_gcp_valid[:, 2].max():.2f}m")
    print(f"Drone GCP Z range: {drone_gcp_coords[:, 2].min():.2f}m to {drone_gcp_coords[:, 2].max():.2f}m")
    z_offset = np.median(ifc_gcp_valid[:, 2]) - np.median(drone_gcp_coords[:, 2])
    print(f"Estimated Z offset (IFC - Drone): {z_offset:.2f}m")

    print("\nComputing similarity transformation...")
    R, translation, scale = compute_similarity_transform(drone_gcp_coords, ifc_gcp_valid)

    print(f"Scale factor: {scale:.6f}")
    print(f"Translation: {translation}")
    print(f"Rotation matrix:\n{R}")

    drone_aligned = apply_similarity_transform(drone_points, R, translation, scale)
    drone_gcp_transformed = apply_similarity_transform(drone_gcp_coords, R, translation, scale)
    residuals = np.linalg.norm(drone_gcp_transformed - ifc_gcp_valid, axis=1)

    print(f"\nGCP Alignment Quality:")
    print(f"Mean residual: {np.mean(residuals):.3f}m")
    print(f"Max residual: {np.max(residuals):.3f}m")
    print(f"RMS residual: {np.sqrt(np.mean(residuals**2)):.3f}m")

    transform_params = {
        'rotation_matrix': R,
        'translation': translation,
        'scale': scale,
        'gcp_residuals': residuals,
        'rms_error': np.sqrt(np.mean(residuals**2)),
        'z_offset_estimated': z_offset
    }

def compare_alignment_methods(centroid_rmse=28.57, gcp_rmse=5.101):
    """Compare the two alignment methods"""
    
    print(f"=== ALIGNMENT METHOD COMPARISON ===")
    
    improvement = ((centroid_rmse - gcp_rmse) / centroid_rmse) * 100
    
    print(f"Centroid-Only Alignment: {centroid_rmse:.2f}m RMSE")
    print(f"GCP-Based Alignment (Z-corrected): {gcp_rmse:.2f}m RMSE")
    print(f"Improvement: {improvement:.1f}% reduction in error")
    
    # Construction monitoring implications
    print(f"\n=== CONSTRUCTION MONITORING IMPACT ===")
    
    accuracy_levels = {
        'Survey Grade': 0.1,
        'Construction Layout': 0.5, 
        'Quality Control': 2.0,
        'Progress Monitoring': 5.0,
        'General Assessment': 10.0
    }
    
    print(f"Suitability for different applications:")
    for application, required_accuracy in accuracy_levels.items():
        centroid_suitable = "Yes" if centroid_rmse <= required_accuracy else "No"
        gcp_suitable = "Yes" if gcp_rmse <= required_accuracy else "No"
        print(f"  {application:20} (≤{required_accuracy:4.1f}m): Centroid {centroid_suitable} | GCP {gcp_suitable}")

# Run comparison if transformation was successful
if 'transform_params' in locals():
    compare_alignment_methods(gcp_rmse=transform_params['rms_error'])

def save_gcp_alignment_results(drone_aligned, transform_params, output_dir, site_name, ground_method):
    """Save GCP alignment results including aligned point cloud and metadata"""
    from pathlib import Path
    import json
    
    # Create output directory
    output_path = Path(output_dir) / ground_method
    output_path.mkdir(parents=True, exist_ok=True)
    
    # Save aligned point cloud
    aligned_pcd = o3d.geometry.PointCloud()
    aligned_pcd.points = o3d.utility.Vector3dVector(drone_aligned)
    aligned_file = output_path / f"{site_name}_gcp_aligned_z_corrected.ply"
    
    success = o3d.io.write_point_cloud(str(aligned_file), aligned_pcd)
    if success:
        print(f"Saved GCP-aligned point cloud: {aligned_file}")
    else:
        print(f"Failed to save aligned point cloud: {aligned_file}")
    
    # Save transformation parameters
    transform_data = {
        'method': 'gcp_similarity_transform_z_corrected',
        'rotation_matrix': transform_params['rotation_matrix'].tolist(),
        'translation': transform_params['translation'].tolist(),
        'scale': float(transform_params['scale']),
        'gcp_residuals': transform_params['gcp_residuals'].tolist(),
        'rms_error': float(transform_params['rms_error']),
        'z_offset_estimated': float(transform_params['z_offset_estimated']),
        'num_gcp_pairs': len(transform_params['gcp_residuals']),
        'site_name': site_name,
        'ground_method': ground_method
    }
    
    transform_file = output_path / f"{site_name}_gcp_transform_params_z_corrected.json"
    with open(transform_file, 'w') as f:
        json.dump(transform_data, f, indent=2)
    
    print(f"Saved transformation parameters: {transform_file}")
    
    # Save GCP residuals as CSV for analysis
    residuals_df = pd.DataFrame({
        'gcp_id': range(len(transform_params['gcp_residuals'])),
        'residual_m': transform_params['gcp_residuals']
    })
    
    residuals_file = output_path / f"{site_name}_gcp_residuals_z_corrected.csv"
    residuals_df.to_csv(residuals_file, index=False)
    print(f"Saved GCP residuals: {residuals_file}")
    
    return {
        'aligned_file': str(aligned_file),
        'transform_file': str(transform_file),
        'residuals_file': str(residuals_file)
    }

# Save results if transformation was successful
if 'transform_params' in locals() and 'drone_aligned' in locals() and save_results:
    print("\n=== SAVING RESULTS ===")
    saved_files = save_gcp_alignment_results(
        drone_aligned, transform_params, output_dir, site_name, ground_method
    )
    
    print(f"\nSaved files:")
    for key, filepath in saved_files.items():
        print(f"  {key}: {filepath}")
else:
    print("\nSkipping save - transformation failed or save_results=False")