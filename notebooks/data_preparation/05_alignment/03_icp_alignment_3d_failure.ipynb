# Parameters (Papermill)
ground_method = "ransac_pmf"  # Ground segmentation method: csf, pmf, ransac_pmf
site_name = "trino_enel"
output_dir = "../../../data/processed/icp_alignment_advanced"
save_results = True

# Voxel downsampling parameters
voxel_size_source = 0.1  # Finer resolution for source (drone)
voxel_size_target = 0.2  # Slightly coarser for target (IFC)

voxel_size_uniform = 0.15

# ICP algorithm parameters
icp_scales = [10.0, 5.0, 2.0, 1.0]  # Start much larger
max_iterations = [20, 30, 50, 100]  # More iterations for final refinement

#icp_scales = [2.0, 1.0, 0.5]  # More conservative progression - start larger
#max_iterations_per_scale = [30, 50, 100]  # Reduced iterations - faster convergence

icp_tolerance = 1e-6
max_corr_distance = 5.0  

# Failure detection thresholds
failure_translation_threshold = 20.0        # Detect excessive translation
minimum_fitness_threshold = 0.1             # Minimum acceptable fitness
use_point_to_plane = True                   # Better for surfaces

# Workflow control flags
use_coordinate_correction = True  # Enable coordinate correction first
demonstrate_failure = True  # Show why ICP fails
compare_with_coordinate_only = True  # Compare with coordinate-only results
outlier_removal = True


# Imports
import numpy as np
import open3d as o3d
import laspy
import matplotlib.pyplot as plt
import pandas as pd
import time
import json
from pathlib import Path
from datetime import datetime
from scipy.spatial import cKDTree

# Setup
np.random.seed(42)
output_path = Path(output_dir) / ground_method
output_path.mkdir(parents=True, exist_ok=True)

print(f"ICP ALIGNMENT With Ground Segmentation File - {ground_method.upper()}")
print("=" * 50)
print(f"Site: {site_name}")
print(f"Ground method: {ground_method}")
print(f"Output: {output_path}")
print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

# File paths
coord_baseline_file = f"../../../data/processed/coordinate_alignment_final/{ground_method}/{site_name}_coordinate_aligned.ply"
drone_file = Path("../../../data/processed/trino_enel/ground_segmentation/ransac/trino_enel_nonground.ply")
ifc_file = Path("../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply")

print("Loading point clouds...")
print(f"Drone scan (non-ground): {drone_file}")
print(f"Drone exists: {drone_file.exists()}")
print(f"IFC point cloud: {ifc_file}")
print(f"IFC exists: {ifc_file.exists()}")

!ls -lh ../../../data/processed/trino_enel/ground_segmentation/ransac

import json
import numpy as np
import open3d as o3d
from pathlib import Path

def load_point_cloud(path, data_type="point cloud"):
    """Load point cloud from .las or .ply file"""
    file = Path(path)
    if not file.exists():
        raise FileNotFoundError(f"{data_type} file not found: {path}")

    ext = file.suffix.lower()
    if ext == ".las":
        points = laspy.read(file).xyz
    elif ext == ".ply":
        points = np.asarray(o3d.io.read_point_cloud(str(file)).points)
    else:
        raise ValueError(f"Unsupported file format '{ext}'. Use .las or .ply")

    print(f"Loaded {data_type}: {points.shape[0]:,} points")
    return points, points.copy()

def load_crs_metadata(pcd_file):
    """Load CRS metadata with multiple fallback strategies"""
    stem = pcd_file.stem
    base_stem = stem.split("_data_driven")[0] if "_data_driven" in stem else stem
    
    candidates = [
        pcd_file.with_name(f"{stem}_crs.json"),
        pcd_file.with_name(f"{base_stem}_crs.json"),
        pcd_file.parent / "crs_metadata.json",
        pcd_file.parent / f"{base_stem}_crs_metadata.json"
    ]
    
    for crs_file in candidates:
        if crs_file.exists():
            try:
                with open(crs_file) as f:
                    return json.load(f)
            except json.JSONDecodeError:
                print(f"Warning: Failed to parse CRS metadata in {crs_file}")
    return None

# Load point clouds
print("=== LOADING POINT CLOUDS ===")
drone_points, drone_points_original = load_point_cloud(drone_file, "drone scan")
ifc_points, ifc_points_original = load_point_cloud(ifc_file, "IFC model")

# Validate CRS compatibility
print("\n=== CRS VALIDATION ===")
drone_crs = load_crs_metadata(drone_file)
ifc_crs = load_crs_metadata(ifc_file)

drone_epsg = drone_crs.get('epsg') if drone_crs else None
ifc_epsg = ifc_crs.get('epsg') if ifc_crs else None

print(f"Drone CRS: {drone_epsg or 'Not Found'}")
print(f"IFC CRS:   {ifc_epsg or 'Not Found'}")

if drone_epsg and ifc_epsg:
    if drone_epsg != ifc_epsg:
        raise ValueError(f"CRS Mismatch: Drone EPSG {drone_epsg} ≠ IFC EPSG {ifc_epsg}")
    print("CRS match confirmed")
else:
    print("Warning: CRS metadata missing. Proceeding with assumption of matching coordinate systems.")

print(f"\nReady for alignment with {len(drone_points):,} drone + {len(ifc_points):,} IFC points")

def load_coordinate_only_results():

    coord_dir = Path("../../../data/processed/coordinate_alignment") / ground_method
    metrics_path = coord_dir / f"{site_name}_coordinate_alignment_metrics.json"
    aligned_path = coord_dir / f"{site_name}_drone_coordinate_aligned.ply"
    
    print(coord_dir)
    print(metrics_path)
    print(aligned_path)

    print("=== LOADING COORDINATE-ONLY BASELINE ===")

    if not metrics_path.exists():
        print(f"Coordinate-only results not found: {metrics_path}")
        print("Run 03a_coordinate_only_alignment.ipynb first for comparison")
        return None, None

    with open(metrics_path, 'r') as f:
        metrics = json.load(f)

    print("Coordinate-only baseline metrics:")
    print(f"  RMSE: {metrics.get('overlap_rmse', 'N/A'):.2f}m" if 'overlap_rmse' in metrics else "")
    print(f"  Good points: {metrics.get('good_pct', 'N/A'):.1f}%" if 'good_pct' in metrics else "")
    print(f"  Method: {metrics.get('alignment_method', 'Unknown')}")

    if aligned_path.exists():
        points = np.asarray(o3d.io.read_point_cloud(str(aligned_path)).points)
        print(f"  Loaded aligned points: {len(points):,}")
    else:
        print("  Aligned points file not found")
        points = None

    return metrics, points

coord_baseline_metrics, coord_baseline_points = load_coordinate_only_results()

def compute_coordinate_alignment(drone_pts, ifc_pts):
    drone_center = np.mean(drone_pts, axis=0)
    ifc_center = np.mean(ifc_pts, axis=0)
    #offset = ifc_center - drone_center
    offset = drone_center - ifc_center

    
    print("Coordinate Analysis:")
    print(f"  Drone range: X[{drone_pts[:, 0].min():.1f}, {drone_pts[:, 0].max():.1f}] "
          f"Y[{drone_pts[:, 1].min():.1f}, {drone_pts[:, 1].max():.1f}] "
          f"Z[{drone_pts[:, 2].min():.1f}, {drone_pts[:, 2].max():.1f}]")
    print(f"  IFC range:   X[{ifc_pts[:, 0].min():.1f}, {ifc_pts[:, 0].max():.1f}] "
          f"Y[{ifc_pts[:, 1].min():.1f}, {ifc_pts[:, 1].max():.1f}] "
          f"Z[{ifc_pts[:, 2].min():.1f}, {ifc_pts[:, 2].max():.1f}]")
    print(f"  Required offset: [{offset[0]:.2f}, {offset[1]:.2f}, {offset[2]:.2f}]")
    print(f"  Offset magnitude: {np.linalg.norm(offset):.2f}m")
    
    return offset

offset_vector = compute_coordinate_alignment(drone_points, ifc_points)


def prepare_icp_clouds(drone_pts, ifc_pts, offset):
    # Apply coordinate alignment: move IFC to match drone
    ifc_aligned = ifc_pts + offset
    
    # Create Open3D point clouds
    source_pcd = o3d.geometry.PointCloud()
    source_pcd.points = o3d.utility.Vector3dVector(ifc_aligned)  # source = IFC aligned
    
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(drone_pts)    # target = drone (reference)
    
    # Downsample
    source_pcd = source_pcd.voxel_down_sample(voxel_size_uniform)
    target_pcd = target_pcd.voxel_down_sample(voxel_size_uniform)

    # Print stats
    print(f"Source (IFC aligned): {len(source_pcd.points)} points")
    print(f"Target (Drone): {len(target_pcd.points)} points")

    # Recalculate offset after downsampling (if needed)
    source_center = np.mean(np.asarray(source_pcd.points), axis=0)
    target_center = np.mean(np.asarray(target_pcd.points), axis=0)
    new_offset = target_center - source_center
    print(f"Offset after downsampling: {new_offset} (Magnitude: {np.linalg.norm(new_offset):.2f}m)")

    # Remove outliers
    if outlier_removal:
        source_pcd, _ = source_pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)
        target_pcd, _ = target_pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=2.0)

    # Estimate normals (needed for point-to-plane ICP)
    if len(source_pcd.points) > 0 and len(target_pcd.points) > 0:
        print("Estimating normals...")
        source_pcd.estimate_normals(
            search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size_source * 2, max_nn=30)
        )
        target_pcd.estimate_normals(
            search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size_target * 2, max_nn=30)
        )
    else:
        print("ERROR: One of the clouds is empty after downsampling")

    return source_pcd, target_pcd


source_pcd_icp, target_pcd_icp = prepare_icp_clouds(drone_points, ifc_points, offset_vector)

# --- Step 5: Analyze point cloud alignment before ICP ---
if len(source_pcd_icp.points) > 0 and len(target_pcd_icp.points) > 0:
    ifc_center = np.mean(np.asarray(source_pcd_icp.points), axis=0)    # Source = IFC
    drone_center = np.mean(np.asarray(target_pcd_icp.points), axis=0)  # Target = Drone
    
    centroid_offset = drone_center - ifc_center
    xy_offset = np.linalg.norm(centroid_offset[:2])
    z_offset = abs(centroid_offset[2])
    total_offset = np.linalg.norm(centroid_offset)
    
    print(f"\nPre-ICP Centroid Analysis (downsampled points):")
    print(f"  IFC center:   {np.round(ifc_center, 2)}")
    print(f"  Drone center: {np.round(drone_center, 2)}")
    print(f"  Centroid offset: {np.round(centroid_offset, 2)}")
    print(f"  XY Offset: {xy_offset:.2f} m")
    print(f"  Z Offset:  {z_offset:.2f} m")
    print(f"  Total Offset: {total_offset:.2f} m")
    
    if total_offset > 50:
        print(f"\nWARNING: Large centroid separation ({total_offset:.1f}m)")
        print("This may indicate poor coordinate alignment or geometric mismatch")
    elif total_offset > 10:
        print(f"\nINFO: Moderate centroid separation ({total_offset:.1f}m)")
        print("ICP may provide some refinement")
    else:
        print(f"\nGOOD: Small centroid separation ({total_offset:.1f}m)")
        print("Point clouds are well pre-aligned")
        
    icp_ready = True
    print(f"\nReady for ICP: {icp_ready}")
else:
    print("\nCannot proceed with ICP analysis - insufficient points")
    icp_ready = False


def calculate_icp_initialization(source_pcd, target_pcd):
    source_center = np.mean(np.asarray(source_pcd.points), axis=0)
    target_center = np.mean(np.asarray(target_pcd.points), axis=0)
    actual_offset = target_center - source_center
    
    print(f"Downsampled cloud centers:")
    print(f"  Source center: {source_center}")
    print(f"  Target center: {target_center}")
    print(f"  Actual offset needed: [{actual_offset[0]:.3f}, {actual_offset[1]:.3f}, {actual_offset[2]:.3f}]")
    print(f"  Offset magnitude: {np.linalg.norm(actual_offset):.3f}m")
    
    return actual_offset

actual_offset = calculate_icp_initialization(source_pcd_icp, target_pcd_icp)



print("\n=== COORDINATE ANALYSIS ===")
print(f"Drone centroid: {np.mean(drone_points, axis=0)}")
print(f"IFC centroid: {np.mean(ifc_points, axis=0)}")
print(f"Separation distance: {np.linalg.norm(np.mean(drone_points, axis=0) - np.mean(ifc_points, axis=0)):.2f}m")

print(f"Drone bounds: {np.min(drone_points, axis=0)} to {np.max(drone_points, axis=0)}")
print(f"IFC bounds: {np.min(ifc_points, axis=0)} to {np.max(ifc_points, axis=0)}")

# See what you're trying to align:
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

# Sample points for visualization
drone_sample = np.asarray(source_pcd_icp.points)[::100]  # Every 100th point
ifc_sample = np.asarray(target_pcd_icp.points)[::50]     # Every 50th point

fig = plt.figure(figsize=(15, 5))

# Side by side view
ax1 = fig.add_subplot(131, projection='3d')
ax1.scatter(drone_sample[:, 0], drone_sample[:, 1], drone_sample[:, 2], 
           c='red', s=1, alpha=0.6, label='Drone')
ax1.set_title('Drone Points')

ax2 = fig.add_subplot(132, projection='3d')
ax2.scatter(ifc_sample[:, 0], ifc_sample[:, 1], ifc_sample[:, 2], 
           c='blue', s=1, alpha=0.6, label='IFC')
ax2.set_title('IFC Points')

# Overlay view
ax3 = fig.add_subplot(133, projection='3d')
ax3.scatter(drone_sample[:, 0], drone_sample[:, 1], drone_sample[:, 2], 
           c='red', s=1, alpha=0.4, label='Drone')
ax3.scatter(ifc_sample[:, 0], ifc_sample[:, 1], ifc_sample[:, 2], 
           c='blue', s=1, alpha=0.4, label='IFC')
ax3.set_title('Overlay')
ax3.legend()

plt.tight_layout()
plt.show()


def run_3d_icp_analysis(source_pcd, target_pcd, init_offset):
    # Estimate normals
    source_pcd.estimate_normals(
        search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=0.5, max_nn=50)
    )
    target_pcd.estimate_normals(
        search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=0.5, max_nn=50)
    )
    
    print("=== RUNNING 3D ICP WITH PROPER INITIALIZATION ===")
    
    # Initialize with measured offset
    transformation = np.eye(4)
    transformation[:3, 3] = init_offset
    
    results = []
    
    for i, scale in enumerate(icp_scales):
        print(f"\nICP Scale {i+1}/{len(icp_scales)} | max_distance = {scale:.2f}m")
        
        start_time = time.time()
        icp_result = o3d.pipelines.registration.registration_icp(
            source_pcd, target_pcd,
            max_correspondence_distance=scale,
            init=transformation,
            estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),
            criteria=o3d.pipelines.registration.ICPConvergenceCriteria(max_iteration=max_iterations[i])
        )
        icp_time = time.time() - start_time
        transformation = icp_result.transformation
        
        trans_magnitude = np.linalg.norm(icp_result.transformation[:3, 3])
        
        print(f"  Completed in {icp_time:.2f}s")
        print(f"  Fitness: {icp_result.fitness:.6f}")
        print(f"  RMSE: {icp_result.inlier_rmse:.6f}")
        print(f"  Translation: [{transformation[0, 3]:.2f}, {transformation[1, 3]:.2f}, {transformation[2, 3]:.2f}]")
        print(f"  Translation magnitude: {trans_magnitude:.2f}m")
        
        if trans_magnitude > 1000:
            print(f"  WARNING: Excessive translation - ICP failure detected")
        
        results.append({
            'scale': scale,
            'fitness': icp_result.fitness,
            'rmse': icp_result.inlier_rmse,
            'translation_magnitude': trans_magnitude,
            'time': icp_time
        })
    
    return icp_result, results

icp_result, icp_results = run_3d_icp_analysis(source_pcd_icp, target_pcd_icp, actual_offset)


print("Initial Offset Applied:", actual_offset)
print("Final Transformation:", icp_result.transformation)


print("\n=== ALIGNMENT VERIFICATION SUMMARY ===")

# Verify final alignment quality
aligned_drone_center = np.mean(np.asarray(source_pcd_icp.points), axis=0)
ifc_center_check = np.mean(ifc_points, axis=0)
final_separation = np.linalg.norm(aligned_drone_center - ifc_center_check)

print(f"Final verification:")
print(f"  Aligned drone centroid: {np.round(aligned_drone_center, 2)}")
print(f"  IFC centroid:           {np.round(ifc_center_check, 2)}")
print(f"  Final separation:       {final_separation:.3f}m")

if final_separation < 1.0:
    print("  Status: EXCELLENT coordinate-only alignment achieved")
elif final_separation < 5.0:
    print("  Status: GOOD coordinate-only alignment achieved")
else:
    print("  Status: Alignment needs improvement")

print(f"\nPoint clouds ready for ICP refinement test:")
print(f"  Source (drone): {len(source_pcd_icp.points):,} points")
print(f"  Target (IFC):   {len(target_pcd_icp.points):,} points")


o3d.visualization.draw_geometries([source_pcd_icp, target_pcd_icp])

def analyze_icp_failure(results):
    print("\n=== 3D ICP FAILURE ANALYSIS ===")
    
    final_result = results[-1]
    final_translation = final_result['translation_magnitude']
    final_fitness = final_result['fitness']
    
    print(f"Final Results:")
    print(f"  Translation magnitude: {final_translation:.1f}m")
    print(f"  Final fitness: {final_fitness:.6f}")
    
    # Determine failure mode
    if final_translation > 10000:
        failure_mode = "CATASTROPHIC FAILURE"
        reason = "Complete geometric mismatch - no meaningful correspondences found"
    elif final_translation > 1000:
        failure_mode = "SEVERE DIVERGENCE"
        reason = "ICP found wrong correspondences and diverged completely"
    elif final_fitness < 0.1:
        failure_mode = "CORRESPONDENCE FAILURE"
        reason = "Low fitness indicates poor geometric matching"
    else:
        failure_mode = "POTENTIAL SUCCESS"
        reason = "ICP achieved reasonable results"
    
    print(f"\nFailure Mode: {failure_mode}")
    print(f"Root Cause: {reason}")
    
    # Analyze progression
    print(f"\nProgression Analysis:")
    for i, result in enumerate(results):
        print(f"  Scale {i+1}: Fitness={result['fitness']:.3f}, Translation={result['translation_magnitude']:.1f}m")
    
    return failure_mode, reason

failure_mode, failure_reason = analyze_icp_failure(icp_results)

print(f"\nFailure Mode: {failure_mode}")
print(f"Root Cause: {failure_reason}")


def compare_with_baseline():
    print("\n=== COMPARISON WITH COORDINATE-ONLY BASELINE ===")
    
    # Coordinate-only achieves perfect centroid alignment
    coord_error = 0.000  # By definition
    
    final_icp_translation = icp_results[-1]['translation_magnitude']
    
    print(f"Coordinate-only centroid error: {coord_error:.3f}m")
    print(f"3D ICP final translation: {final_icp_translation:.1f}m")
    
    if final_icp_translation > 100:
        improvement_factor = -final_icp_translation  # Negative = worse
        print(f"ICP Result: {abs(improvement_factor):.0f}m WORSE than coordinate-only")
        recommendation = "USE COORDINATE-ONLY ALIGNMENT"
    else:
        improvement_factor = coord_error - final_icp_translation
        print(f"ICP improvement: {improvement_factor:.3f}m")
        recommendation = "Consider ICP refinement"
    
    print(f"Recommendation: {recommendation}")
    
    return recommendation

recommendation = compare_with_baseline()


if save_results:
    output_path = Path(output_dir) / ground_method
    output_path.mkdir(parents=True, exist_ok=True)
    
    analysis_results = {
        'failure_mode': failure_mode,
        'failure_reason': failure_reason,
        'recommendation': recommendation,
        'icp_progression': icp_results,
        'coordinate_baseline_error': 0.000,
        'final_icp_translation': icp_results[-1]['translation_magnitude'],
        'improvement_factor': -icp_results[-1]['translation_magnitude']
    }
    
    import json
    with open(output_path / f"{site_name}_3d_icp_failure_analysis.json", 'w') as f:
        json.dump(analysis_results, f, indent=2)
    
    print(f"Analysis saved to: {output_path}")

print("\n=== 3D ICP ANALYSIS COMPLETE ===")
print(f"Conclusion: {failure_mode}")
print(f"Reason: {failure_reason}")
print(f"Recommendation: {recommendation}")
