# Parameters (Papermill)
ground_method = "ransac_pmf"  # Ground segmentation method
site_name = "trino_enel"
save_results = True
quality_sample_size = 5000  # For quality assessment sampling

import numpy as np
import pandas as pd
import open3d as o3d
import laspy
from pathlib import Path
import json
from scipy.spatial import cKDTree
import warnings
warnings.filterwarnings('ignore')

# Now import from shared package
from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path

# Setup paths using shared config
output_dir = get_processed_data_path(site_name, "coordinate_alignment")

print("=== CORRECTED COORDINATE-ONLY ALIGNMENT ===")
print(f"Ground method: {ground_method}")
print(f"Site: {site_name}")
print(f"Output directory: {output_dir}")
print(f"Using metadata coordinates (corrected approach)")

# Define file paths using shared config
ground_seg_path = get_processed_data_path(site_name, "ground_segmentation") / ground_method
ifc_metadata_path = get_processed_data_path(site_name, "ifc_metadata")
ifc_pointcloud_path = get_processed_data_path(site_name, "ifc_pointclouds")

# Find the actual files
drone_file = find_latest_file(ground_seg_path, f"{site_name}_nonground.ply")
ifc_metadata_file = find_latest_file(ifc_metadata_path, "*enhanced_metadata.csv")
ifc_pointcloud_file = find_latest_file(ifc_pointcloud_path, "*data_driven.ply")  # For comparison

print("Loading data with corrected approach...")
print(f"Drone file: {drone_file}")
print(f"IFC metadata file: {ifc_metadata_file}")
print(f"IFC point cloud file: {ifc_pointcloud_file} (for comparison)")



def load_drone_points(drone_path):
    """Load drone point cloud"""
    drone_file = Path(drone_path)
    
    if not drone_file.exists():
        raise FileNotFoundError(f"Drone file not found: {drone_path}")
    
    if drone_file.suffix.lower() == ".las":
        drone_las = laspy.read(drone_file)
        drone_points = drone_las.xyz
    elif drone_file.suffix.lower() == ".ply":
        drone_pcd = o3d.io.read_point_cloud(str(drone_file))
        drone_points = np.asarray(drone_pcd.points)
    else:
        raise ValueError("Unsupported drone file format. Use .las or .ply")
    
    print(f"Loaded drone scan: {drone_points.shape[0]:,} points")
    return drone_points

# Load data using corrected approach
drone_points = load_drone_points(drone_file)

def load_ifc_points_from_metadata(metadata_csv_path):
    """Load IFC coordinates from metadata CSV (corrected approach)"""
    metadata_file = Path(metadata_csv_path)
    
    if not metadata_file.exists():
        raise FileNotFoundError(f"IFC metadata file not found: {metadata_csv_path}")
    
    # Load metadata
    df = pd.read_csv(metadata_file)
    print(f"Loaded IFC metadata: {len(df):,} records")
    
    # Extract coordinates
    coord_cols = ['X', 'Y', 'Z']
    if not all(col in df.columns for col in coord_cols):
        raise ValueError(f"Missing coordinate columns. Found: {list(df.columns)}")
    
    # Get valid coordinates
    valid_coords = df[coord_cols].dropna()
    ifc_points = valid_coords.values
    
    print(f"Valid IFC coordinates: {len(ifc_points):,} points")
    print(f"Coordinate ranges:")
    for i, col in enumerate(coord_cols):
        print(f"  {col}: {ifc_points[:, i].min():.2f} to {ifc_points[:, i].max():.2f}")
    
    return ifc_points

print(f"\n=== USING METADATA COORDINATES (CORRECTED APPROACH) ===")
ifc_points = load_ifc_points_from_metadata(ifc_metadata_file)


def load_ifc_points_from_pointcloud(ifc_ply_path):
    """Load IFC point cloud (for comparison)"""
    ifc_file = Path(ifc_ply_path)
    
    if not ifc_file.exists():
        print(f"Warning: IFC point cloud file not found: {ifc_ply_path}")
        return None
    
    ifc_pcd = o3d.io.read_point_cloud(str(ifc_file))
    ifc_points = np.asarray(ifc_pcd.points)
    
    print(f"Loaded IFC point cloud: {ifc_points.shape[0]:,} points (for comparison)")
    return ifc_points

print(f"\n=== POINT CLOUD COMPARISON (VERIFICATION) ===")
ifc_points_pointcloud = load_ifc_points_from_pointcloud(ifc_pointcloud_file)



# Validate CRS compatibility
def load_crs_metadata(pcd_file):
    """Load CRS metadata with multiple fallback strategies"""
    stem = pcd_file.stem
    base_stem = stem.split("_data_driven")[0] if "_data_driven" in stem else stem
    
    candidates = [
        pcd_file.with_name(f"{stem}_crs.json"),
        pcd_file.with_name(f"{base_stem}_crs.json"),
        pcd_file.parent / "crs_metadata.json",
        pcd_file.parent / f"{base_stem}_crs_metadata.json"
    ]
    
    for crs_file in candidates:
        if crs_file.exists():
            try:
                with open(crs_file) as f:
                    return json.load(f)
            except json.JSONDecodeError:
                print(f"Warning: Failed to parse CRS metadata in {crs_file}")
    return None
print("\n=== CRS VALIDATION ===")
drone_crs = load_crs_metadata(Path(drone_file))
ifc_crs = load_crs_metadata(Path(ifc_pointcloud_file))

drone_epsg = drone_crs.get('epsg') if drone_crs else None
ifc_epsg = ifc_crs.get('epsg') if ifc_crs else None

print(f"Drone CRS: {drone_epsg or 'Not Found'}")
print(f"IFC CRS:   {ifc_epsg or 'Not Found'}")

if drone_epsg and ifc_epsg:
    if drone_epsg != ifc_epsg:
        raise ValueError(f"CRS Mismatch: Drone EPSG {drone_epsg} ≠ IFC EPSG {ifc_epsg}")
    print("CRS match confirmed")
else:
    print("Warning: CRS metadata missing. Proceeding with assumption of matching coordinate systems.")

print(f"\nReady for alignment with {len(drone_points):,} drone + {len(ifc_points):,} IFC points")


try:
    if ifc_points_pointcloud is not None:
        # Compare centers
        metadata_center = np.mean(ifc_points, axis=0)
        pointcloud_center = np.mean(ifc_points_pointcloud, axis=0)
        center_diff = np.linalg.norm(metadata_center - pointcloud_center)
        
        print(f"Metadata center: [{metadata_center[0]:.2f}, {metadata_center[1]:.2f}, {metadata_center[2]:.2f}]")
        print(f"Point cloud center: [{pointcloud_center[0]:.2f}, {pointcloud_center[1]:.2f}, {pointcloud_center[2]:.2f}]")
        print(f"Center difference: {center_diff:.2f}m")
        
        if center_diff > 100:
            print(f"Large difference detected - using metadata coordinates")
        else:
            print(f"Centers are similar - both approaches would work")
            
except Exception as e:
    print(f"Point cloud comparison failed: {e}")
    print(f"Proceeding with metadata coordinates only")

print(f"\nData loading complete:")
print(f"  Drone points: {len(drone_points):,}")
print(f"  IFC points (from metadata): {len(ifc_points):,}")

def analyze_alignment(drone_pts, ifc_pts):
    drone_center = np.mean(drone_pts, axis=0)
    ifc_center = np.mean(ifc_pts, axis=0)
    offset = ifc_center - drone_center
    
    print("Point Cloud Analysis:")
    print(f"  Drone center: {np.round(drone_center, 2)}")
    print(f"  IFC center:   {np.round(ifc_center, 2)}")
    print(f"  Required offset: {np.round(offset, 2)}")
    print(f"  Total separation: {np.linalg.norm(offset):.2f}m")
    
    return offset

offset_vector = analyze_alignment(drone_points, ifc_points)
print(f"\nRequired coordinate offset: [{offset_vector[0]:.2f}, {offset_vector[1]:.2f}, {offset_vector[2]:.2f}]")
print(f"  → X Offset: {offset_vector[0]:.2f} m")
print(f"  → Y Offset: {offset_vector[1]:.2f} m")
print(f"  → Z Offset: {offset_vector[2]:.2f} m")


def apply_coordinate_alignment(source_pts, offset, target_pts=None):
    """
    Applies an offset to source_pts (e.g., IFC) and optionally compares to target_pts (e.g., drone)
    """
    aligned_pts = source_pts + offset

    if target_pts is not None:
        aligned_center = np.mean(aligned_pts, axis=0)
        target_center = np.mean(target_pts, axis=0)
        final_error = np.linalg.norm(aligned_center - target_center)

        print("Alignment Results:")
        print(f"  Source center after alignment: {np.round(aligned_center, 2)}")
        print(f"  Target center (reference):     {np.round(target_center, 2)}")
        print(f"  Final centroid error: {final_error:.6f}m")
        print(f"  Status: {'SUCCESS' if final_error < 0.1 else 'CHECK REQUIRED'}")

    return aligned_pts


# Apply alignment
drone_aligned = apply_coordinate_alignment(drone_points, offset_vector, target_pts=ifc_points)
#ifc_aligned = apply_coordinate_alignment(ifc_points, -offset_vector, target_pts=drone_points)


def assess_alignment_quality(drone_aligned, ifc_pts, sample_size=10000):
    # Sample for performance
    n_drone = min(sample_size, len(drone_aligned))
    n_ifc = min(sample_size, len(ifc_pts))
    
    drone_sample = drone_aligned[np.random.choice(len(drone_aligned), n_drone, replace=False)]
    ifc_sample = ifc_pts[np.random.choice(len(ifc_pts), n_ifc, replace=False)]
    
    # Nearest neighbor distances
    tree = cKDTree(ifc_sample)
    distances, _ = tree.query(drone_sample)
    
    # Calculate metrics
    rmse = np.sqrt(np.mean(distances**2))
    median_dist = np.median(distances)
    
    # Quality thresholds
    excellent_pct = np.sum(distances < 0.5) / len(distances) * 100
    good_pct = np.sum(distances < 2.0) / len(distances) * 100
    acceptable_pct = np.sum(distances < 10.0) / len(distances) * 100
    
    print("Quality Assessment:")
    print(f"  RMSE: {rmse:.2f}m")
    print(f"  Median distance: {median_dist:.2f}m")
    print(f"  Excellent (<0.5m): {excellent_pct:.1f}%")
    print(f"  Good (<2.0m): {good_pct:.1f}%")
    print(f"  Acceptable (<10.0m): {acceptable_pct:.1f}%")
    
    # Add quality warnings
    if rmse > 20.0:
        print(f"\nCRITICAL: RMSE {rmse:.1f}m indicates severe alignment failure")
        print(f"   Possible causes: coordinate system mismatch, rotation, scale issues")
    elif rmse > 10.0:
        print(f"\nWARNING: RMSE {rmse:.1f}m shows significant alignment problems")
        print(f"   Consider: rotation correction, scale adjustment, or different method")
    elif rmse > 5.0:
        print(f"\CAUTION: RMSE {rmse:.1f}m indicates moderate alignment issues")
    
    return {
        'rmse': rmse,
        'median_distance': median_dist,
        'excellent_pct': excellent_pct,
        'good_pct': good_pct,
        'acceptable_pct': acceptable_pct,
        'method': 'coordinate_only'
    }

#quality_results = assess_alignment_quality(drone_points, ifc_aligned)
quality_results = assess_alignment_quality(drone_aligned, ifc_points)



from scipy.spatial import cKDTree

def compute_deviation_map(drone_pts, ifc_pts):
    tree = cKDTree(ifc_pts)
    distances, indices = tree.query(drone_pts)
    matched_ifc_pts = ifc_pts[indices]
    deviation_vectors = drone_pts - matched_ifc_pts  # Shape: (N, 3)
    deviation_magnitude = np.linalg.norm(deviation_vectors, axis=1)
    return deviation_vectors, deviation_magnitude, matched_ifc_pts

def save_colored_deviation_pcd(points, errors, out_path, max_error=5.0):
    pcd = o3d.geometry.PointCloud()
    pcd.points = o3d.utility.Vector3dVector(points)
    
    # Normalize error to [0, 1]
    norm_errors = np.clip(errors / max_error, 0, 1)
    
    # Map to red → green colormap
    colors = np.stack([norm_errors, 1 - norm_errors, np.zeros_like(norm_errors)], axis=1)
    pcd.colors = o3d.utility.Vector3dVector(colors)

    o3d.io.write_point_cloud(str(out_path), pcd)
    print(f"Saved heatmap-colored point cloud to: {out_path}")

# Compute deviation
# deviation_vectors, deviation_magnitude, matched_ifc_pts = compute_deviation_map(
#     drone_points, ifc_aligned
# )

# Compute deviations between aligned drone points and IFC points
deviation_vectors, deviation_magnitude, matched_ifc_pts = compute_deviation_map(drone_aligned, ifc_points)

# Set output path using shared config
output_path = output_dir / ground_method
output_path.mkdir(parents=True, exist_ok=True)
heatmap_path = output_path / f"{site_name}_deviation_heatmap.ply"

# Save colored deviation point cloud (using aligned drone points)
save_colored_deviation_pcd(drone_aligned, deviation_magnitude, heatmap_path)


def run_icp_refinement(source_pts, target_pts, voxel_size=0.5):
    """
    Run point-to-point ICP to refine alignment between source and target point clouds.
    """
    source_pcd = o3d.geometry.PointCloud()
    target_pcd = o3d.geometry.PointCloud()
    source_pcd.points = o3d.utility.Vector3dVector(source_pts)
    target_pcd.points = o3d.utility.Vector3dVector(target_pts)

    # Downsample for faster ICP
    source_down = source_pcd.voxel_down_sample(voxel_size)
    target_down = target_pcd.voxel_down_sample(voxel_size)

    # Rigid ICP
    threshold = 2.0  # max distance to consider a correspondence
    icp_result = o3d.pipelines.registration.registration_icp(
        source_down, target_down, threshold, np.eye(4),
        o3d.pipelines.registration.TransformationEstimationPointToPoint()
    )
    return icp_result


#icp_result = run_icp_refinement(drone_points, ifc_aligned)
icp_result = run_icp_refinement(drone_aligned, ifc_points)
print(f"ICP fitness: {icp_result.fitness:.4f}")
print(f"ICP RMSE: {icp_result.inlier_rmse:.4f}")


def compute_z_deviation(drone_pts, ifc_pts):
    tree = cKDTree(drone_pts[:, :2])  # Only X, Y
    distances, indices = tree.query(ifc_pts[:, :2])
    
    # IFC Z vs drone Z at nearest XY
    ifc_z = ifc_pts[:, 2]
    drone_z = drone_pts[indices, 2]
    dz = drone_z - ifc_z  # Positive if panel above pile

    return dz, ifc_pts, drone_pts[indices]

#dz, ifc_matched, drone_matched = compute_z_deviation(drone_points, ifc_aligned)
dz, ifc_matched, drone_matched = compute_z_deviation(drone_aligned, ifc_points)
print(f"Z deviation range: {np.min(dz):.2f} to {np.max(dz):.2f}m")

import pandas as pd
import numpy as np
from scipy.spatial import cKDTree
from pathlib import Path

def classify_z_deviation(dz, xy_dists, nearby_counts):
    labels = []
    for d, dist, count in zip(dz, xy_dists, nearby_counts):
        if np.isnan(d) or dist > 2.0 or count < 3:
            labels.append("no_data")  # Sparse or unmatched
        elif d < -1.0:
            labels.append("under_surface")
        elif d < 0.5:
            labels.append("pile_top")
        elif d < 3.0:
            labels.append("support")
        elif d < 6.0:
            labels.append("panel")
        else:
            labels.append("above_panel")
    return labels

def compute_per_pile_deviation_csv(ifc_metadata_csv, drone_pts, output_csv_path):
    # Load IFC metadata
    df = pd.read_csv(ifc_metadata_csv)
    coord_cols = ['X', 'Y', 'Z']
    if not all(col in df.columns for col in coord_cols):
        raise ValueError("IFC metadata missing X, Y, Z columns")

    ifc_pts = df[coord_cols].dropna().values

    # --- Z OFFSET CORRECTION ---
    # Align drone Z (local) to IFC Z (global/sea-level)
    drone_xy_centroid = np.mean(drone_pts[:, :2], axis=0)
    ifc_xy_centroid = np.mean(ifc_pts[:, :2], axis=0)

    # Find Z difference at centroid
    drone_z_mean = np.mean(drone_pts[:, 2])
    ifc_z_mean = np.mean(ifc_pts[:, 2])
    z_offset = ifc_z_mean - drone_z_mean
    print(f"Applying Z offset: {z_offset:.2f}m")

    drone_pts[:, 2] += z_offset  # apply globally

    tree = cKDTree(drone_pts[:, :2])  # Match by XY

    # Nearest drone point within 2m
    dists, indices = tree.query(ifc_pts[:, :2], distance_upper_bound=2.0)

    # Assign drone Z or NaN if invalid index
    drone_z = np.array([
        drone_pts[i, 2] if (i < len(drone_pts) and not np.isinf(d)) else np.nan
        for i, d in zip(indices, dists)
    ])

    # Z deviation
    ifc_z = ifc_pts[:, 2]
    dz = drone_z - ifc_z
    dz_abs = np.abs(dz)

    # Drone point density near each pile (1m radius)
    nearby_counts = [len(tree.query_ball_point(pt[:2], r=1.0)) for pt in ifc_pts]

    # Classify each pile deviation
    dz_class = classify_z_deviation(dz, dists, nearby_counts)

    # Compose output DataFrame
    df_out = pd.DataFrame({
        'Pile_ID': df['Pile_ID'] if 'Pile_ID' in df.columns else df.index,
        'X': ifc_pts[:, 0],
        'Y': ifc_pts[:, 1],
        'IFC_Z': ifc_z,
        'Drone_Z': drone_z,
        'Z_Deviation': dz,
        'Z_Deviation_Abs': dz_abs,
        'XY_Distance': dists,
        'Drone_Points_Nearby': nearby_counts,
        'Deviation_Class': dz_class
    })

    # Save
    df_out.to_csv(output_csv_path, index=False)
    print(f"Deviation report saved to: {output_csv_path}")
    return df_out

# Run using shared config paths
ifc_metadata_csv = str(ifc_metadata_file)
z_deviation_path = get_processed_data_path(site_name, "z_deviation")
z_deviation_path.mkdir(parents=True, exist_ok=True)
output_csv = z_deviation_path / f"{site_name}_z_deviation_report.csv"

report_df = compute_per_pile_deviation_csv(ifc_metadata_csv, drone_points, str(output_csv))


import matplotlib.pyplot as plt

def plot_deviation_map(df_out, title="Deviation Class Map"):
    plt.figure(figsize=(10, 10))
    
    class_colors = {
        "no_data": 'gray',
        "under_surface": 'brown',
        "pile_top": 'green',
        "support": 'orange',
        "panel": 'blue',
        "above_panel": 'red'
    }

    for cls, color in class_colors.items():
        subset = df_out[df_out['Deviation_Class'] == cls]
        plt.scatter(subset['X'], subset['Y'], s=5, label=cls, c=color, alpha=0.6)

    plt.title(title)
    plt.xlabel("X (UTM)")
    plt.ylabel("Y (UTM)")
    plt.axis('equal')
    plt.legend(markerscale=3, fontsize=9, loc='upper right')
    plt.grid(True)
    plt.tight_layout()
    plt.show()

# Call after computing `df_out`
plot_deviation_map(report_df)


# Assuming `report_df` is your output DataFrame
summary = (
    report_df['Deviation_Class']
    .value_counts(normalize=True)
    .mul(100)
    .round(2)
    .rename('Percent')
    .to_frame()
)

summary['Count'] = report_df['Deviation_Class'].value_counts()
summary = summary[['Count', 'Percent']]
print(summary)


# For example, to get all piles classified as 'pile_top' or 'support'
mask = report_df["Deviation_Class"].isin(["pile_top", "support"])
detected_piles = report_df[mask].copy()

# Estimated pile height (if IFC_Z is pile bottom)
detected_piles["Estimated_Height"] = detected_piles["Drone_Z"] - detected_piles["IFC_Z"]

detected_piles.groupby("Deviation_Class")["Estimated_Height"].describe()


import open3d as o3d

# Convert drone point cloud to Open3D and sample for speed
drone_o3d = o3d.geometry.PointCloud()
drone_o3d.points = o3d.utility.Vector3dVector(drone_points)

# Convert IFC pile points
ifc_xyz = report_df[['X', 'Y', 'IFC_Z']].values
ifc_pcd = o3d.geometry.PointCloud()
ifc_pcd.points = o3d.utility.Vector3dVector(ifc_xyz)
ifc_pcd.paint_uniform_color([1, 0, 0])  # Red for IFC

# Visualize
o3d.visualization.draw_geometries([drone_o3d.voxel_down_sample(0.5), ifc_pcd])


if save_results:
    # Use shared config for output path
    final_output_path = output_dir / ground_method
    final_output_path.mkdir(parents=True, exist_ok=True)
    
    # Save aligned point cloud
    aligned_pcd = o3d.geometry.PointCloud()
    aligned_pcd.points = o3d.utility.Vector3dVector(drone_aligned)
    aligned_file = final_output_path / f"{site_name}_drone_aligned_to_ifc.ply"

    o3d.io.write_point_cloud(str(aligned_file), aligned_pcd)
    
    # Save transformation parameters
    transform_data = {
        'offset_vector': offset_vector.tolist(),
        'quality_metrics': quality_results,
        'method': 'coordinate_only_alignment',
        'ground_segmentation_method': ground_method,
        'site_name': site_name
    }
    
    transform_file = final_output_path / f"{site_name}_coordinate_transform.json"
    with open(transform_file, 'w') as f:
        json.dump(transform_data, f, indent=2)
    
    print(f"Results saved to: {final_output_path}")
    print(f"  - Aligned point cloud: {aligned_file.name}")
    print(f"  - Transform parameters: {transform_file.name}")

print("=== COORDINATE-ONLY ALIGNMENT COMPLETE ===")
print(f"Final RMSE: {quality_results['rmse']:.2f}m")
print(f"Method: Robust and reliable for construction monitoring")
