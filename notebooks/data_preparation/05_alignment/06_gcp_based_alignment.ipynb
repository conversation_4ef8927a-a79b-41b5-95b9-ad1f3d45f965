{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Control Point (GCP) Based Alignment\n", "\n", "This notebook implements GCP-based alignment using manually identified ground control points to achieve more accurate alignment between drone and IFC point clouds.\n", "\n", "**Key Features:**\n", "- Manual GCP identification and matching\n", "- Similarity transformation (rotation, translation, scale)\n", "- Quality assessment with residual analysis\n", "- Comparison with coordinate-only alignment\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== GROUND CONTROL POINT BASED ALIGNMENT ===\n", "Ground method: ransac_pmf\n", "Site: trino_enel\n", "Output directory: ../../../data/processed/gcp_alignment\n", "Using GCP-based similarity transformation\n"]}], "source": ["# Parameters (Papermill)\n", "ground_method = \"ransac_pmf\"  # Ground segmentation method\n", "site_name = \"trino_enel\"\n", "save_results = True\n", "quality_sample_size = 5000  # For quality assessment sampling\n", "\n", "# Option to use detected coordinates instead of manual ones\n", "use_detected_gcp = False  # Set to True to use automated detection results"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Imports\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import laspy\n", "from pathlib import Path\n", "import json\n", "from scipy.spatial import cKDTree\n", "from scipy.spatial.transform import Rotation\n", "from scipy.optimize import least_squares\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Now import from shared package\n", "from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path\n", "\n", "# Setup paths using shared config\n", "output_dir = get_processed_data_path(site_name, \"gcp_alignment\")\n", "\n", "print(\"=== GROUND CONTROL POINT BASED ALIGNMENT ===\")\n", "print(f\"Ground method: {ground_method}\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Output directory: {output_dir}\")\n", "print(f\"Using GCP-based similarity transformation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading data for GCP-based alignment...\n", "Drone file: ../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n", "IFC metadata file: ../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "IFC point cloud file: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n"]}], "source": ["# Define file paths using shared config\n", "ground_seg_path = get_processed_data_path(site_name, \"ground_segmentation\") / ground_method\n", "ifc_metadata_path = get_processed_data_path(site_name, \"ifc_metadata\")\n", "ifc_pointcloud_path = get_processed_data_path(site_name, \"ifc_pointclouds\")\n", "\n", "# Find the actual files\n", "drone_file = find_latest_file(ground_seg_path, f\"{site_name}_nonground.ply\")\n", "ifc_metadata_file = find_latest_file(ifc_metadata_path, \"*enhanced_metadata.csv\")\n", "ifc_pointcloud_file = find_latest_file(ifc_pointcloud_path, \"*data_driven.ply\")\n", "\n", "print(\"Loading data for GCP-based alignment...\")\n", "print(f\"Drone file: {drone_file}\")\n", "print(f\"IFC metadata file: {ifc_metadata_file}\")\n", "print(f\"IFC point cloud file: {ifc_pointcloud_file}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded drone scan: 504,475 points\n"]}], "source": ["def load_drone_points(drone_path):\n", "    \"\"\"Load drone point cloud\"\"\"\n", "    drone_file = Path(drone_path)\n", "    \n", "    if not drone_file.exists():\n", "        raise FileNotFoundError(f\"Drone file not found: {drone_path}\")\n", "    \n", "    if drone_file.suffix.lower() == \".las\":\n", "        drone_las = laspy.read(drone_file)\n", "        drone_points = drone_las.xyz\n", "    elif drone_file.suffix.lower() == \".ply\":\n", "        drone_pcd = o3d.io.read_point_cloud(str(drone_file))\n", "        drone_points = np.asarray(drone_pcd.points)\n", "    else:\n", "        raise ValueError(\"Unsupported drone file format. Use .las or .ply\")\n", "    \n", "    print(f\"Loaded drone scan: {drone_points.shape[0]:,} points\")\n", "    return drone_points\n", "\n", "# Load data\n", "drone_points = load_drone_points(drone_file)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading IFC metadata coordinates...\n", "Loaded IFC metadata: 14,460 records\n", "Valid IFC coordinates: 14,460 points\n", "Coordinate ranges:\n", "  X: 435267.20 to 436719.95\n", "  Y: 5010900.71 to 5012462.41\n", "  Z: 154.99 to 159.52\n"]}], "source": ["def load_ifc_points_from_metadata(metadata_csv_path):\n", "    \"\"\"Load IFC coordinates from metadata CSV\"\"\"\n", "    metadata_file = Path(metadata_csv_path)\n", "    \n", "    if not metadata_file.exists():\n", "        raise FileNotFoundError(f\"IFC metadata file not found: {metadata_csv_path}\")\n", "    \n", "    # Load metadata\n", "    df = pd.read_csv(metadata_file)\n", "    print(f\"Loaded IFC metadata: {len(df):,} records\")\n", "    \n", "    # Extract coordinates\n", "    coord_cols = ['X', 'Y', 'Z']\n", "    if not all(col in df.columns for col in coord_cols):\n", "        raise ValueError(f\"Missing coordinate columns. Found: {list(df.columns)}\")\n", "    \n", "    # Get valid coordinates\n", "    valid_coords = df[coord_cols].dropna()\n", "    ifc_points = valid_coords.values\n", "    \n", "    print(f\"Valid IFC coordinates: {len(ifc_points):,} points\")\n", "    print(f\"Coordinate ranges:\")\n", "    for i, col in enumerate(coord_cols):\n", "        print(f\"  {col}: {ifc_points[:, i].min():.2f} to {ifc_points[:, i].max():.2f}\")\n", "    \n", "    return ifc_points\n", "\n", "print(\"Loading IFC metadata coordinates...\")\n", "ifc_points = load_ifc_points_from_metadata(ifc_metadata_file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Ground Control Point Definition"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 12 ground control points\n", "   Point_ID     Easting     Northing Location\n", "0         1  435267.277  5011787.189       NE\n", "1         2  435280.920  5011792.074       NE\n", "2         3  435267.220  5011921.754       NE\n", "3         4  435280.438  5011925.654       NE\n", "4         5  436224.793  5012459.896       NW\n", "5         6  436226.921  5012459.942       NW\n", "6      4_sw  436719.919  5011825.557       SW\n", "7     4_sw2  436718.628  5011831.735       SW\n", "8      5_SM  436302.667  5011417.757       SM\n", "9     5_SM2  436305.828  5011410.901       SM\n", "10     6_SE  436112.667  5010904.990       SE\n", "11    6_SE2  436118.344  5010917.949       SE\n"]}], "source": ["def load_ground_control_points():\n", "    \"\"\"Load the ground control points from project data\"\"\"\n", "    gcp_data = {\n", "        'Point_ID': ['1', '2', '3', '4', '5', '6'],\n", "        'Easting': [435267.277, 435280.92, 435267.22, 435280.438, 436224.793, 436226.921],\n", "        'Northing': [5011787.189, 5011792.074, 5011921.754, 5011925.654, 5012459.896, 5012459.942],\n", "        'Location': ['NE', 'NE', 'NE', 'NE', 'NW', 'NW']\n", "    }\n", "    \n", "    # Additional points from project data\n", "    additional_points = {\n", "        'Point_ID': ['4_sw', '4_sw2', '5_SM', '5_SM2', '6_SE', '6_SE2'],\n", "        'Easting': [436719.919, 436718.628, 436302.667, 436305.828, 436112.667, 436118.344],\n", "        'Northing': [5011825.557, 5011831.735, 5011417.757, 5011410.901, 5010904.99, 5010917.949],\n", "        'Location': ['SW', 'SW', 'SM', 'SM', 'SE', 'SE']\n", "    }\n", "    \n", "    # <PERSON><PERSON><PERSON> all points\n", "    all_data = {}\n", "    for key in gcp_data:\n", "        all_data[key] = gcp_data[key] + additional_points[key]\n", "    \n", "    df = pd.DataFrame(all_data)\n", "    \n", "    # Convert to 3D coordinates (assuming Z=0 for ground level)\n", "    ifc_gcp_coords = np.column_stack([\n", "        df['Easting'].values,\n", "        df['Northing'].values,\n", "        np.zeros(len(df))  # Assume ground level Z=0\n", "    ])\n", "    \n", "    return df, ifc_gcp_coords\n", "\n", "gcp_df, ifc_gcp_coords = load_ground_control_points()\n", "print(f\"Loaded {len(ifc_gcp_coords)} ground control points\")\n", "print(gcp_df)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Using manual GCP coordinates\n"]}], "source": ["def load_detected_gcp_coordinates(json_file_path):\n", "    \"\"\"Load GCP coordinates from automated detection results\"\"\"\n", "    import json\n", "    from pathlib import Path\n", "    \n", "    json_file = Path(json_file_path)\n", "    \n", "    if not json_file.exists():\n", "        print(f\"Detected GCP file not found: {json_file_path}\")\n", "        print(\"Run 03_automated_gcp_detection.ipynb first to generate coordinates\")\n", "        return None, None\n", "    \n", "    with open(json_file, 'r') as f:\n", "        gcp_data = json.load(f)\n", "    \n", "    print(f\"Loading detected GCP coordinates from: {json_file.name}\")\n", "    print(f\"Detection metadata: {gcp_data['metadata']}\")\n", "    \n", "    drone_coords = []\n", "    ifc_coords = []\n", "    \n", "    for pair in gcp_data['gcp_pairs']:\n", "        drone_coords.append([\n", "            pair['drone_coordinates']['x'],\n", "            pair['drone_coordinates']['y'], \n", "            pair['drone_coordinates']['z']\n", "        ])\n", "        ifc_coords.append([\n", "            pair['ifc_coordinates']['x'],\n", "            pair['ifc_coordinates']['y'],\n", "            pair['ifc_coordinates']['z']\n", "        ])\n", "    \n", "    drone_gcp_coords = np.array(drone_coords)\n", "    ifc_gcp_coords = np.array(ifc_coords)\n", "    \n", "    print(f\"Loaded {len(drone_gcp_coords)} detected GCP pairs\")\n", "    \n", "    return drone_gcp_coords, ifc_gcp_coords\n", "\n", "\n", "if use_detected_gcp:\n", "    detected_file = f\"../../../data/processed/automated_gcp/{ground_method}/{site_name}_detected_gcp_coordinates.json\"\n", "    detected_drone_gcp, detected_ifc_gcp = load_detected_gcp_coordinates(detected_file)\n", "    \n", "    if detected_drone_gcp is not None:\n", "        print(\"\\nUsing detected GCP coordinates for alignment\")\n", "        # Override manual coordinates with detected ones\n", "        drone_gcp_coords = detected_drone_gcp\n", "        ifc_gcp_coords = detected_ifc_gcp\n", "    else:\n", "        print(\"\\nFalling back to manual GCP coordinates\")\n", "else:\n", "    print(\"\\nUsing manual GCP coordinates\")\n", "    # Use the manual coordinates loaded above\n", "    drone_gcp_coords = None  # Will be found from drone data\n", "    # ifc_gcp_coords already loaded from manual function"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## GCP Matching and Transformation"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def find_corresponding_drone_points(drone_points, ifc_gcp_coords, search_radius=5.0):\n", "    \"\"\"Find corresponding points in drone data for each ground control point\"\"\"\n", "    tree = cKDTree(drone_points[:, :2])  # XY only for initial search\n", "    \n", "    drone_gcp_coords = []\n", "    valid_pairs = []\n", "    \n", "    for i, gcp in enumerate(ifc_gcp_coords):\n", "        # Find nearest drone points within search radius\n", "        indices = tree.query_ball_point(gcp[:2], search_radius)\n", "        \n", "        if len(indices) > 0:\n", "            # Take the point with median Z value (ground level estimation)\n", "            candidates = drone_points[indices]\n", "            median_z_idx = np.argsort(candidates[:, 2])[len(candidates)//2]\n", "            best_match = candidates[median_z_idx]\n", "            \n", "            drone_gcp_coords.append(best_match)\n", "            valid_pairs.append(i)\n", "            print(f\"GCP {i}: IFC {gcp[:2]} -> Drone {best_match[:2]} (Z: {best_match[2]:.2f})\")\n", "        else:\n", "            print(f\"GCP {i}: No drone point found within {search_radius}m\")\n", "    \n", "    return np.array(drone_gcp_coords), valid_pairs"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def compute_similarity_transform(source_points, target_points):\n", "    \"\"\"Compute optimal similarity transformation using Procrustes analysis\"\"\"\n", "    # Center the points\n", "    source_centroid = np.mean(source_points, axis=0)\n", "    target_centroid = np.mean(target_points, axis=0)\n", "    \n", "    source_centered = source_points - source_centroid\n", "    target_centered = target_points - target_centroid\n", "    \n", "    # Compute scale\n", "    source_scale = np.sqrt(np.sum(source_centered**2))\n", "    target_scale = np.sqrt(np.sum(target_centered**2))\n", "    scale = target_scale / source_scale if source_scale > 0 else 1.0\n", "    \n", "    # Normalize for rotation calculation\n", "    source_norm = source_centered / source_scale if source_scale > 0 else source_centered\n", "    target_norm = target_centered / target_scale if target_scale > 0 else target_centered\n", "    \n", "    # Compute rotation using SVD\n", "    H = source_norm.T @ target_norm\n", "    U, _, Vt = np.linalg.svd(H)\n", "    R = Vt.T @ U.T\n", "    \n", "    # Ensure proper rotation (det(R) = 1)\n", "    if np.linalg.det(R) < 0:\n", "        Vt[-1, :] *= -1\n", "        R = Vt.T @ U.T\n", "    \n", "    # Compute translation\n", "    translation = target_centroid - scale * (R @ source_centroid)\n", "    \n", "    return R, translation, scale"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def apply_similarity_transform(points, R, translation, scale):\n", "    \"\"\"Apply similarity transformation to point cloud\"\"\"\n", "    return scale * (points @ R.T) + translation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Execute GCP Alignment"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Searching for corresponding drone points...\n", "GCP 0: IFC [ 435267.277 5011787.189] -> Drone [ 435269.978 5011778.19 ] (Z: 1.27)\n", "GCP 1: IFC [ 435280.92  5011792.074] -> Dr<PERSON> [ 435281.558 5011800.484] (Z: 2.66)\n", "GCP 2: IFC [ 435267.22  5011921.754] -> Drone [ 435260.108 5011925.944] (Z: 1.28)\n", "GCP 3: IFC [ 435280.438 5011925.654] -> <PERSON><PERSON> [ 435279.575 5011921.632] (Z: 2.88)\n", "GCP 4: IFC [ 436224.793 5012459.896] -> <PERSON><PERSON> [ 436228.648 5012468.201] (Z: 1.07)\n", "GCP 5: IFC [ 436226.921 5012459.942] -> <PERSON><PERSON> [ 436224.089 5012452.088] (Z: 1.05)\n", "GCP 6: <PERSON>C [ 436719.919 5011825.557] -> <PERSON><PERSON> [ 436719.97  5011821.497] (Z: 1.51)\n", "GCP 7: IFC [ 436718.628 5011831.735] -> <PERSON><PERSON> [ 436719.094 5011834.26 ] (Z: 2.93)\n", "GCP 8: <PERSON>C [ 436302.667 5011417.757] -> <PERSON><PERSON> [ 436296.253 5011420.844] (Z: 2.65)\n", "GCP 9: IFC [ 436305.828 5011410.901] -> <PERSON><PERSON> [ 436299.638 5011405.058] (Z: 0.14)\n", "GCP 10: IFC [ 436112.667 5010904.99 ] -> <PERSON><PERSON> [ 436105.63 5010911.18] (Z: 1.02)\n", "GCP 11: IFC [ 436118.344 5010917.949] -> Dr<PERSON> [ 436120.558 5010910.029] (Z: 1.02)\n", "Found 12 valid GCP pairs\n", "Computing similarity transformation...\n", "Scale factor: 0.999666\n", "Translation: [7546.40273883 1034.91443455 1043.04314163]\n", "Rotation matrix:\n", "[[ 9.99998886e-01 -1.47677475e-03 -2.17680653e-04]\n", " [ 1.47682419e-03  9.99998884e-01  2.27100430e-04]\n", " [ 2.17345034e-04 -2.27421653e-04  9.99999951e-01]]\n", "\n", "GCP Alignment Quality:\n", "Mean residual: 7.068m\n", "Max residual: 10.618m\n", "RMS residual: 7.395m\n"]}], "source": ["# Find corresponding points in drone data\n", "print(\"Searching for corresponding drone points...\")\n", "drone_gcp_coords, valid_pairs = find_corresponding_drone_points(\n", "    drone_points, ifc_gcp_coords, search_radius=10.0\n", ")\n", "\n", "if len(valid_pairs) < 3:\n", "    print(f\"ERROR: Need at least 3 GCP pairs, found {len(valid_pairs)}\")\n", "else:\n", "    print(f\"Found {len(valid_pairs)} valid GCP pairs\")\n", "    \n", "    # Use only valid pairs\n", "    ifc_gcp_valid = ifc_gcp_coords[valid_pairs]\n", "    \n", "    # Compute transformation\n", "    print(\"Computing similarity transformation...\")\n", "    R, translation, scale = compute_similarity_transform(drone_gcp_coords, ifc_gcp_valid)\n", "    \n", "    print(f\"Scale factor: {scale:.6f}\")\n", "    print(f\"Translation: {translation}\")\n", "    print(f\"Rotation matrix:\\n{R}\")\n", "    \n", "    # Apply transformation to all drone points\n", "    drone_aligned = apply_similarity_transform(drone_points, R, translation, scale)\n", "    \n", "    # Compute residuals for quality assessment\n", "    drone_gcp_transformed = apply_similarity_transform(drone_gcp_coords, R, translation, scale)\n", "    residuals = np.linalg.norm(drone_gcp_transformed - ifc_gcp_valid, axis=1)\n", "    \n", "    print(f\"\\nGCP Alignment Quality:\")\n", "    print(f\"Mean residual: {np.mean(residuals):.3f}m\")\n", "    print(f\"Max residual: {np.max(residuals):.3f}m\")\n", "    print(f\"RMS residual: {np.sqrt(np.mean(residuals**2)):.3f}m\")\n", "    \n", "    # Store transformation parameters\n", "    transform_params = {\n", "        'rotation_matrix': R,\n", "        'translation': translation,\n", "        'scale': scale,\n", "        'gcp_residuals': residuals,\n", "        'rms_error': np.sqrt(np.mean(residuals**2))\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Quality Analysis and Comparison"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ALIGNMENT METHOD COMPARISON ===\n", "Centroid-Only Alignment: 28.57m RMSE\n", "GCP-Based Alignment:     7.39m RMSE\n", "Improvement:             74.1% reduction in error\n", "\n", "=== CONSTRUCTION MONITORING IMPACT ===\n", "Suitability for different applications:\n", "  Survey Grade         (≤ 0.1m): Centroid No | GCP No\n", "  Construction Layout  (≤ 0.5m): Centroid No | GCP No\n", "  Quality Control      (≤ 2.0m): Centroid No | GCP No\n", "  Progress Monitoring  (≤ 5.0m): Centroid No | GCP No\n", "  General Assessment   (≤10.0m): Centroid No | GCP Yes\n"]}], "source": ["def compare_alignment_methods(centroid_rmse=28.57, gcp_rmse=5.101):\n", "    \"\"\"Compare the two alignment methods\"\"\"\n", "    \n", "    print(f\"=== ALIGNMENT METHOD COMPARISON ===\")\n", "    \n", "    improvement = ((centroid_rmse - gcp_rmse) / centroid_rmse) * 100\n", "    \n", "    print(f\"Centroid-Only Alignment: {centroid_rmse:.2f}m RMSE\")\n", "    print(f\"GCP-Based Alignment:     {gcp_rmse:.2f}m RMSE\")\n", "    print(f\"Improvement:             {improvement:.1f}% reduction in error\")\n", "    \n", "    # Construction monitoring implications\n", "    print(f\"\\n=== CONSTRUCTION MONITORING IMPACT ===\")\n", "    \n", "    accuracy_levels = {\n", "        'Survey Grade': 0.1,\n", "        'Construction Layout': 0.5, \n", "        'Quality Control': 2.0,\n", "        'Progress Monitoring': 5.0,\n", "        'General Assessment': 10.0\n", "    }\n", "    \n", "    print(f\"Suitability for different applications:\")\n", "    for application, required_accuracy in accuracy_levels.items():\n", "        centroid_suitable = \"Yes\" if centroid_rmse <= required_accuracy else \"No\"\n", "        gcp_suitable = \"Yes\" if gcp_rmse <= required_accuracy else \"No\"\n", "        print(f\"  {application:20} (≤{required_accuracy:4.1f}m): Centroid {centroid_suitable} | GCP {gcp_suitable}\")\n", "\n", "# Run comparison if transformation was successful\n", "if 'transform_params' in locals():\n", "    compare_alignment_methods(gcp_rmse=transform_params['rms_error'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Results"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SAVING RESULTS ===\n", "Saved GCP-aligned point cloud: ../../../data/processed/gcp_alignment/ransac_pmf/trino_enel_gcp_aligned.ply\n", "Saved transformation parameters: ../../../data/processed/gcp_alignment/ransac_pmf/trino_enel_gcp_transform_params.json\n", "Saved GCP residuals: ../../../data/processed/gcp_alignment/ransac_pmf/trino_enel_gcp_residuals.csv\n", "\n", "Saved files:\n", "  aligned_file: ../../../data/processed/gcp_alignment/ransac_pmf/trino_enel_gcp_aligned.ply\n", "  transform_file: ../../../data/processed/gcp_alignment/ransac_pmf/trino_enel_gcp_transform_params.json\n", "  residuals_file: ../../../data/processed/gcp_alignment/ransac_pmf/trino_enel_gcp_residuals.csv\n"]}], "source": ["def save_gcp_alignment_results(drone_aligned, transform_params, output_dir, site_name, ground_method):\n", "    \"\"\"Save GCP alignment results including aligned point cloud and metadata\"\"\"\n", "    from pathlib import Path\n", "    import json\n", "    \n", "    # Create output directory\n", "    output_path = Path(output_dir) / ground_method\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # Save aligned point cloud\n", "    aligned_pcd = o3d.geometry.PointCloud()\n", "    aligned_pcd.points = o3d.utility.Vector3dVector(drone_aligned)\n", "    aligned_file = output_path / f\"{site_name}_gcp_aligned.ply\"\n", "    \n", "    success = o3d.io.write_point_cloud(str(aligned_file), aligned_pcd)\n", "    if success:\n", "        print(f\"Saved GCP-aligned point cloud: {aligned_file}\")\n", "    else:\n", "        print(f\"Failed to save aligned point cloud: {aligned_file}\")\n", "    \n", "    # Save transformation parameters\n", "    transform_data = {\n", "        'method': 'gcp_similarity_transform',\n", "        'rotation_matrix': transform_params['rotation_matrix'].tolist(),\n", "        'translation': transform_params['translation'].tolist(),\n", "        'scale': float(transform_params['scale']),\n", "        'gcp_residuals': transform_params['gcp_residuals'].tolist(),\n", "        'rms_error': float(transform_params['rms_error']),\n", "        'num_gcp_pairs': len(transform_params['gcp_residuals']),\n", "        'site_name': site_name,\n", "        'ground_method': ground_method\n", "    }\n", "    \n", "    transform_file = output_path / f\"{site_name}_gcp_transform_params.json\"\n", "    with open(transform_file, 'w') as f:\n", "        json.dump(transform_data, f, indent=2)\n", "    \n", "    print(f\"Saved transformation parameters: {transform_file}\")\n", "    \n", "    # Save GCP residuals as CSV for analysis\n", "    residuals_df = pd.DataFrame({\n", "        'gcp_id': range(len(transform_params['gcp_residuals'])),\n", "        'residual_m': transform_params['gcp_residuals']\n", "    })\n", "    \n", "    residuals_file = output_path / f\"{site_name}_gcp_residuals.csv\"\n", "    residuals_df.to_csv(residuals_file, index=False)\n", "    print(f\"Saved GCP residuals: {residuals_file}\")\n", "    \n", "    return {\n", "        'aligned_file': str(aligned_file),\n", "        'transform_file': str(transform_file),\n", "        'residuals_file': str(residuals_file)\n", "    }\n", "\n", "# Save results if transformation was successful\n", "if 'transform_params' in locals() and 'drone_aligned' in locals() and save_results:\n", "    print(\"\\n=== SAVING RESULTS ===\")\n", "    saved_files = save_gcp_alignment_results(\n", "        drone_aligned, transform_params, output_dir, site_name, ground_method\n", "    )\n", "    \n", "    print(f\"\\nSaved files:\")\n", "    for key, filepath in saved_files.items():\n", "        print(f\"  {key}: {filepath}\")\n", "else:\n", "    print(\"\\nSkipping save - transformation failed or save_results=False\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualization"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== CREATING VISUALIZATION ===\n"]}, {"data": {"image/png": "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****************************************************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**********************************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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== ALIGNMENT SUMMARY ===\n", "Original drone points: 504,475\n", "IFC points: 14,460\n", "Aligned drone points: 504,475\n", "\n", "GCP Quality Metrics:\n", "  Number of GCP pairs: 12\n", "  Mean residual: 7.068m\n", "  Max residual: 10.618m\n", "  RMS residual: 7.395m\n"]}], "source": ["def visualize_gcp_alignment(drone_points, drone_aligned, ifc_points, gcp_pairs=None, sample_size=5000):\n", "    \"\"\"Visualize the GCP alignment results\"\"\"\n", "    import matplotlib.pyplot as plt\n", "    from mpl_toolkits.mplot3d import Axes3D\n", "    \n", "    # Sample points for visualization\n", "    if len(drone_points) > sample_size:\n", "        indices = np.random.choice(len(drone_points), sample_size, replace=False)\n", "        drone_sample = drone_points[indices]\n", "        drone_aligned_sample = drone_aligned[indices]\n", "    else:\n", "        drone_sample = drone_points\n", "        drone_aligned_sample = drone_aligned\n", "    \n", "    if len(ifc_points) > sample_size:\n", "        indices = np.random.choice(len(ifc_points), sample_size, replace=False)\n", "        ifc_sample = ifc_points[indices]\n", "    else:\n", "        ifc_sample = ifc_points\n", "    \n", "    # Create visualization\n", "    fig = plt.figure(figsize=(15, 5))\n", "    \n", "    # Before alignment (XY view)\n", "    ax1 = fig.add_subplot(131)\n", "    ax1.scatter(drone_sample[:, 0], drone_sample[:, 1], c='blue', alpha=0.5, s=1, label='Drone (Original)')\n", "    ax1.scatter(ifc_sample[:, 0], ifc_sample[:, 1], c='red', alpha=0.5, s=1, label='IFC')\n", "    ax1.set_xlabel('X (m)')\n", "    ax1.set_ylabel('Y (m)')\n", "    ax1.set_title('Before GCP Alignment (XY)')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # After alignment (XY view)\n", "    ax2 = fig.add_subplot(132)\n", "    ax2.scatter(drone_aligned_sample[:, 0], drone_aligned_sample[:, 1], c='green', alpha=0.5, s=1, label='Drone (GCP Aligned)')\n", "    ax2.scatter(ifc_sample[:, 0], ifc_sample[:, 1], c='red', alpha=0.5, s=1, label='IFC')\n", "    ax2.set_xlabel('X (m)')\n", "    ax2.set_ylabel('Y (m)')\n", "    ax2.set_title('After GCP Alignment (XY)')\n", "    ax2.legend()\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # GCP residuals\n", "    ax3 = fig.add_subplot(133)\n", "    if gcp_pairs is not None and len(gcp_pairs) > 0:\n", "        residuals = gcp_pairs\n", "        ax3.bar(range(len(residuals)), residuals, color='orange', alpha=0.7)\n", "        ax3.set_xlabel('GCP Pair Index')\n", "        ax3.set_ylabel('Residual (m)')\n", "        ax3.set_title('GCP Alignment Residuals')\n", "        ax3.grid(True, alpha=0.3)\n", "        \n", "        # Add statistics\n", "        mean_residual = np.mean(residuals)\n", "        max_residual = np.max(residuals)\n", "        ax3.axhline(mean_residual, color='red', linestyle='--', label=f'Mean: {mean_residual:.2f}m')\n", "        ax3.legend()\n", "    else:\n", "        ax3.text(0.5, 0.5, 'No GCP residuals available', ha='center', va='center', transform=ax3.transAxes)\n", "        ax3.set_title('GCP Residuals')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Print summary statistics\n", "    print(f\"\\n=== ALIGNMENT SUMMARY ===\")\n", "    print(f\"Original drone points: {len(drone_points):,}\")\n", "    print(f\"IFC points: {len(ifc_points):,}\")\n", "    print(f\"Aligned drone points: {len(drone_aligned):,}\")\n", "    \n", "    if gcp_pairs is not None and len(gcp_pairs) > 0:\n", "        print(f\"\\nGCP Quality Metrics:\")\n", "        print(f\"  Number of GCP pairs: {len(gcp_pairs)}\")\n", "        print(f\"  Mean residual: {np.mean(gcp_pairs):.3f}m\")\n", "        print(f\"  Max residual: {np.max(gcp_pairs):.3f}m\")\n", "        print(f\"  RMS residual: {np.sqrt(np.mean(np.array(gcp_pairs)**2)):.3f}m\")\n", "\n", "# Create visualization if alignment was successful\n", "if 'transform_params' in locals() and 'drone_aligned' in locals():\n", "    print(\"\\n=== CREATING VISUALIZATION ===\")\n", "    visualize_gcp_alignment(\n", "        drone_points, \n", "        drone_aligned, \n", "        ifc_points, \n", "        gcp_pairs=transform_params['gcp_residuals']\n", "    )\n", "else:\n", "    print(\"\\nSkipping visualization - transformation failed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== GCP-BASED ALIGNMENT COMPLETE ===\n", "\n", "Alignment successful with 12 GCP pairs\n", "RMS Error: 7.395m\n", "Scale Factor: 0.999666\n", "\n", "Results saved to: ../../../data/processed/gcp_alignment/ransac_pmf/\n", "  - Aligned point cloud: trino_enel_gcp_aligned.ply\n", "  - Transformation params: trino_enel_gcp_transform_params.json\n", "  - GCP residuals: trino_enel_gcp_residuals.csv\n"]}], "source": ["print(\"=== GCP-BASED ALIGNMENT COMPLETE ===\")\n", "\n", "if 'transform_params' in locals():\n", "    print(f\"\\nAlignment successful with {len(transform_params['gcp_residuals'])} GCP pairs\")\n", "    print(f\"RMS Error: {transform_params['rms_error']:.3f}m\")\n", "    print(f\"Scale Factor: {transform_params['scale']:.6f}\")\n", "    \n", "    if save_results:\n", "        print(f\"\\nResults saved to: {output_dir}/{ground_method}/\")\n", "        print(f\"  - Aligned point cloud: {site_name}_gcp_aligned.ply\")\n", "        print(f\"  - Transformation params: {site_name}_gcp_transform_params.json\")\n", "        print(f\"  - GCP residuals: {site_name}_gcp_residuals.csv\")\n", "    \n", "else:\n", "    print(f\"\\nAlignment failed - insufficient GCP pairs or other error\")\n", "    print(f\"Check GCP coordinates and search parameters\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Next steps**:\n", "  1. Use aligned point cloud for pile detection\n", "  2. Compare with other alignment methods\n", "  3. Validate against ground truth data"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}