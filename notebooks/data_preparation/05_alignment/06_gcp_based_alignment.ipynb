{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Control Point (GCP) Based Alignment\n", "\n", "This notebook implements GCP-based alignment using manually identified ground control points to achieve more accurate alignment between drone and IFC point clouds.\n", "\n", "**Key Features:**\n", "- Manual GCP identification and matching\n", "- Similarity transformation (rotation, translation, scale)\n", "- Quality assessment with residual analysis\n", "- Comparison with coordinate-only alignment\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path\n", "\n", "# Parameters (Papermill)\n", "ground_method = \"ransac_pmf\"  # Ground segmentation method\n", "site_name = \"trino_enel\"\n", "save_results = True\n", "quality_sample_size = 5000  # For quality assessment sampling\n", "\n", "# Option to use detected coordinates instead of manual ones\n", "use_detected_gcp = False  # Set to True to use automated detection results"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== GROUND CONTROL POINT BASED ALIGNMENT ===\n", "Ground method: ransac_pmf\n", "Site: trino_enel\n", "Output directory: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment\n", "Using GCP-based similarity transformation\n"]}], "source": ["# Imports\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import laspy\n", "from pathlib import Path\n", "import json\n", "from scipy.spatial import cKDTree\n", "from scipy.spatial.transform import Rotation\n", "from scipy.optimize import least_squares\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "\n", "# Setup paths using shared config\n", "output_dir = get_processed_data_path(site_name, \"gcp_alignment\")\n", "\n", "print(\"=== GROUND CONTROL POINT BASED ALIGNMENT ===\")\n", "print(f\"Ground method: {ground_method}\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Output directory: {output_dir}\")\n", "print(f\"Using GCP-based similarity transformation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading data for GCP-based alignment...\n", "Drone file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n", "IFC metadata file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "IFC point cloud file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n"]}], "source": ["# Define file paths using shared config\n", "ground_seg_path = get_processed_data_path(site_name, \"ground_segmentation\") / ground_method\n", "ifc_metadata_path = get_processed_data_path(site_name, \"ifc_metadata\")\n", "ifc_pointcloud_path = get_processed_data_path(site_name, \"ifc_pointclouds\")\n", "\n", "# Find the actual files\n", "drone_file = find_latest_file(ground_seg_path, f\"{site_name}_nonground.ply\")\n", "ifc_metadata_file = find_latest_file(ifc_metadata_path, \"*enhanced_metadata.csv\")\n", "ifc_pointcloud_file = find_latest_file(ifc_pointcloud_path, \"*data_driven.ply\")\n", "\n", "print(\"Loading data for GCP-based alignment...\")\n", "print(f\"Drone file: {drone_file}\")\n", "print(f\"IFC metadata file: {ifc_metadata_file}\")\n", "print(f\"IFC point cloud file: {ifc_pointcloud_file}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded drone scan: 517,002 points\n"]}], "source": ["def load_drone_points(drone_path):\n", "    \"\"\"Load drone point cloud\"\"\"\n", "    drone_file = Path(drone_path)\n", "    \n", "    if not drone_file.exists():\n", "        raise FileNotFoundError(f\"Drone file not found: {drone_path}\")\n", "    \n", "    if drone_file.suffix.lower() == \".las\":\n", "        drone_las = laspy.read(drone_file)\n", "        drone_points = drone_las.xyz\n", "    elif drone_file.suffix.lower() == \".ply\":\n", "        drone_pcd = o3d.io.read_point_cloud(str(drone_file))\n", "        drone_points = np.asarray(drone_pcd.points)\n", "    else:\n", "        raise ValueError(\"Unsupported drone file format. Use .las or .ply\")\n", "    \n", "    print(f\"Loaded drone scan: {drone_points.shape[0]:,} points\")\n", "    return drone_points\n", "\n", "# Load data\n", "drone_points = load_drone_points(drone_file)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading IFC metadata coordinates...\n", "Loaded IFC metadata: 14,460 records\n", "Valid IFC coordinates: 14,460 points\n", "Coordinate ranges:\n", "  X: 435267.20 to 436719.95\n", "  Y: 5010900.71 to 5012462.41\n", "  Z: 154.99 to 159.52\n"]}], "source": ["def load_ifc_points_from_metadata(metadata_csv_path):\n", "    \"\"\"Load IFC coordinates from metadata CSV\"\"\"\n", "    metadata_file = Path(metadata_csv_path)\n", "    \n", "    if not metadata_file.exists():\n", "        raise FileNotFoundError(f\"IFC metadata file not found: {metadata_csv_path}\")\n", "    \n", "    # Load metadata\n", "    df = pd.read_csv(metadata_file)\n", "    print(f\"Loaded IFC metadata: {len(df):,} records\")\n", "    \n", "    # Extract coordinates\n", "    coord_cols = ['X', 'Y', 'Z']\n", "    if not all(col in df.columns for col in coord_cols):\n", "        raise ValueError(f\"Missing coordinate columns. Found: {list(df.columns)}\")\n", "    \n", "    # Get valid coordinates\n", "    valid_coords = df[coord_cols].dropna()\n", "    ifc_points = valid_coords.values\n", "    \n", "    print(f\"Valid IFC coordinates: {len(ifc_points):,} points\")\n", "    print(f\"Coordinate ranges:\")\n", "    for i, col in enumerate(coord_cols):\n", "        print(f\"  {col}: {ifc_points[:, i].min():.2f} to {ifc_points[:, i].max():.2f}\")\n", "    \n", "    return ifc_points\n", "\n", "print(\"Loading IFC metadata coordinates...\")\n", "ifc_points = load_ifc_points_from_metadata(ifc_metadata_file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Ground Control Point Definition"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 12 ground control points\n", "   Point_ID     Easting     Northing Location\n", "0         1  435267.277  5011787.189       NE\n", "1         2  435280.920  5011792.074       NE\n", "2         3  435267.220  5011921.754       NE\n", "3         4  435280.438  5011925.654       NE\n", "4         5  436224.793  5012459.896       NW\n", "5         6  436226.921  5012459.942       NW\n", "6      4_sw  436719.919  5011825.557       SW\n", "7     4_sw2  436718.628  5011831.735       SW\n", "8      5_SM  436302.667  5011417.757       SM\n", "9     5_SM2  436305.828  5011410.901       SM\n", "10     6_SE  436112.667  5010904.990       SE\n", "11    6_SE2  436118.344  5010917.949       SE\n"]}], "source": ["def load_ground_control_points():\n", "    \"\"\"Load the ground control points from project data\"\"\"\n", "    gcp_data = {\n", "        'Point_ID': ['1', '2', '3', '4', '5', '6'],\n", "        'Easting': [435267.277, 435280.92, 435267.22, 435280.438, 436224.793, 436226.921],\n", "        'Northing': [5011787.189, 5011792.074, 5011921.754, 5011925.654, 5012459.896, 5012459.942],\n", "        'Location': ['NE', 'NE', 'NE', 'NE', 'NW', 'NW']\n", "    }\n", "    \n", "    # Additional points from project data\n", "    additional_points = {\n", "        'Point_ID': ['4_sw', '4_sw2', '5_SM', '5_SM2', '6_SE', '6_SE2'],\n", "        'Easting': [436719.919, 436718.628, 436302.667, 436305.828, 436112.667, 436118.344],\n", "        'Northing': [5011825.557, 5011831.735, 5011417.757, 5011410.901, 5010904.99, 5010917.949],\n", "        'Location': ['SW', 'SW', 'SM', 'SM', 'SE', 'SE']\n", "    }\n", "    \n", "    # <PERSON><PERSON><PERSON> all points\n", "    all_data = {}\n", "    for key in gcp_data:\n", "        all_data[key] = gcp_data[key] + additional_points[key]\n", "    \n", "    df = pd.DataFrame(all_data)\n", "    \n", "    # Convert to 3D coordinates (assuming Z=0 for ground level)\n", "    ifc_gcp_coords = np.column_stack([\n", "        df['Easting'].values,\n", "        df['Northing'].values,\n", "        np.zeros(len(df))  # Assume ground level Z=0\n", "    ])\n", "    \n", "    return df, ifc_gcp_coords\n", "\n", "gcp_df, ifc_gcp_coords = load_ground_control_points()\n", "print(f\"Loaded {len(ifc_gcp_coords)} ground control points\")\n", "print(gcp_df)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Using manual GCP coordinates\n"]}], "source": ["def load_detected_gcp_coordinates(json_file_path):\n", "    \"\"\"Load GCP coordinates from automated detection results\"\"\"\n", "    import json\n", "    from pathlib import Path\n", "    \n", "    json_file = Path(json_file_path)\n", "    \n", "    if not json_file.exists():\n", "        print(f\"Detected GCP file not found: {json_file_path}\")\n", "        print(\"Run 03_automated_gcp_detection.ipynb first to generate coordinates\")\n", "        return None, None\n", "    \n", "    with open(json_file, 'r') as f:\n", "        gcp_data = json.load(f)\n", "    \n", "    print(f\"Loading detected GCP coordinates from: {json_file.name}\")\n", "    print(f\"Detection metadata: {gcp_data['metadata']}\")\n", "    \n", "    drone_coords = []\n", "    ifc_coords = []\n", "    \n", "    for pair in gcp_data['gcp_pairs']:\n", "        drone_coords.append([\n", "            pair['drone_coordinates']['x'],\n", "            pair['drone_coordinates']['y'], \n", "            pair['drone_coordinates']['z']\n", "        ])\n", "        ifc_coords.append([\n", "            pair['ifc_coordinates']['x'],\n", "            pair['ifc_coordinates']['y'],\n", "            pair['ifc_coordinates']['z']\n", "        ])\n", "    \n", "    drone_gcp_coords = np.array(drone_coords)\n", "    ifc_gcp_coords = np.array(ifc_coords)\n", "    \n", "    print(f\"Loaded {len(drone_gcp_coords)} detected GCP pairs\")\n", "    \n", "    return drone_gcp_coords, ifc_gcp_coords\n", "\n", "\n", "if use_detected_gcp:\n", "    detected_file = f\"../../../data/processed/automated_gcp/{ground_method}/{site_name}_detected_gcp_coordinates.json\"\n", "    detected_drone_gcp, detected_ifc_gcp = load_detected_gcp_coordinates(detected_file)\n", "    \n", "    if detected_drone_gcp is not None:\n", "        print(\"\\nUsing detected GCP coordinates for alignment\")\n", "        # Override manual coordinates with detected ones\n", "        drone_gcp_coords = detected_drone_gcp\n", "        ifc_gcp_coords = detected_ifc_gcp\n", "    else:\n", "        print(\"\\nFalling back to manual GCP coordinates\")\n", "else:\n", "    print(\"\\nUsing manual GCP coordinates\")\n", "    # Use the manual coordinates loaded above\n", "    drone_gcp_coords = None  # Will be found from drone data\n", "    # ifc_gcp_coords already loaded from manual function"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## GCP Matching and Transformation"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def find_corresponding_drone_points(drone_points, ifc_gcp_coords, search_radius=5.0):\n", "    \"\"\"Find corresponding points in drone data for each GCP using 2-stage (XY then 3D)\"\"\"\n", "    tree_xy = cKDTree(drone_points[:, :2])  # Stage 1: XY filter\n", "    drone_gcp_coords = []\n", "    valid_pairs = []\n", "\n", "    for i, gcp in enumerate(ifc_gcp_coords):\n", "        idx = tree_xy.query_ball_point(gcp[:2], search_radius)\n", "        if not idx:\n", "            print(f\"GCP {i}: No drone match found in {search_radius}m radius\")\n", "            continue\n", "        \n", "        # Stage 2: Pick best 3D match among candidates\n", "        candidates = drone_points[idx]\n", "        best_idx = np.argmin(np.linalg.norm(candidates - gcp, axis=1))\n", "        best_match = candidates[best_idx]\n", "\n", "        drone_gcp_coords.append(best_match)\n", "        valid_pairs.append(i)\n", "        print(f\"GCP {i}: IFC {gcp[:2]} -> Drone {best_match[:2]} (Z: {best_match[2]:.2f})\")\n", "\n", "    return np.array(drone_gcp_coords), valid_pairs"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def compute_similarity_transform(source_points, target_points):\n", "    \"\"\"Compute optimal similarity transformation using Procrustes analysis\"\"\"\n", "    # Center the points\n", "    source_centroid = np.mean(source_points, axis=0)\n", "    target_centroid = np.mean(target_points, axis=0)\n", "    \n", "    source_centered = source_points - source_centroid\n", "    target_centered = target_points - target_centroid\n", "    \n", "    # Compute scale\n", "    source_scale = np.sqrt(np.sum(source_centered**2))\n", "    target_scale = np.sqrt(np.sum(target_centered**2))\n", "    scale = target_scale / source_scale if source_scale > 0 else 1.0\n", "    \n", "    # Normalize for rotation calculation\n", "    source_norm = source_centered / source_scale if source_scale > 0 else source_centered\n", "    target_norm = target_centered / target_scale if target_scale > 0 else target_centered\n", "    \n", "    # Compute rotation using SVD\n", "    H = source_norm.T @ target_norm\n", "    U, _, Vt = np.linalg.svd(H)\n", "    R = Vt.T @ U.T\n", "    \n", "    # Ensure proper rotation (det(R) = 1)\n", "    if np.linalg.det(R) < 0:\n", "        Vt[-1, :] *= -1\n", "        R = Vt.T @ U.T\n", "    \n", "    # Compute translation\n", "    translation = target_centroid - scale * (R @ source_centroid)\n", "    \n", "    return R, translation, scale"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def apply_similarity_transform(points, R, translation, scale):\n", "    \"\"\"Apply similarity transformation to point cloud\"\"\"\n", "    return scale * (points @ R.T) + translation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Execute GCP Alignment"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Searching for corresponding drone points...\n", "GCP 0: IFC [ 435267.277 5011787.189] -> <PERSON><PERSON> [ 435266.722 5011787.555] (Z: 0.68)\n", "GCP 1: IFC [ 435280.92  5011792.074] -> <PERSON><PERSON> [ 435281.72  5011792.377] (Z: 0.49)\n", "GCP 2: IFC [ 435267.22  5011921.754] -> Dr<PERSON> [ 435267.204 5011922.127] (Z: 0.50)\n", "GCP 3: IFC [ 435280.438 5011925.654] -> <PERSON><PERSON> [ 435280.115 5011925.549] (Z: 2.86)\n", "GCP 4: IFC [ 436224.793 5012459.896] -> <PERSON><PERSON> [ 436224.595 5012460.691] (Z: 0.83)\n", "GCP 5: IFC [ 436226.921 5012459.942] -> <PERSON><PERSON> [ 436227.353 5012459.799] (Z: 0.92)\n", "GCP 6: IFC [ 436719.919 5011825.557] -> <PERSON><PERSON> [ 436720.289 5011823.432] (Z: 0.68)\n", "GCP 7: IFC [ 436718.628 5011831.735] -> <PERSON><PERSON> [ 436718.271 5011831.364] (Z: 0.72)\n", "GCP 8: IFC [ 436302.667 5011417.757] -> <PERSON><PERSON> [ 436299.454 5011416.334] (Z: 0.19)\n", "GCP 9: IFC [ 436305.828 5011410.901] -> <PERSON><PERSON> [ 436305.584 5011414.021] (Z: 2.60)\n", "GCP 10: IFC [ 436112.667 5010904.99 ] -> <PERSON><PERSON> [ 436112.33  5010905.091] (Z: 1.14)\n", "GCP 11: IFC [ 436118.344 5010917.949] -> <PERSON><PERSON> [ 436117.105 5010918.004] (Z: 0.78)\n", "Found 12 valid GCP pairs\n", "Computing similarity transformation...\n", "Scale factor: 1.000242\n", "Translation: [ 3332.34060412 -1511.0293762   -574.39951513]\n", "Rotation matrix:\n", "[[ 9.99999747e-01 -6.85697721e-04 -1.89134693e-04]\n", " [ 6.85679186e-04  9.99999760e-01 -9.80481098e-05]\n", " [ 1.89201879e-04  9.79183993e-05  9.99999977e-01]]\n", "\n", "GCP Alignment Quality:\n", "Mean residual: 1.305m\n", "Max residual: 3.594m\n", "RMS residual: 1.663m\n"]}], "source": ["# Find corresponding points in drone data\n", "print(\"Searching for corresponding drone points...\")\n", "drone_gcp_coords, valid_pairs = find_corresponding_drone_points(\n", "    drone_points, ifc_gcp_coords, search_radius=10.0\n", ")\n", "\n", "if len(valid_pairs) < 3:\n", "    print(f\"ERROR: Need at least 3 GCP pairs, found {len(valid_pairs)}\")\n", "else:\n", "    print(f\"Found {len(valid_pairs)} valid GCP pairs\")\n", "    \n", "    # Use only valid pairs\n", "    ifc_gcp_valid = ifc_gcp_coords[valid_pairs]\n", "    \n", "    # Compute transformation\n", "    print(\"Computing similarity transformation...\")\n", "    R, translation, scale = compute_similarity_transform(drone_gcp_coords, ifc_gcp_valid)\n", "    \n", "    print(f\"Scale factor: {scale:.6f}\")\n", "    print(f\"Translation: {translation}\")\n", "    print(f\"Rotation matrix:\\n{R}\")\n", "    \n", "    # Apply transformation to all drone points\n", "    drone_aligned = apply_similarity_transform(drone_points, R, translation, scale)\n", "    \n", "    # Compute residuals for quality assessment\n", "    drone_gcp_transformed = apply_similarity_transform(drone_gcp_coords, R, translation, scale)\n", "    residuals = np.linalg.norm(drone_gcp_transformed - ifc_gcp_valid, axis=1)\n", "    \n", "    print(f\"\\nGCP Alignment Quality:\")\n", "    print(f\"Mean residual: {np.mean(residuals):.3f}m\")\n", "    print(f\"Max residual: {np.max(residuals):.3f}m\")\n", "    print(f\"RMS residual: {np.sqrt(np.mean(residuals**2)):.3f}m\")\n", "    \n", "    # Store transformation parameters\n", "    transform_params = {\n", "        'rotation_matrix': R,\n", "        'translation': translation,\n", "        'scale': scale,\n", "        'gcp_residuals': residuals,\n", "        'rms_error': np.sqrt(np.mean(residuals**2))\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Quality Analysis and Comparison"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ALIGNMENT METHOD COMPARISON ===\n", "Centroid-Only Alignment: 28.57m RMSE\n", "GCP-Based Alignment:     1.66m RMSE\n", "Improvement:             94.2% reduction in error\n", "\n", "=== CONSTRUCTION MONITORING IMPACT ===\n", "Suitability for different applications:\n", "  Survey Grade         (≤ 0.1m): Centroid No | GCP No\n", "  Construction Layout  (≤ 0.5m): Centroid No | GCP No\n", "  Quality Control      (≤ 2.0m): Centroid No | GCP Yes\n", "  Progress Monitoring  (≤ 5.0m): Centroid No | GCP Yes\n", "  General Assessment   (≤10.0m): Centroid No | GCP Yes\n"]}], "source": ["def compare_alignment_methods(centroid_rmse=28.57, gcp_rmse=5.101):\n", "    \"\"\"Compare the two alignment methods\"\"\"\n", "    \n", "    print(f\"=== ALIGNMENT METHOD COMPARISON ===\")\n", "    \n", "    improvement = ((centroid_rmse - gcp_rmse) / centroid_rmse) * 100\n", "    \n", "    print(f\"Centroid-Only Alignment: {centroid_rmse:.2f}m RMSE\")\n", "    print(f\"GCP-Based Alignment:     {gcp_rmse:.2f}m RMSE\")\n", "    print(f\"Improvement:             {improvement:.1f}% reduction in error\")\n", "    \n", "    # Construction monitoring implications\n", "    print(f\"\\n=== CONSTRUCTION MONITORING IMPACT ===\")\n", "    \n", "    accuracy_levels = {\n", "        'Survey Grade': 0.1,\n", "        'Construction Layout': 0.5, \n", "        'Quality Control': 2.0,\n", "        'Progress Monitoring': 5.0,\n", "        'General Assessment': 10.0\n", "    }\n", "    \n", "    print(f\"Suitability for different applications:\")\n", "    for application, required_accuracy in accuracy_levels.items():\n", "        centroid_suitable = \"Yes\" if centroid_rmse <= required_accuracy else \"No\"\n", "        gcp_suitable = \"Yes\" if gcp_rmse <= required_accuracy else \"No\"\n", "        print(f\"  {application:20} (≤{required_accuracy:4.1f}m): Centroid {centroid_suitable} | GCP {gcp_suitable}\")\n", "\n", "# Run comparison if transformation was successful\n", "if 'transform_params' in locals():\n", "    compare_alignment_methods(gcp_rmse=transform_params['rms_error'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Results"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SAVING RESULTS ===\n", "Saved GCP-aligned point cloud: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/ransac_pmf/trino_enel_gcp_aligned.ply\n", "Saved transformation parameters: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/ransac_pmf/trino_enel_gcp_transform_params.json\n", "Saved GCP residuals: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/ransac_pmf/trino_enel_gcp_residuals.csv\n", "\n", "Saved files:\n", "  aligned_file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/ransac_pmf/trino_enel_gcp_aligned.ply\n", "  transform_file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/ransac_pmf/trino_enel_gcp_transform_params.json\n", "  residuals_file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/ransac_pmf/trino_enel_gcp_residuals.csv\n"]}], "source": ["def save_gcp_alignment_results(drone_aligned, transform_params, output_dir, site_name, ground_method):\n", "    \"\"\"Save GCP alignment results including aligned point cloud and metadata\"\"\"\n", "    from pathlib import Path\n", "    import json\n", "    \n", "    # Create output directory\n", "    output_path = Path(output_dir) / ground_method\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # Save aligned point cloud\n", "    aligned_pcd = o3d.geometry.PointCloud()\n", "    aligned_pcd.points = o3d.utility.Vector3dVector(drone_aligned)\n", "    aligned_file = output_path / f\"{site_name}_gcp_aligned.ply\"\n", "    \n", "    success = o3d.io.write_point_cloud(str(aligned_file), aligned_pcd)\n", "    if success:\n", "        print(f\"Saved GCP-aligned point cloud: {aligned_file}\")\n", "    else:\n", "        print(f\"Failed to save aligned point cloud: {aligned_file}\")\n", "    \n", "    # Save transformation parameters\n", "    transform_data = {\n", "        'method': 'gcp_similarity_transform',\n", "        'rotation_matrix': transform_params['rotation_matrix'].tolist(),\n", "        'translation': transform_params['translation'].tolist(),\n", "        'scale': float(transform_params['scale']),\n", "        'gcp_residuals': transform_params['gcp_residuals'].tolist(),\n", "        'rms_error': float(transform_params['rms_error']),\n", "        'num_gcp_pairs': len(transform_params['gcp_residuals']),\n", "        'site_name': site_name,\n", "        'ground_method': ground_method\n", "    }\n", "    \n", "    transform_file = output_path / f\"{site_name}_gcp_transform_params.json\"\n", "    with open(transform_file, 'w') as f:\n", "        json.dump(transform_data, f, indent=2)\n", "    \n", "    print(f\"Saved transformation parameters: {transform_file}\")\n", "    \n", "    # Save GCP residuals as CSV for analysis\n", "    residuals_df = pd.DataFrame({\n", "        'gcp_id': range(len(transform_params['gcp_residuals'])),\n", "        'residual_m': transform_params['gcp_residuals']\n", "    })\n", "    \n", "    residuals_file = output_path / f\"{site_name}_gcp_residuals.csv\"\n", "    residuals_df.to_csv(residuals_file, index=False)\n", "    print(f\"Saved GCP residuals: {residuals_file}\")\n", "    \n", "    return {\n", "        'aligned_file': str(aligned_file),\n", "        'transform_file': str(transform_file),\n", "        'residuals_file': str(residuals_file)\n", "    }\n", "\n", "# Save results if transformation was successful\n", "if 'transform_params' in locals() and 'drone_aligned' in locals() and save_results:\n", "    print(\"\\n=== SAVING RESULTS ===\")\n", "    saved_files = save_gcp_alignment_results(\n", "        drone_aligned, transform_params, output_dir, site_name, ground_method\n", "    )\n", "    \n", "    print(f\"\\nSaved files:\")\n", "    for key, filepath in saved_files.items():\n", "        print(f\"  {key}: {filepath}\")\n", "else:\n", "    print(\"\\nSkipping save - transformation failed or save_results=False\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualization"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== CREATING VISUALIZATION ===\n"]}, {"data": {"image/png": "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*********************************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********************************************************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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== ALIGNMENT SUMMARY ===\n", "Original drone points: 517,002\n", "IFC points: 14,460\n", "Aligned drone points: 517,002\n", "\n", "GCP Quality Metrics:\n", "  Number of GCP pairs: 12\n", "  Mean residual: 1.305m\n", "  Max residual: 3.594m\n", "  RMS residual: 1.663m\n"]}], "source": ["def visualize_gcp_alignment(drone_points, drone_aligned, ifc_points, gcp_pairs=None, sample_size=5000):\n", "    \"\"\"Visualize the GCP alignment results\"\"\"\n", "    import matplotlib.pyplot as plt\n", "    from mpl_toolkits.mplot3d import Axes3D\n", "    \n", "    # Sample points for visualization\n", "    if len(drone_points) > sample_size:\n", "        indices = np.random.choice(len(drone_points), sample_size, replace=False)\n", "        drone_sample = drone_points[indices]\n", "        drone_aligned_sample = drone_aligned[indices]\n", "    else:\n", "        drone_sample = drone_points\n", "        drone_aligned_sample = drone_aligned\n", "    \n", "    if len(ifc_points) > sample_size:\n", "        indices = np.random.choice(len(ifc_points), sample_size, replace=False)\n", "        ifc_sample = ifc_points[indices]\n", "    else:\n", "        ifc_sample = ifc_points\n", "    \n", "    # Create visualization\n", "    fig = plt.figure(figsize=(15, 5))\n", "    \n", "    # Before alignment (XY view)\n", "    ax1 = fig.add_subplot(131)\n", "    ax1.scatter(drone_sample[:, 0], drone_sample[:, 1], c='blue', alpha=0.5, s=1, label='Drone (Original)')\n", "    ax1.scatter(ifc_sample[:, 0], ifc_sample[:, 1], c='red', alpha=0.5, s=1, label='IFC')\n", "    ax1.set_xlabel('X (m)')\n", "    ax1.set_ylabel('Y (m)')\n", "    ax1.set_title('Before GCP Alignment (XY)')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # After alignment (XY view)\n", "    ax2 = fig.add_subplot(132)\n", "    ax2.scatter(drone_aligned_sample[:, 0], drone_aligned_sample[:, 1], c='green', alpha=0.5, s=1, label='Drone (GCP Aligned)')\n", "    ax2.scatter(ifc_sample[:, 0], ifc_sample[:, 1], c='red', alpha=0.5, s=1, label='IFC')\n", "    ax2.set_xlabel('X (m)')\n", "    ax2.set_ylabel('Y (m)')\n", "    ax2.set_title('After GCP Alignment (XY)')\n", "    ax2.legend()\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # GCP residuals\n", "    ax3 = fig.add_subplot(133)\n", "    if gcp_pairs is not None and len(gcp_pairs) > 0:\n", "        residuals = gcp_pairs\n", "        ax3.bar(range(len(residuals)), residuals, color='orange', alpha=0.7)\n", "        ax3.set_xlabel('GCP Pair Index')\n", "        ax3.set_ylabel('Residual (m)')\n", "        ax3.set_title('GCP Alignment Residuals')\n", "        ax3.grid(True, alpha=0.3)\n", "        \n", "        # Add statistics\n", "        mean_residual = np.mean(residuals)\n", "        max_residual = np.max(residuals)\n", "        ax3.axhline(mean_residual, color='red', linestyle='--', label=f'Mean: {mean_residual:.2f}m')\n", "        ax3.legend()\n", "    else:\n", "        ax3.text(0.5, 0.5, 'No GCP residuals available', ha='center', va='center', transform=ax3.transAxes)\n", "        ax3.set_title('GCP Residuals')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Print summary statistics\n", "    print(f\"\\n=== ALIGNMENT SUMMARY ===\")\n", "    print(f\"Original drone points: {len(drone_points):,}\")\n", "    print(f\"IFC points: {len(ifc_points):,}\")\n", "    print(f\"Aligned drone points: {len(drone_aligned):,}\")\n", "    \n", "    if gcp_pairs is not None and len(gcp_pairs) > 0:\n", "        print(f\"\\nGCP Quality Metrics:\")\n", "        print(f\"  Number of GCP pairs: {len(gcp_pairs)}\")\n", "        print(f\"  Mean residual: {np.mean(gcp_pairs):.3f}m\")\n", "        print(f\"  Max residual: {np.max(gcp_pairs):.3f}m\")\n", "        print(f\"  RMS residual: {np.sqrt(np.mean(np.array(gcp_pairs)**2)):.3f}m\")\n", "\n", "# Create visualization if alignment was successful\n", "if 'transform_params' in locals() and 'drone_aligned' in locals():\n", "    print(\"\\n=== CREATING VISUALIZATION ===\")\n", "    visualize_gcp_alignment(\n", "        drone_points, \n", "        drone_aligned, \n", "        ifc_points, \n", "        gcp_pairs=transform_params['gcp_residuals']\n", "    )\n", "else:\n", "    print(\"\\nSkipping visualization - transformation failed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== GCP-BASED ALIGNMENT COMPLETE ===\n", "\n", "Alignment successful with 12 GCP pairs\n", "RMS Error: 1.663m\n", "Scale Factor: 1.000242\n", "\n", "Results saved to: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/ransac_pmf/\n", "  - Aligned point cloud: trino_enel_gcp_aligned.ply\n", "  - Transformation params: trino_enel_gcp_transform_params.json\n", "  - GCP residuals: trino_enel_gcp_residuals.csv\n"]}], "source": ["print(\"=== GCP-BASED ALIGNMENT COMPLETE ===\")\n", "\n", "if 'transform_params' in locals():\n", "    print(f\"\\nAlignment successful with {len(transform_params['gcp_residuals'])} GCP pairs\")\n", "    print(f\"RMS Error: {transform_params['rms_error']:.3f}m\")\n", "    print(f\"Scale Factor: {transform_params['scale']:.6f}\")\n", "    \n", "    if save_results:\n", "        print(f\"\\nResults saved to: {output_dir}/{ground_method}/\")\n", "        print(f\"  - Aligned point cloud: {site_name}_gcp_aligned.ply\")\n", "        print(f\"  - Transformation params: {site_name}_gcp_transform_params.json\")\n", "        print(f\"  - GCP residuals: {site_name}_gcp_residuals.csv\")\n", "    \n", "else:\n", "    print(f\"\\nAlignment failed - insufficient GCP pairs or other error\")\n", "    print(f\"Check GCP coordinates and search parameters\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Next steps**:\n", "  1. Use aligned point cloud for pile detection\n", "  2. Compare with other alignment methods\n", "  3. Validate against ground truth data"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}