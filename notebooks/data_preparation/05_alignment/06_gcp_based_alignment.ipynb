{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ground Control Point (GCP) Based Alignment\n", "\n", "This notebook implements GCP-based alignment using manually identified ground control points to achieve more accurate alignment between drone and IFC point clouds.\n", "\n", "**Key Features:**\n", "- Manual GCP identification and matching\n", "- Similarity transformation (rotation, translation, scale)\n", "- Quality assessment with residual analysis\n", "- Comparison with coordinate-only alignment\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "from pathlib import Path\n", "\n", "# Add the `notebooks` folder to sys.path\n", "notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]\n", "\n", "if str(notebooks_root) not in sys.path:\n", "    sys.path.insert(0, str(notebooks_root))\n", "\n", "# Now import from shared package\n", "from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path\n", "\n", "# Parameters (Papermill)\n", "ground_method = \"csf\"  # Ground segmentation method\n", "site_name = \"trino_enel\"\n", "save_results = True\n", "quality_sample_size = 5000  # For quality assessment sampling\n", "\n", "# Option to use detected coordinates instead of manual ones\n", "use_detected_gcp = False  # Set to True to use automated detection results"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== GROUND CONTROL POINT BASED ALIGNMENT ===\n", "Ground method: csf\n", "Site: trino_enel\n", "Output directory: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment\n", "Using GCP-based similarity transformation\n"]}], "source": ["# Imports\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "import laspy\n", "from pathlib import Path\n", "import json\n", "from scipy.spatial import cKDTree\n", "from scipy.spatial.transform import Rotation\n", "from scipy.optimize import least_squares\n", "import matplotlib.pyplot as plt\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "\n", "# Setup paths using shared config\n", "output_dir = get_processed_data_path(site_name, \"gcp_alignment\")\n", "\n", "print(\"=== GROUND CONTROL POINT BASED ALIGNMENT ===\")\n", "print(f\"Ground method: {ground_method}\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Output directory: {output_dir}\")\n", "print(f\"Using GCP-based similarity transformation\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading data for GCP-based alignment...\n", "Drone file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation/csf/trino_enel_nonground.ply\n", "IFC metadata file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "IFC point cloud file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n"]}], "source": ["# Define file paths using shared config\n", "ground_seg_path = get_processed_data_path(site_name, \"ground_segmentation\") / ground_method\n", "ifc_metadata_path = get_processed_data_path(site_name, \"ifc_metadata\")\n", "ifc_pointcloud_path = get_processed_data_path(site_name, \"ifc_pointclouds\")\n", "\n", "# Find the actual files\n", "drone_file = find_latest_file(ground_seg_path, f\"{site_name}_nonground.ply\")\n", "ifc_metadata_file = find_latest_file(ifc_metadata_path, \"*enhanced_metadata.csv\")\n", "ifc_pointcloud_file = find_latest_file(ifc_pointcloud_path, \"*data_driven.ply\")\n", "\n", "print(\"Loading data for GCP-based alignment...\")\n", "print(f\"Drone file: {drone_file}\")\n", "print(f\"IFC metadata file: {ifc_metadata_file}\")\n", "print(f\"IFC point cloud file: {ifc_pointcloud_file}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded drone scan: 13,848 points\n"]}], "source": ["def load_drone_points(drone_path):\n", "    \"\"\"Load drone point cloud\"\"\"\n", "    drone_file = Path(drone_path)\n", "    \n", "    if not drone_file.exists():\n", "        raise FileNotFoundError(f\"Drone file not found: {drone_path}\")\n", "    \n", "    if drone_file.suffix.lower() == \".las\":\n", "        drone_las = laspy.read(drone_file)\n", "        drone_points = drone_las.xyz\n", "    elif drone_file.suffix.lower() == \".ply\":\n", "        drone_pcd = o3d.io.read_point_cloud(str(drone_file))\n", "        drone_points = np.asarray(drone_pcd.points)\n", "    else:\n", "        raise ValueError(\"Unsupported drone file format. Use .las or .ply\")\n", "    \n", "    print(f\"Loaded drone scan: {drone_points.shape[0]:,} points\")\n", "    return drone_points\n", "\n", "# Load data\n", "drone_points = load_drone_points(drone_file)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading IFC metadata coordinates...\n", "Loaded IFC metadata: 14,460 records\n", "Valid IFC coordinates: 14,460 points\n", "Coordinate ranges:\n", "  X: 435267.20 to 436719.95\n", "  Y: 5010900.71 to 5012462.41\n", "  Z: 154.99 to 159.52\n"]}], "source": ["def load_ifc_points_from_metadata(metadata_csv_path):\n", "    \"\"\"Load IFC coordinates from metadata CSV\"\"\"\n", "    metadata_file = Path(metadata_csv_path)\n", "    \n", "    if not metadata_file.exists():\n", "        raise FileNotFoundError(f\"IFC metadata file not found: {metadata_csv_path}\")\n", "    \n", "    # Load metadata\n", "    df = pd.read_csv(metadata_file)\n", "    print(f\"Loaded IFC metadata: {len(df):,} records\")\n", "    \n", "    # Extract coordinates\n", "    coord_cols = ['X', 'Y', 'Z']\n", "    if not all(col in df.columns for col in coord_cols):\n", "        raise ValueError(f\"Missing coordinate columns. Found: {list(df.columns)}\")\n", "    \n", "    # Get valid coordinates\n", "    valid_coords = df[coord_cols].dropna()\n", "    ifc_points = valid_coords.values\n", "    \n", "    print(f\"Valid IFC coordinates: {len(ifc_points):,} points\")\n", "    print(f\"Coordinate ranges:\")\n", "    for i, col in enumerate(coord_cols):\n", "        print(f\"  {col}: {ifc_points[:, i].min():.2f} to {ifc_points[:, i].max():.2f}\")\n", "    \n", "    return ifc_points\n", "\n", "print(\"Loading IFC metadata coordinates...\")\n", "ifc_points = load_ifc_points_from_metadata(ifc_metadata_file)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Ground Control Point Definition"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 12 ground control points\n", "   Point_ID     Easting     Northing Location\n", "0         1  435267.277  5011787.189       NE\n", "1         2  435280.920  5011792.074       NE\n", "2         3  435267.220  5011921.754       NE\n", "3         4  435280.438  5011925.654       NE\n", "4         5  436224.793  5012459.896       NW\n", "5         6  436226.921  5012459.942       NW\n", "6      4_sw  436719.919  5011825.557       SW\n", "7     4_sw2  436718.628  5011831.735       SW\n", "8      5_SM  436302.667  5011417.757       SM\n", "9     5_SM2  436305.828  5011410.901       SM\n", "10     6_SE  436112.667  5010904.990       SE\n", "11    6_SE2  436118.344  5010917.949       SE\n"]}], "source": ["def load_ground_control_points():\n", "    \"\"\"Load the ground control points from project data\"\"\"\n", "    gcp_data = {\n", "        'Point_ID': ['1', '2', '3', '4', '5', '6'],\n", "        'Easting': [435267.277, 435280.92, 435267.22, 435280.438, 436224.793, 436226.921],\n", "        'Northing': [5011787.189, 5011792.074, 5011921.754, 5011925.654, 5012459.896, 5012459.942],\n", "        'Location': ['NE', 'NE', 'NE', 'NE', 'NW', 'NW']\n", "    }\n", "    \n", "    # Additional points from project data\n", "    additional_points = {\n", "        'Point_ID': ['4_sw', '4_sw2', '5_SM', '5_SM2', '6_SE', '6_SE2'],\n", "        'Easting': [436719.919, 436718.628, 436302.667, 436305.828, 436112.667, 436118.344],\n", "        'Northing': [5011825.557, 5011831.735, 5011417.757, 5011410.901, 5010904.99, 5010917.949],\n", "        'Location': ['SW', 'SW', 'SM', 'SM', 'SE', 'SE']\n", "    }\n", "    \n", "    # <PERSON><PERSON><PERSON> all points\n", "    all_data = {}\n", "    for key in gcp_data:\n", "        all_data[key] = gcp_data[key] + additional_points[key]\n", "    \n", "    df = pd.DataFrame(all_data)\n", "    \n", "    # Convert to 3D coordinates (assuming Z=0 for ground level)\n", "    ifc_gcp_coords = np.column_stack([\n", "        df['Easting'].values,\n", "        df['Northing'].values,\n", "        np.zeros(len(df))  # Assume ground level Z=0\n", "    ])\n", "    \n", "    return df, ifc_gcp_coords\n", "\n", "gcp_df, ifc_gcp_coords = load_ground_control_points()\n", "print(f\"Loaded {len(ifc_gcp_coords)} ground control points\")\n", "print(gcp_df)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Using manual GCP coordinates\n"]}], "source": ["def load_detected_gcp_coordinates(json_file_path):\n", "    \"\"\"Load GCP coordinates from automated detection results\"\"\"\n", "    import json\n", "    from pathlib import Path\n", "    \n", "    json_file = Path(json_file_path)\n", "    \n", "    if not json_file.exists():\n", "        print(f\"Detected GCP file not found: {json_file_path}\")\n", "        print(\"Run 03_automated_gcp_detection.ipynb first to generate coordinates\")\n", "        return None, None\n", "    \n", "    with open(json_file, 'r') as f:\n", "        gcp_data = json.load(f)\n", "    \n", "    print(f\"Loading detected GCP coordinates from: {json_file.name}\")\n", "    print(f\"Detection metadata: {gcp_data['metadata']}\")\n", "    \n", "    drone_coords = []\n", "    ifc_coords = []\n", "    \n", "    for pair in gcp_data['gcp_pairs']:\n", "        drone_coords.append([\n", "            pair['drone_coordinates']['x'],\n", "            pair['drone_coordinates']['y'], \n", "            pair['drone_coordinates']['z']\n", "        ])\n", "        ifc_coords.append([\n", "            pair['ifc_coordinates']['x'],\n", "            pair['ifc_coordinates']['y'],\n", "            pair['ifc_coordinates']['z']\n", "        ])\n", "    \n", "    drone_gcp_coords = np.array(drone_coords)\n", "    ifc_gcp_coords = np.array(ifc_coords)\n", "    \n", "    print(f\"Loaded {len(drone_gcp_coords)} detected GCP pairs\")\n", "    \n", "    return drone_gcp_coords, ifc_gcp_coords\n", "\n", "\n", "if use_detected_gcp:\n", "    detected_file = f\"../../../data/processed/automated_gcp/{ground_method}/{site_name}_detected_gcp_coordinates.json\"\n", "    detected_drone_gcp, detected_ifc_gcp = load_detected_gcp_coordinates(detected_file)\n", "    \n", "    if detected_drone_gcp is not None:\n", "        print(\"\\nUsing detected GCP coordinates for alignment\")\n", "        # Override manual coordinates with detected ones\n", "        drone_gcp_coords = detected_drone_gcp\n", "        ifc_gcp_coords = detected_ifc_gcp\n", "    else:\n", "        print(\"\\nFalling back to manual GCP coordinates\")\n", "else:\n", "    print(\"\\nUsing manual GCP coordinates\")\n", "    # Use the manual coordinates loaded above\n", "    drone_gcp_coords = None  # Will be found from drone data\n", "    # ifc_gcp_coords already loaded from manual function"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## GCP Matching and Transformation"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def find_corresponding_drone_points(drone_points, ifc_gcp_coords, search_radii=[2.0, 5.0, 10.0]):\n", "    \"\"\"\n", "    Find corresponding points in drone data for each GCP using multi-radius (XY) then 3D search.\n", "    \"\"\"\n", "    tree_xy = cKDTree(drone_points[:, :2])\n", "    drone_gcp_coords = []\n", "    valid_pairs = []\n", "\n", "    for i, gcp in enumerate(ifc_gcp_coords):\n", "        matched = False\n", "        for radius in search_radii:\n", "            idx = tree_xy.query_ball_point(gcp[:2], radius)\n", "            if idx:\n", "                # Stage 2: pick best 3D match among candidates\n", "                candidates = drone_points[idx]\n", "                best_idx = np.argmin(np.linalg.norm(candidates - gcp, axis=1))\n", "                best_match = candidates[best_idx]\n", "\n", "                drone_gcp_coords.append(best_match)\n", "                valid_pairs.append(i)\n", "                print(f\"GCP {i}: IFC {gcp[:2]} -> Drone {best_match[:2]} (Z: {best_match[2]:.2f}) using radius {radius}m\")\n", "                matched = True\n", "                break  # Stop at first successful radius\n", "        if not matched:\n", "            print(f\"GCP {i}: No drone match found in any of the radii {search_radii}\")\n", "\n", "    return np.array(drone_gcp_coords), valid_pairs\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def compute_similarity_transform(source_points, target_points):\n", "    \"\"\"Compute optimal similarity transformation using Procrustes analysis\"\"\"\n", "    # Center the points\n", "    source_centroid = np.mean(source_points, axis=0)\n", "    target_centroid = np.mean(target_points, axis=0)\n", "    \n", "    source_centered = source_points - source_centroid\n", "    target_centered = target_points - target_centroid\n", "    \n", "    # Compute scale\n", "    source_scale = np.sqrt(np.sum(source_centered**2))\n", "    target_scale = np.sqrt(np.sum(target_centered**2))\n", "    scale = target_scale / source_scale if source_scale > 0 else 1.0\n", "    \n", "    # Normalize for rotation calculation\n", "    source_norm = source_centered / source_scale if source_scale > 0 else source_centered\n", "    target_norm = target_centered / target_scale if target_scale > 0 else target_centered\n", "    \n", "    # Compute rotation using SVD\n", "    H = source_norm.T @ target_norm\n", "    U, _, Vt = np.linalg.svd(H)\n", "    R = Vt.T @ U.T\n", "    \n", "    # Ensure proper rotation (det(R) = 1)\n", "    if np.linalg.det(R) < 0:\n", "        Vt[-1, :] *= -1\n", "        R = Vt.T @ U.T\n", "    \n", "    # Compute translation\n", "    translation = target_centroid - scale * (R @ source_centroid)\n", "    \n", "    return R, translation, scale"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def apply_similarity_transform(points, R, translation, scale):\n", "    \"\"\"Apply similarity transformation to point cloud\"\"\"\n", "    return scale * (points @ R.T) + translation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Execute GCP Alignment"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Searching for corresponding drone points...\n", "GCP 0: IFC [ 435267.277 5011787.189] -> <PERSON><PERSON> [ 435260.692 5011779.803] (Z: 1.39) using radius 10.0m\n", "GCP 1: No drone match found in any of the radii [2.0, 5.0, 10.0]\n", "GCP 2: No drone match found in any of the radii [2.0, 5.0, 10.0]\n", "GCP 3: No drone match found in any of the radii [2.0, 5.0, 10.0]\n", "GCP 4: IFC [ 436224.793 5012459.896] -> <PERSON><PERSON> [ 436226.82  5012456.923] (Z: 3.45) using radius 5.0m\n", "GCP 5: IFC [ 436226.921 5012459.942] -> <PERSON><PERSON> [ 436226.82  5012456.923] (Z: 3.45) using radius 5.0m\n", "GCP 6: No drone match found in any of the radii [2.0, 5.0, 10.0]\n", "GCP 7: No drone match found in any of the radii [2.0, 5.0, 10.0]\n", "GCP 8: No drone match found in any of the radii [2.0, 5.0, 10.0]\n", "GCP 9: No drone match found in any of the radii [2.0, 5.0, 10.0]\n", "GCP 10: IFC [ 436112.667 5010904.99 ] -> <PERSON><PERSON> [ 436119.567 5010904.127] (Z: 1.20) using radius 10.0m\n", "GCP 11: IFC [ 436118.344 5010917.949] -> Dr<PERSON> [ 436119.909 5010918.707] (Z: 3.12) using radius 2.0m\n", "Found 5 valid GCP pairs\n", "Computing similarity transformation...\n", "Scale factor: 0.999576\n", "Translation: [-15720.52995562   3539.72674692   4326.6861596 ]\n", "Rotation matrix:\n", "[[ 9.99993615e-01  3.17541882e-03  1.63937785e-03]\n", " [-3.17659699e-03  9.99994698e-01  7.16564454e-04]\n", " [-1.63709376e-03 -7.21767522e-04  9.99998399e-01]]\n", "\n", "GCP Alignment Quality:\n", "Mean residual: 4.200m\n", "Max residual: 7.204m\n", "RMS residual: 4.524m\n"]}], "source": ["# Find corresponding points in drone data with multi-radius search\n", "print(\"Searching for corresponding drone points...\")\n", "search_radii = [2.0, 5.0, 10.0]  # Try tighter match first, fallback if needed\n", "drone_gcp_coords, valid_pairs = find_corresponding_drone_points(\n", "    drone_points, ifc_gcp_coords, search_radii\n", ")\n", "\n", "if len(valid_pairs) < 3:\n", "    print(f\"ERROR: Need at least 3 GCP pairs, found {len(valid_pairs)}\")\n", "else:\n", "    print(f\"Found {len(valid_pairs)} valid GCP pairs\")\n", "\n", "    ifc_gcp_valid = ifc_gcp_coords[valid_pairs]\n", "\n", "    print(\"Computing similarity transformation...\")\n", "    R, translation, scale = compute_similarity_transform(drone_gcp_coords, ifc_gcp_valid)\n", "\n", "    print(f\"Scale factor: {scale:.6f}\")\n", "    print(f\"Translation: {translation}\")\n", "    print(f\"Rotation matrix:\\n{R}\")\n", "\n", "    drone_aligned = apply_similarity_transform(drone_points, R, translation, scale)\n", "    drone_gcp_transformed = apply_similarity_transform(drone_gcp_coords, R, translation, scale)\n", "    residuals = np.linalg.norm(drone_gcp_transformed - ifc_gcp_valid, axis=1)\n", "\n", "    print(f\"\\nGCP Alignment Quality:\")\n", "    print(f\"Mean residual: {np.mean(residuals):.3f}m\")\n", "    print(f\"Max residual: {np.max(residuals):.3f}m\")\n", "    print(f\"RMS residual: {np.sqrt(np.mean(residuals**2)):.3f}m\")\n", "\n", "    transform_params = {\n", "        'rotation_matrix': R,\n", "        'translation': translation,\n", "        'scale': scale,\n", "        'gcp_residuals': residuals,\n", "        'rms_error': np.sqrt(np.mean(residuals**2))\n", "    }\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Quality Analysis and Comparison"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ALIGNMENT METHOD COMPARISON ===\n", "Centroid-Only Alignment: 28.57m RMSE\n", "GCP-Based Alignment:     4.52m RMSE\n", "Improvement:             84.2% reduction in error\n", "\n", "=== CONSTRUCTION MONITORING IMPACT ===\n", "Suitability for different applications:\n", "  Survey Grade         (≤ 0.1m): Centroid No | GCP No\n", "  Construction Layout  (≤ 0.5m): Centroid No | GCP No\n", "  Quality Control      (≤ 2.0m): Centroid No | GCP No\n", "  Progress Monitoring  (≤ 5.0m): Centroid No | GCP Yes\n", "  General Assessment   (≤10.0m): Centroid No | GCP Yes\n"]}], "source": ["def compare_alignment_methods(centroid_rmse=28.57, gcp_rmse=5.101):\n", "    \"\"\"Compare the two alignment methods\"\"\"\n", "    \n", "    print(f\"=== ALIGNMENT METHOD COMPARISON ===\")\n", "    \n", "    improvement = ((centroid_rmse - gcp_rmse) / centroid_rmse) * 100\n", "    \n", "    print(f\"Centroid-Only Alignment: {centroid_rmse:.2f}m RMSE\")\n", "    print(f\"GCP-Based Alignment:     {gcp_rmse:.2f}m RMSE\")\n", "    print(f\"Improvement:             {improvement:.1f}% reduction in error\")\n", "    \n", "    # Construction monitoring implications\n", "    print(f\"\\n=== CONSTRUCTION MONITORING IMPACT ===\")\n", "    \n", "    accuracy_levels = {\n", "        'Survey Grade': 0.1,\n", "        'Construction Layout': 0.5, \n", "        'Quality Control': 2.0,\n", "        'Progress Monitoring': 5.0,\n", "        'General Assessment': 10.0\n", "    }\n", "    \n", "    print(f\"Suitability for different applications:\")\n", "    for application, required_accuracy in accuracy_levels.items():\n", "        centroid_suitable = \"Yes\" if centroid_rmse <= required_accuracy else \"No\"\n", "        gcp_suitable = \"Yes\" if gcp_rmse <= required_accuracy else \"No\"\n", "        print(f\"  {application:20} (≤{required_accuracy:4.1f}m): Centroid {centroid_suitable} | GCP {gcp_suitable}\")\n", "\n", "# Run comparison if transformation was successful\n", "if 'transform_params' in locals():\n", "    compare_alignment_methods(gcp_rmse=transform_params['rms_error'])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Results"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SAVING RESULTS ===\n", "Saved GCP-aligned point cloud: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/csf/trino_enel_gcp_aligned.ply\n", "Saved transformation parameters: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/csf/trino_enel_gcp_transform_params.json\n", "Saved GCP residuals: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/csf/trino_enel_gcp_residuals.csv\n", "\n", "Saved files:\n", "  aligned_file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/csf/trino_enel_gcp_aligned.ply\n", "  transform_file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/csf/trino_enel_gcp_transform_params.json\n", "  residuals_file: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/csf/trino_enel_gcp_residuals.csv\n"]}], "source": ["def save_gcp_alignment_results(drone_aligned, transform_params, output_dir, site_name, ground_method):\n", "    \"\"\"Save GCP alignment results including aligned point cloud and metadata\"\"\"\n", "    from pathlib import Path\n", "    import json\n", "    \n", "    # Create output directory\n", "    output_path = Path(output_dir) / ground_method\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # Save aligned point cloud\n", "    aligned_pcd = o3d.geometry.PointCloud()\n", "    aligned_pcd.points = o3d.utility.Vector3dVector(drone_aligned)\n", "    aligned_file = output_path / f\"{site_name}_gcp_aligned.ply\"\n", "    \n", "    success = o3d.io.write_point_cloud(str(aligned_file), aligned_pcd)\n", "    if success:\n", "        print(f\"Saved GCP-aligned point cloud: {aligned_file}\")\n", "    else:\n", "        print(f\"Failed to save aligned point cloud: {aligned_file}\")\n", "    \n", "    # Save transformation parameters\n", "    transform_data = {\n", "        'method': 'gcp_similarity_transform',\n", "        'rotation_matrix': transform_params['rotation_matrix'].tolist(),\n", "        'translation': transform_params['translation'].tolist(),\n", "        'scale': float(transform_params['scale']),\n", "        'gcp_residuals': transform_params['gcp_residuals'].tolist(),\n", "        'rms_error': float(transform_params['rms_error']),\n", "        'num_gcp_pairs': len(transform_params['gcp_residuals']),\n", "        'site_name': site_name,\n", "        'ground_method': ground_method\n", "    }\n", "    \n", "    transform_file = output_path / f\"{site_name}_gcp_transform_params.json\"\n", "    with open(transform_file, 'w') as f:\n", "        json.dump(transform_data, f, indent=2)\n", "    \n", "    print(f\"Saved transformation parameters: {transform_file}\")\n", "    \n", "    # Save GCP residuals as CSV for analysis\n", "    residuals_df = pd.DataFrame({\n", "        'gcp_id': range(len(transform_params['gcp_residuals'])),\n", "        'residual_m': transform_params['gcp_residuals']\n", "    })\n", "    \n", "    residuals_file = output_path / f\"{site_name}_gcp_residuals.csv\"\n", "    residuals_df.to_csv(residuals_file, index=False)\n", "    print(f\"Saved GCP residuals: {residuals_file}\")\n", "    \n", "    return {\n", "        'aligned_file': str(aligned_file),\n", "        'transform_file': str(transform_file),\n", "        'residuals_file': str(residuals_file)\n", "    }\n", "\n", "# Save results if transformation was successful\n", "if 'transform_params' in locals() and 'drone_aligned' in locals() and save_results:\n", "    print(\"\\n=== SAVING RESULTS ===\")\n", "    saved_files = save_gcp_alignment_results(\n", "        drone_aligned, transform_params, output_dir, site_name, ground_method\n", "    )\n", "    \n", "    print(f\"\\nSaved files:\")\n", "    for key, filepath in saved_files.items():\n", "        print(f\"  {key}: {filepath}\")\n", "else:\n", "    print(\"\\nSkipping save - transformation failed or save_results=False\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualization"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== CREATING VISUALIZATION ===\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABdIAAAHqCAYAAAAAkLx0AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzsnQl4VNX5xt+EJQsJCVsWSEDCHgkCCTsqMipG3K1WUKHauldbW4VapahUKy61Li1qXQoi+McVBSJCEEX2BJCw78tAFggQEkgIIfN/*******************************+c97znffzczgcDgiCIAiCIAiCIAiCIAiCIAiC4BZ/908LgiAIgiAIgiAIgiAIgiAIgkBESBcEQRAEQRAEQRAEQRAEQRAEL4iQLgiCIAiCIAiCIAiCIAiCIAheECFdEARBEARBEARBEARBEARBELwgQrogCIIgCIIgCIIgCIIgCIIgeEGEdEEQBEEQBEEQBEEQBEEQBEHwggjpgiAIgiAIgiAIgiAIgiAIguAFEdIFQRAEQRAEQRAEQRAEQRAEwQsipAuCIAiCIAiCIAiCIAiCIAiCF0RIF4Rq8MorryAuLg6NGjVC796967s55x2/+c1vcNFFF7k85+fnh2effbbe2uRrXHvttbjvvvtq/TgDBw7EuHHjav04giAIZ8PHH3+M7t27o0mTJggPD6/v5pwXWOPx//73P/Xcnj176rVdvsL+/fsRGBiIpUuX1upxNm3ahMaNG2PDhg21ehxBEIQLjcWLF6u4x3tv41eh4VCdvgo/R36etYl8X84PREgXztsfS/MtIiICV1xxBVJSUs56v99//70SDocMGYKPPvoIL774IhoS3377La6//npERkaiadOmaNmyJS677DK89tprOH78eIXtz5w5o85j2LBhatuAgAD1o37PPfcgLS3N4/XkILBr1674/e9/j+zs7Cq379ixY+q93MfmzZtr7LzPV5YtW6YEC163qsLBOb+n48ePL3vumWeeqdDh03z66afqtbfffhvLly+Hv78/nnrqKbf7njx5stp27ty56jGP8e9//xtZWVlndX6CIAhny3/+8x/1ezRgwAC3r2/ZskUNVDp16oT//ve/eO+993Dy5En1m+rut7C2YQx+4YUXkJSUhLCwMBVvO3TogF//+tdlv6lWGF+feOIJNRkQHByMZs2aITExEX//+99d4gJjuDlGM57369cPH374IUpLS2vsmgoVrxf7R9Xh+eefV9eX/UhSVFSEzp07q8+4uLi4wvbJycnq+3Lw4EE8+OCDqm/nThwvKSlBr169VB/uxIkTiI+Px8iRI/G3v/3tHM5QEAShZtm9e7caP3IcybjGG3+vHnnkEaxfv97te9atW4e77roLsbGxKnYyxl155ZVqDMuxrMYcBzmeadu2La6++upqx/zbb79d7cM8lhLcU91+lZ6E0DcmJlKj+dWvfiXagOB7OAThPOOjjz5y8Kv9/PPPOz7++GPHtGnTHK+88orj4osvVs9/++23Z7Xf8ePHO/z9/R2nTp1yNCTOnDnj+M1vfqPOLSEhwfHss886PvjgA8e//vUvx69//WtHYGCgY/jw4S7vOXnypOOaa65R77nsssvU9eF7JkyY4OjWrZvDz8/PsX//frfX87///a9j7Nix6lp07NjRceLEiSq187333lNtiYqKcjz99NNut+F+O3To4PJcYWGh4/Tp044LDX4mvO67d++u8ntuvPFGx9VXX13h+nXq1El9rubv7tGjR9Vn0a9fP/UdIg8++KCjSZMmjg0bNrjsY8+ePY7g4GDHbbfdVvYc38P38zsjCIJQlwwePNhx0UUXqd/I7du3V3h9ypQpFV47dOiQem7ixIl12la2IS4uztGoUSPHr371K8cbb7yh4i1jdf/+/VWb2E8xs2rVKkfr1q1VzPzd736nzoe33/72t45mzZo5rrrqqrJtL7/8ckdMTIyKz7z985//dPTu3Vvtl/2WmrqmxHr9SkpKVIwpLS11XGiwT8lrX1VycnJUfJ0xY4bL899//726rvw+mJk5c6Z6/q233nKJ2UOGDKlwvV9++WW17dy5c8uemzdvnnpux44dZ3mGgiAINQfH3xxLNG/e3PHQQw853nnnHTU2/NOf/qRiD8eeHG+Y4ZiTsbNt27Yqnr3//vuO119/3XHdddep7V944YWybfl7x9iox/7PPfecIzIyUm3H38OqkJeXp+Iu2xMbG+s2tv3www/qWLzXFBcXO4qKihwXGtXtV+lr99hjj6nP6cMPP3T88Y9/VNe8VatWjszMzFppZ3X6KtQhqEfUJu70DsH3ECFdOO/Qwu/q1atdnj9y5IgaxIwePfqs9nvPPfeoAWxNwR9zCtrnyj/+8Q91vo8//rjbAHHw4EHHSy+95PLcI488ot7Dzoi7YEMR1yqkW68nOz583joo9AQF+1tuuUW1kwK8OySwnL2Qnp2d7WjcuLHqZFpxN1B/4IEHVOd07dq1Zc8dO3bMER0dXWGgfv311zvCwsLUd8nM73//e/V5XYgiiiAI9cOuXbvU79mXX37paNOmTQUBknAAzW04yKttIb2goMDja5wE7tmzp+o7/Pzzz263mT9/vssgn4Jpu3btlACwefPmCttnZWU5Jk2aVPaYYi5FXTOc4Ka4zuNygF8T15TUx0TE+SKkc4IjKCjIkZ+fX+E19ksDAgIcW7du9TjRTf7v//5PfQbvvvtu2XN79+5Vn/Ptt9/usk9+7i1atJDJbkEQ6h1O6PF3qkePHhXGEjpWcpJ53759Zc8tX75cjVOGDh3qOH78eIX3cFzKMaqGv40c35pZv369et6aZOQJCrvUChYtWqTet3jx4ioJ6RcqZyukf/bZZ26THyZPnuyob0RIF6qKCOnCeYcn4ZdiH2fBx4wZ4/I8BykUlOPj49VAJiIiwnH//fcr4V3D/VlvOngz+DNbmxlnTZs2VT+MTz31VIWZaT4/cuRIx3fffedITExUx9JCNgdNf/jDH9TAl/tgBjHFb/MAyh0cLIeHh6sBHQXwqkCBnIKrOaPtbK7nnDlz1PPmbABPcKDHjIBZs2Y5Vq5cqd63dOnSKgUWdwGagVhfQ153ZjVwG+siG92p+uqrr9Q14rXl55ySkuKynX4vB7F33nmn+p4wG/CZZ55R3xt27G644QZHaGioEjdeffXVCm3n5/23v/1NfXY8Dj/LJ598ssL3oCpt0u2x3ryJ6uz8cRtrNoe7gfqyZcvU58HJECv8jLgfZokQCit8zE6OldmzZ6vX1qxZ47FdgiAINQlFZAqEXGHDrLYuXbq4vM4YYv3tZGxx95tqji0UrW+99Va1b/5WMsbwN85dPOTgmsem6MwY7AlONHN762S2N7gt3/PJJ59UaXt3Qjph9jv3c+DAgXO+phrrNdPXwxyb2G/hNpyUpXA8bNgwx8aNGysMTvV7OcHACXbGXGYr3nTTTSp7213/Scd+Zq9xgkILGV988YV6zM+tb9++bmNSdT7fytrk7jtWmajOZAJeC08T4WzXFVdc4TLR7e48rr32WrUt30PYN+F30F0m38033+zo1auX13YJgiDUNhxX83dyxYoVVX4PV05zvMoxZFVwJ6QT/o57imlWbDab+o0lFP3vu+++Kgnp7savhw8fdtx1111q7MhkJOoP69atc9EQ9Hs5yWC329XKYv6bbf7zn//sMrZnnOV7mWj19ttvq6Q0xliO5zlO5XiVegQn4hkjGRtyc3MrtJ8T95ycYGwLCQlR52tdiVyVNun2eOtXVVVI5/H5PL8nZnh8JjJSm9HjZa7os/Lmm2+q13g9GA8Z3819KHd9FV4v9n14vXRfhe2w9lXc6Que9vn111+r68n+D9tLjYKfiVWjcfd94So09l/4mfA7wz4N3QWEhot4pAvnLXl5eTh8+DAOHTqEjRs34qGHHkJBQYHyWTPzwAMP4Mknn1SelW+88YbyCP/kk08wYsQInD59uqxo2aWXXqq82fhv3ug/Tn73u98pH8q+ffvi9ddfx+WXX45//OMfuOOOOyq0aevWrRg1ahSuuuoqdSwWLKW/GN8zffp0jBkzBm+++aZqC72q//SnP3k9x59//ll5pXKf9BmrCvSJp5/m3XffjXNh586d6r5Vq1aVbjtz5kzl73rdddehf//+yreW1/hsWLt2La655hrk5ubiueeew29/+1vlO/r11197vEYPP/yw+jxefvll5Ul66623qvdboV8tPWVfeukl5WNKL9p//etf6vNq166d8gmnnyl9a3/66aey9/E9N9xwA1599VXlU//WW2/hpptuUt8H7rO6bbrlllvUZ0q4D/2da9OmjVdPdX4W9N11xz//+U/lRcjvO28xMTHq+lm57bbblLcqvQF37dqFP/zhDxg8eLB6jxX69ZLaLpwmCIKgYezgbyT9ovk7uX37dqxevbrsdf5m33zzzerfU6ZMUb+djz/+uPo34Wv6N5X7IewjsIAyPTr/8pe/qNoijFn8Hf/qq68qtIG/3yzoyNjP7b3VLiHWfoc3vvnmGwQFBSnP0HOBv9/sF1Sl0Gpl17Q6sO/C2EI/eBZo79Kli+pP0bvbHY8++ih++eUXTJw4UfXTeM3ooWtlx44dGD16tIqx7GMdPXpU/Ztt5+fLa8zjsm9Cj1uzP3x1P9/K2sTvGGMovc31d+npp5/2eE3Yl+T1ZD/RHfSIZb/jhx9+UMemp/9jjz2GPn36uPVmp586z3n27Nnq+8L3RkVFuY3R9FR3VydHEAShrpgzZ44aP1W1BgfHxqmpqWqs3b59+7M+LuMEb1UZq7IWBX+D9fiL959//rnb+hWVwfjD+MTx79ixY1WNlMzMTPVvd9DrnXGS7eRYkroA4xRjgRXGPMYBxoo///nP+PHHH1XMY02s7777To3f7r//fhW3OF41w1jFMV5ISIga006YMEH1ZYYOHVqhEGdlbeKY1Fu/qjroY7do0cKlTgzj9sKFC1X8pW7C7xDH/YzBGtbBYbyk1z6fZz+A+srKlSu9HpP9N57/JZdcovoqcXFxylPfU1+lKrBuCq8t9Ru2lzG4sn4iWbBggfq+8fz5uTCms/6NjK8bOPWt5AtCTaNnCK03ZiD973//c9l2yZIlbjO/mDVufV7PzprRM8v0MDXzxBNPqOe5NMyawcR9m+FsKPe7bds2l+f/8pe/qIwk8zI3K1wGx31yBtQMZz653Mp80/YbzLLie8yWHlW5ngsXLlT7YUb7p59+qrzMOIPL2eLKoHc7M701f/3rX9XMttX7vCoZ6bQZ4Sy6OcuOfq7MWnCXkc4ZYbNH6C+//OLiO2qebTbPhPMaMqucmdvmbEKuHuB5m2er6fNGz3h+n8wwU96afV/VNlXX2oXZBZyB9waXg+u/B+t3xgyz2vmdbNmypVrimJGR4XFbngszGAVBEGqbtLQ09fu1YMEC9Zhxjb/TXNFlRv+mV9XahVlojFPmFUTcN33DzZlsOh7y97Yqq8D69OnjNmOddjDm+ExfVg2zjS+55BJHVWEmdPfu3cv2xcxr+o+ynYyXNXVNq5KRTtsZxmJmcJuhVYxeGWB975VXXuliD8Y+Cvs+tBqz9p+4mspsicPnGI/NGYs6zpmzBav7+ValTdWxdmG8t8Z4KzwebdW4Hb153VnAaLgqjtsxRrvzTLeuiOBKQEEQhPqA8Y2/Q9a4oMdU5lioLU/1uMhdHPIEt2cdEe6HK4j4u8fffj7/2muvVfp+/q4ynmgbGY7L+V6uIK5uRjpXSXEbc0YxV2uxZpm7jHQ+x8xla//BPK7TGeBcCWeORVwFz+fZbzCPq0eNGqXGaDruMaawP2LNsmfcZsa8+fmqtulsrV24iprvpc0PdZHOnTur8Tbrw2j4WTKzm5n9Zu644w7VXv1dYca8u1V5Zqx9FX4/eG240s0cP6lPWPsq1clId2fZyxVm1C3M/Q/r94Xfc66Gr6q7gNAwkIx04bzl3//+t5rh443Z3ldccYXKHv/yyy/Ltvnss88QFhamMo6Zva5vnEHkjCJnpr0xb948dW/NHOcMMZk7d67L8x07dlSzu2bYBma7cxbS3AZWJOdssDnz2YrOMmJbzWRkZKiZYvNNZzvr94SGhqI6sD3cD6umM5Oax2QmFzO1vcEq7GyPnuEn/DfPcf78+dVqA68HZ6aZRcZq7BrOUCcnJ3tsNzPgNb169ULz5s1Vtp4Vfj80zORjRh37Zpz91jC7r1u3bi7v52fYo0cPlZ1m/gyHDx+uXrd+j6rTpqrCz9c8k++O1q1bq3tmpjP7wBPMamcm3pEjR9R3u2fPnh631d9bQRCE2oaZWJGRkSqeEz8/P7Xq59NPP1Xx4Wzg79yiRYtURld+fn7Z7zd/UxmvmZ194MABl/fcd999VVoFxnhrjc+E2cvm+MxMa/N7qhuft2zZUrYvxiKuimLW2Ycfflin15QZhFzxxox9M8yc8wQz53hMDftDPO7evXtdtmO22aBBg8oe68xGxllzxqJ+XsfTs/l8q9qmqqL7X95iNI/XsmVL9W+ep7vvjeaPf/yj6jdwReK7777r0lYz+ngSowVBqC88jVUJs27NsZBjd/N7qhsLP/jgA7UfrvJhLGBGL8cx/M2sSixk3NTH5Goq6gFns4KameFNmjRRfQWNv78/HnnkEY/vefDBB10eM+64Gxdy5TC1C2vM46qsxo0buzzPbHod36iH6FXs5rEq+zLc1p3mUdU2VZd7771XfU4cy3OVOV0EmM3er18/9TrH3l988YXK6ue/ze1l3Ob2a9asKRuX2+32aq2io5bAa8O+iTl+VuV74g2uJtTo/gavGVdYsJ/mCZ4DM+H5GQm+gwjpDQSKpfyx4A8K/6A92VR4gz80XHrTtWtXZUFCgZNLiS5UaCFCwZK3O++8U4naHIhxeZBepsUBFH+MGXCtwjNtYHJycrweg4MqBkYKuWa4xJY/itZBF4V0K2wDA671+Gw38dYGHezZVjNsj55EsFq4ULDVP/BnMzHBQMtlYAyk1kkBd3ASg0uouWSKS7N5CwwMxEUXXVTtzgmvRWFhYYXrTdw9R9wtCeTgkkv9KtuWHRW2VQvQ5ufN7+dnyKXj1s+Qf4u63WfbpupgJGS4h583l75xEoDffy7984buzHAyobJjehrEC8KFgMTvuoFCJsVdCr67d+8uiyccAHIJMEXcs4H74PXnEl/rbzgnFN39hruL5Z5itDU+EwrNOkZTxLbG6OrGZ8ZT7ouDQ1qHZWVlqaX01thV29dU93ms8ZgCsScR2RoP9XbWeOguPhNO7rt7Xr//bD7fqrapJmM0kzy4FJ8T15ycX7JkicdtKXzQ9oWD9osvvrjS40mMFgShvvA0ViWcCNQJbzUxVr3xxhvLYiGtPShk0o6EY3Vv0PaL1qG0VtVxkDcK/Yyl1bXHYiyMjo5WiUtVGatyrGm176zOWLUqsZBjVT35bI2F33//fYU4WJ02VRfanfBzYkIebW2pxZg/I9ryUvSnjYy1rbTgJbq9HM9ykoa6Dyc/OFlRmSWK7qtwezPcf2VJad6gFkCbG157foe5P23tx3P0BPuE7P8zKZC2cZxooDYkNGzKp62EeoWzUPRo4h/O2XhLEXoZ84eQg/GEhASVhcObYMAfaA4W6VnFYMLBBz3MKKJ7EnS9eVKbqeogxTxTqWEbmBE/btw4t+/RYqw7mAFN6IHJzoOGAUUL8RxUu3sPs8TpIVZVGKAqE1XdDeLoD8fvNycxrDAIsmPlLfPqXPGUNehuQOtu26q8n58h/+boQ+4Oa+emOm2qKvSw89a5YQYkxZVVq1Yp4YS/E+yMsNN4LrCjU5lYIwjnMxK/6wZmFdNjlL9fvFlhHKe/ZXXRXtr0EvU0OWwd/LqL5e5gvF23bp3KCDOv3mJc17Gdg1V37+GEJz3LqwInq3XMbwjXtDpUNR562q6y95/N51vTMVr783qK0Xqim9mPTFZgtjm92SnsMKvxbNHHkxgtCEJ9QVGRojLHqlZ0NrXVn5u/ycyu5li1OlCEPJtYqIV81p7gzQqzo7WAWxtUtc5ZTcRCZn67q6lhzmavbpuqC/u5+nPiKnNmbDN7n6ulOWbWbaUI7clXnnGScBUea9BxwoPiMz8reshTrHdXC6y6eNJ4rCv2OB6mjzwFdNZu48pz9u+YOU+x31y3xQq1KPb7uFKftex4++ijj9Qkw9SpU8/5HITaQYT0BgJnoDxZU5BTp04pIYyiJP9QmbHCYgScKdUzqSz4wCDFjNPqZExdSHDJsXlWnD9ynLWmmFjVgbHVAoM/jBTm+UOuYSYXPydPhR/NsA1sz9kEfi4XYgeFA2AW+Kpsxp3we8bgyE7DuRYcrQwWQOFyKwYU8/XRAzwun2b2ZlULsTHQMCgxS8CKu+fqCn6GLExms9lqLPOruvuh+MLOgzvS0tLUigIuYWOxM/5G/N///Z9asseBurXzVFUoDlHssX62gnAhIfG7bqCoyxigl35bs3mZ2fTOO+94jOWeflO5WopQsDybOOwNFthmfGbbPU2WW+HqhuXLl6vfc7MlWkO8plZ0n4fx2PwdprVJTWSxnQ219flWJ0Yzg5DXkFn/7mCROE5osHgoszdpzcPvATMpKytS5g0ej/1CbwkZgiAItQ0tU95//32VzMPErMpgJjczpznZu3///goJSTUJheYZM2aoZDurLRmZNGmSipXVEdIZCzkpSoHYnJVe32NVwphfU7Gwpsa8LK7J/gZXYrLPwURGxkKK1VVpK5MJaEnHG8elTGrhvqiNWJMVzH0V6je6j6Az4a19FZ2hzv67uXi71XVg8eLFqq/DvhOL5Go8xX0rTJxg3OeN2hK/i1yxwdV0nlYyCPWLWLv4CLQj4cCKAzJ6TtMfi55SepkOl4Pyh4CzcRy8cJkv/Z4lo62c06dPq4w//lBp4Y+emfyRZpB0J7rzR9Mb1157rbo3V48mOjOZHYfKYBv42brzC+fxtfjvDgZnDs4pwHCw5S5byvocOyOc9eW14GDNCn+8OXijAH6uaFuXJ598Er/61a9cbmwDl1RVx96FEwAMqBTfWV3d3DHh7G19wc+QojIrh1uhFc3ZVADndSOVfQc19FRl8Ld61/H7/cADD6hsEP0957752fN78/rrr+NsSU9PV/eDBw8+630IwvmOxO9zh7+jHJxQmLbGEt54jZnV+80333jchx7MWn9TOajkpAYHLBQzrXBgdS6xgaux+Nu7YsUKt9tYYzQnOPl7zVor27Ztc7uS6+9//zsawjW1wslkTsxyYsjM22+/jfqitj5fxtGqxmeK+FzRx0ltd3GUExm83sxIJ/xMuDyc35uz9WXX++bqS7OfriAIQl3DsSpjMFfuMdnMirvxK623+DyTvtzZwvD3rSaydWkDwox4CuXuYiHFWYri5nFnZXD1E3UH87iQ42t3k9Z1BdvEbOkXX3xRta0mYqGnftXZiPy33nor/ve//6nV0xzv8zETCtytZDC3Vdcg0VDnYb+L3x1350moJTAucyxs/u5Z9RzdNmKuWcdxvfW7pzP4zfujqM/s+MqwngMnwHXGPZNxhIaJZKT7APv27VPLO3ivCyxyiSqXr/B5/iBSPGNnm76K06ZNU+IZlyYxAHA290KEwqou7MCBJ2ebKVxQcNbea1yCQ5HxH//4h1pSwyXM/GHldryWtIHhNfQEl/NzyRE9vPSSHs6288eVS5V08S5vUGTmQJUDp9/85jdqIMUfaC5n+/zzz1Vw97Ysl+fDjMZXXnlFieMMPFzaRlGVy4l4HjqTW0OhfOfOnWopsR5Ic8aV3zFuz+vGgqLnAn/4GQBpW+NuNpjccMMN6hrz82Ebq8Kzzz6rzpOrCLj0md91DtKZ5cnPsD5gJ2/WrFlKAGFni21ju3gd+TwnSapri6MH1Mxk5WfB7yVnqbXAboWTNhQwuMKCmf6aN998U30P+FmYi/bw2vPGZW/sJLrzba8M+tvxffRqFQShIhK/awbGSIq6/M1yx8CBA1UGEydm+XvmDmYEc3DF1TjM0KVvN+MGbxzcckkxlxtzkpcTGxzscwKEk8pccXQ28HebWVYcwHL/zJLiSjL+jnPylefF74Z50p2xmO/hRD3t17hiS8cD/pZzZYO56GZ9XlMr9HunTRH7GNwvJ4x47dgfYz+mvry6a+Pz5WfCCQNOajBbjH0YXWDcHbTfYzyn167ug/JvnfGay+ytkyPsG/H7ypVk1ZnM0FBA4KpAdxmWgiAIdQkTpzgO5yorrrxj7TKOoSk6MmOXr1E85PhVwyQd/nbzN4yrbjnW4n4Yt5j9y9/FmphUZoyjCOop+Y2xjL/dTIZg4dKqQA2AmfecEGeyF9vP9uoEifqIhYw7jFm8jlydzLElYzz7IKwjx7FrdSe9vfWrqgv1EI6ZKWYzQ503jqlp/8O4zePw+rEfxLGuvpbUbhhD2X72QaiJ8DzMhWOt8LzZF6f+Qw2E/S2u0NZ9FTPcP8e6v/3tb1Ub+V1hIXd97czfV/bfqAtRX+FnTBudqljC6eQZ9iH4N8AxAUV+9gFl1XcDxiE0OPixfPXVV2WP58yZo55r1qyZy61x48aO22+/XW1z3333qW22bt1a9r709HT13JYtWxwXEh999JE6b/MtMDDQ0bt3b8eUKVMcpaWlFd7z3nvvORITEx1BQUGO0NBQR0JCgmPcuHGOgwcPlm0zduxYdd2tnD592vHcc885Onbs6GjSpIkjNjbW8dRTTzmKiopctuvQoYNj5MiRbtucn5+v3tO5c2dH06ZNHa1bt3YMHjzY8eqrrzqKi4urdN78zlx77bWONm3aqO9GeHi4Y+jQoY5XXnnFcezYsQrbl5SUON5//33HpZde6ggLC1NtZxvvuecex9q1aytcz9WrVzuqwxdffKHe98EHH3jcZvHixWqbN954o+wasw1m+PrEiRNdnktNTXX06dNHXatOnTqp8/jzn/+sPmfrex955JEKx+UxeCwN989tDx065LKdp8/88ssvd1x88cUuz/Fzmjx5sno+ICDA0aJFC/Wd4ncjLy+v2m0ikyZNcrRr187h7++v3rd7926HN2644QaHzWYre7x//35HSEiI47rrrnO7/d69e9X58X1WfvjhB3XMzz77zO17z5w544iOjnY888wzXtskCBcSEr9rh+uvv179vp84ccLjNr/5zW9UHDt8+LDH3/Rly5ap32XGDmts2blzp2PMmDGOqKgotR/+9vK38/PPPz/neMgY/Pzzz6u4xd9kHp99hV/96leOb7/91u172P94/PHHHV27dlXnHhwcrNr+wgsvuMQUd/GoNq4psV4zfT3MsYl9iwkTJqjryD7V8OHDHZs3b3a0atXK8eCDD1Z6LXXs4X1l/Sd38ZRt4fPs+5g5l8/XXZuysrJUm9hn5Gv8HLyRnZ2t/u4//vjjsudef/119V5zG8ywD8jXv/zyywqveeqfaFJSUtR7t2/f7rVdgiAIdcWOHTscDz30kBrvMv4wRnTv3l3FhnXr1rl9D/tDo0ePdrRt21b9dnN8xbHO1KlT1ViksvGVNzh2Y2ziWNgbHOMzfnuKB+7Gr+x/sN2MERxnM54uXbpUvffTTz+t9Ldc92Mqi22exmve4tmIESNUm/gZcBzNtqWlpVW7TZX1q6o7thw2bJijefPmZboF4yY/U/aX+NkzfvOzp26jeffddx2XXXaZ+hw5/ub5PPnkky79JHd9FX53OEbnWJbfQx57w4YNbsfj/A4OGDBAnWP79u0d//znP93uk5/vwIED1f74faWWNH/+/Eq/L+wDXH311Y6IiIiyYzzwwAOOzMxMj9dSqH/8+L/6FvMFVziDxWwkzmYSzvJx5paVgK2FH1ikkbNwXP5kXarDZbtccsPsXWYFC8L5DP9e+Dei7RIuRJYsWaKWsDMT3lqJvKahvc7o0aPVygbaEAiCIPFbENzBFXvM1GL2IDP7LlSY0Ua7Hsbq2oa/Qfr3SBAEQah/OHaibdfPP/+sMqgFQfBdxNrFB6BtApd/0v6CS4LdwR9jemlT1NJeTtpbsyoFLwXBl6DIZC5+RvF83rx5Hit7Xyjw94FL0F5++WW3fu01CYsl0tNVRHRB8IzEb+FCj89m31FdYPdChZNmXP5OT97aFFG4tJ01F+rL7k4QBOFCxxoL2RekXQctVmitIgiCbyMZ6Q0EFtHQlZw58GaxSvpr02uKvkz0yGTHm76TfJ1FFlJTU1UhAnpAsYBFv379VIYbByx8/Mgjj6gfa2a0CcL5BMVb+snT55Q+YvR8oyc7/c1qOxNbEATBjMRvQSiHxcJ4o+cov9PMvKOvOyd53RVVFwRBEITzDfpeU0xnTROOUVmTbNmyZWoF4lNPPVXfzRME4RwRIb2BwKIZ7gpTMsOWAxIu+eaSWBYiY4EqFkJgISgWCmTxJMJq0ixKxIE3C1klJyergTsH84JwPsHK6ixAwsreAQEBqpPCjonM8AuCUNdI/BaEclgIbNy4cSobmoU1WfyLRdD5N0BhXRAEQRDOd1hAlf04JloUFRWpotQPPfSQWs0rCILvI0K6IAiCIAiCIAiCIAiCIAiCIHjB39uLgiAIgiAIgiAIgiAIgiAIgnChI0K6IAiCIAiCIAiCIAiCIAiCIHihsbcXhdqFBcXoixoaGgo/P7/6bo4gCILQQKDrWn5+Ptq2bQt/f5nzbmhI/BYEQRDcIfG7YSPxWxAEQTjX+C1Cej3CIB4bG1vfzRAEQRAaKPv370dMTEx9N0OwIPFbEARB8IbE74aJxG9BEAThXOO3COn1CGfC9QfVvHnzKs+iHzp0CG3atPGZLAdfbLOvttsX2+yr7ZY21x2+2O5zbfPx48fVQE/HCaFhIfG7YeOL7fbFNvtqu6XNdYcvtlvi9/nN2cTvhoQv/k3VB3KdqoZcp6oh1+nCuE7HqxG/RUivR/RyMgbx6gzEi4qK1Pa+8uX0xTb7art9sc2+2m5pc93hi+2uqTbLsuOGicTvho0vttsX2+yr7ZY21x2+2G6J3+c3ZxO/GxK++DdVH8h1qhpynaqGXKcL6zr5VSF+++7ZCYIgCIIgCIIgCIIgCIIgCEIdIEK6IAiCIAiCIAiCIAiCIAiCIHhBhHRBEARBEARBEARBEARBEARB8IJ4pPsAZ86cwenTp8t8h/hveg/5iu+QL7bZV9tdk21u0qQJGjVqVGNtEwRBuNCQ+F0/+GK7fbHN7totfQdBEARBEOqzz13T+Gofra4pbeDXqSb7qCKkN2AcDgeysrJw7Ngxl+f4Bc3Pz/eZIja+2GZfbXdNtzk8PBxRUVE+c/6CIAgNAYnf9YsvttsX2+yp3dJ3EARBEAShvvrctXEMX+yj1TUOH7hONdVHFSG9AaN/ECIiIhAcHKw+bH45S0pK0Lhx4wb75bTii2321XbXVJu5n5MnTyInJ0c9jo6OrsFWCoIgnN9I/K5ffLHdvthma7uJ9B0EQRAEQajPPndN46t9tLrG0YCvU03rWyKkN+ClKfoHoVWrVj7x5fSEL7bZV9tdk20OCgpS9/yx4fdQlmoLgiBUjsTv+scX2+2LbXbXbuk7CIIgCIJQn33umsZX+2h1jaOBX6ea7KM2POMaQaH9nTirJgj1hf7+1ZbfmCAIwvmGxG/hQkf6DoIgCIIg1DbS5xbqq48qQnoDpyHO5AgXDvL9EwRBODvk91O4UJHvviAIgiAIdYX0O4S6/q6IkC4IgiAIgiAIgiAIgiAIgiAIXhAhXRAs3H333XjxxRdrbH+LFy9WM1/VqST97LPPonfv3jXWBnft+O6779QxWFlZEARBEHydmo7fdcWePXvg7++PdevWnXW/obaQvoMgCIIgCIIglCNCulDj/OY3v1GDLt6aNGmCqKgoJCcn48MPP2zwA69ffvkF8+bNw2OPPeby/MaNG3H77bejTZs2CAgIQNeuXfG3v/1NVf6tjMGDByMzMxNhYWFVbscTTzyB1NRU1CbXXHON+nw++eSTWj2OIAiC4Bucj/F7x44duPfee9G+fXsVv9u1awebzaZiHwsimfnhhx9w7bXXqoJV9FCMj4/Hn//8Zxw4cMBFVNa3yMhI3Hrrrdi1a1el7bPb7WjatCl69uxZK/2GukL6DoIgCIIgCDXT537wwQcrvPbII4+o17hNQ+fBBx9Ubf3Xv/7ldbt//OMf6NevH0JDQ1Whz5tuuglbt2512aaoqEidO/vhISEhqo+dnZ2NhogI6UKtDbQ4CGSWFQe2l19+Of74xz/iuuuuqzBwNVPfhaneeust3HbbbeoPV7NixQoMGDAAxcXFmDt3LrZt24YXXngB//vf/3DVVVep572dDwfOFCOq48fE49dm5WkNf5zffPPNWj+OIAiC4BucT/F71apV6Nu3LzZv3ox///vf2LBhgxLDf/e732HKlClqklzz7rvv4sorr1Tx+osvvsCmTZvwzjvvIC8vD6+99prLsdjxP3jwID777DO1j+uvvx5nzpzx2j72GTghf/z4caxcudLrtmfTb6hLpO8gCIIgCIJwbsTGxuLTTz9FYWGhi5g8Y8YMlQDS0Pnqq6+UVta2bdtKt/3xxx+VSM7tFyxYoMYNV199NU6cOFG2zeOPP45vv/1W9a+5Pfvat9xyCxoiIqQLtQKzvjgIZOYXB7F/+ctf8PXXXyMlJUUNJjUcJHIwe8MNN6BZs2ZKoCZ8rlOnTmow2a1bN3z88ccu++f73n//fdx8880qa6xLly745ptvXLbhgJmZdBxUM2uMS74PHz7ssc0cBH/++edqQKxxOBxqwN2jRw98+eWX6N+/Pzp06KAG6/wjX758OV5//XWv5+NuifZ///tf9cPJtvMc/vnPfyI8PNyjtQsHrZy1e/XVVxEdHa1Edv4QmYULXiPO8rVs2VJtM3r0aOTk5Hj9nHiuaWlp2Llzp9ftBEEQhAuD8yl+33PPPWoF2dKlS9VrPBZvo0aNws8//4xevXqVZYszk503Zt8PGzYMF110ES677DLVVq5AM8NMGsZZvs7XKLoz890TbMtHH32kzoOx+YMPPvD6GZxLv4HXm21nNvsdd9yB/Pz8sm24qoAZQR07dkRQUBAuueQSdd3McPKE14yvX3HFFWpCxYr0HQRBEARBEM4N9rPZt6POpOG/KaL36dPHZdvK+nDsC//2t78te5198DfeeMNlH1XRlKrKgQMH8Oijj6oVilypWBm0BuTxL774YtV2jin27duH9PR09ToTV9g/Zv92+PDhSExMVH3nZcuWKfHd3D+eP3++uj48T25LzYvjFGp2zZs3V33tqjhHnAsipAt1Br/k/KMx/1DowR8HhRkZGWr5NWe2/vCHP6jl1BxMP/DAA2owzCXXZp577jmV3bV+/Xq1FPvOO+/EkSNH1GscfPJ4/APjYI9/uFwWwu09wf3wDzgpKansOfqVcoD8pz/9SfmXmuG5MHtt5syZXs/HCgf0XALDc+T+mdWuBQhv8Pw5aOX91KlT1Y+PWdTgD+Dzzz+vzpfXkIPfypYD8UeaIsWSJUsqPb4gCIJwYeKr8ZuZ6LRKs8Zvjc74ZuYLV5eNGzfO7XZmwdoKO/HE2+o0nj879Owz3HXXXSr7yJyBUxlV7Tewj8BJjzlz5qgbs3leeumlstc5AJs2bZrKtGcmPTN/2B5uR/bv368yfyiU8zhMJOBEihXpOwiCIAiC0KBhP8vTraio6tuassU9bnsOsP9MwVjDhA72na1U1oej0B4TE6P6tNSvmOjx17/+FbNmzaqWpsS+PRMyvFFaWqqSQ5588kkljJ8N7LcTJoESCurUs9hX1nTv3l31OZm8aoZtfPvtt5XIzr4rxwi0lmEmPx0kvv/+e7VStVZxCPVGXl6egx8B760UFhY6Nm3apO7NlJaWOoqLi9V9Q2Xs2LGOG2+80W2bf/3rXzt69OhR9hrP/49//KPL+wcPHuy47777XJ677bbbHNdee63L+5555pmyxwUFBeq5lJQU9XjSpEmOq6++2mUf+/fvV9ts3brVbbu/+uorR6NGjcquLe+nT5+u3rN27Vq373nsscccQUFBXs/nhx9+UM8fPXpUPeY1GDlypMs2d955pyMsLKzs8cSJEx2XXHKJyzXt0KGDo6SkxOWacF9mzNd69erV6rj5+flu26Hp06eP49lnn3V7fp6+hzXNmTNnHJmZmeq+obJ6tcPx0kvGPWFbf/op0/HSS2fKnmvo+MJ1Pl/afa5t9hYfhPpH4rfvxO81a9aUbZedne1o1qxZ2e3f//63ev6hhx5yNG/evNLrY42jBw8eVOfcrl07x6lTpzy+b/To0S7XivH9o48+Knu8e/dutd9Vq1aptp9tvyE4ONhx/PjxsueefPJJx4ABA9S/i4qK1OvLli1z2c9vf/tbx6hRo9S/n3rqKUd8fLzL6+PHj/fad3D3va6rvkNVWX1gteOlJS+pe/Pv8yr7KpfnGzK+GAd9td0Sv89vfP3z8cW/qfpArtOFcZ289jcodXq6mfqliuBgz9tefrlrX6d164rbnEOfOycnxxEQEODYs2ePugUGBjoOHTqkXuM2Ve3DueORRx5x3HrrrdXSlN566y3H8OHDvbb9xRdfdFx11VVlfT/u8/XXX6/yWIffN/ZrhwwZUvbcJ5984mjatGmFbfv16+cYN26c+rfuHy9cuLDs9X/84x/quZ07d5Y998ADDzhGjBhR7e9MdeKDZKRfIKSlAZMnG/f1CX/SrJ6f5gwywgyyIUOGuDzHx3zejF6STbisnMs4tJUJi45xlo3LwvWNM1rE01JkelNxSbs7T1Jj7F81rOdjhd6qtIgxY33sDs72NWrUqOwxl+OYrVs4i8cl9lxSz2tBX1vCJTPeYDZdbS99OR9gUt7TTwOXXcZsSuO59eu5TAmo5bqwgiBcwKQdTMPknyer+/rEV+O3GS5hZaY1b8wy11nk7s7NG8z4YbvpCcnMcnqq08rGHcywZyY/s4Y0/Hdl9i5n029gBhGLOLnrJ9B6hrGe2ezma8vsJn1d+TmxJoyZQYMG+XzfYeq6qfj7T3/H4A8Go/9/+yM901hGvGjXIqTsSEHqLgnigiAIgiDUPW3atMHIkSNVVjgz0/nv1q1bu2xTlT4cYT0gWqJwn3z9vffeq6AFVaYp/f73v0eqF3GDmtMbb7yh2nu2tXxoJ8OVq1yheTaYxxFcIUnbw7i4OJfnKrM4Plca1+rehQYD/xZSUox/V6Lz1iocpNG3yQwHo2eD1YuJf8hcZkIKCgrU0uTJnD2wwB8Ld/AHiz9QHFjrATG9VHW7rT5V+nl6idbE+ZzL+XIgP2LECHXjEh3623KZCx97W25OuJyeP7aCezj5xL+ftWvpPWas7poxA5gwgUv5gawsijvGRJXNVr9/X4IgnH9Q5KPYR5La1t8PjK/Gb4rQOn5z4NC5c2f178aNy7vAjONcYsoiq56OYYaWJhT/6ZVuFq7dwWWmLBxlFqgp3PN8Wbzc2oc4Fyq7roRLXul/b4aTENXFF/oOnHzi38/mw5tRcNo4/7WZa5WAHts4Ft9s/QbtwtohOjRaTVbZ4mz1+jcmCIKPsbi8Lked4fADSmKBxvsBv6onep0zw76tu2MJQk3g7Pe4xSQkK7yJrlZ7QDe1Y84V2rtQwNZiuJWq9OEoStPO8LXXXlNJEOyfvvLKKxUK3HvrK1YF9oFzcnJciqHSn522jrRX2b59u9f38zxpP/jTTz+pxBQN9Sv245mAYrZUpL0jX/N0Dmz/uZ7T2SBC+gUCBT7zfX2waNEi5aNKPydvsEgA/UDHjh1b9hwfx8fHV6twAzPEmJ1lHix7Qxf3pKeU/jfvmQnHgqIs2mX2WWXW3MKFC5VfVXVg4YfVq1e7PGd9XF22bNmC3Nxc1RaKADxnXbjBGxzccxbT3SSBYIjod9xB31gKRkbMpUYzerTx+tat/HFnFWpg2zbjOQrpWnx3J6x7e00QBMEKxT3zfX3gy/GbBZXonejJJ5386le/Ul7gL7/8sksBcY21U88JBW++6WaYec7BhbVmycMPP6x8MM0e5rXZb+BnwMEWM5P0ijV3n5+18Ksu8OSLfYe/LPwLluxdgsDGgQhqFIQSRwl6R/XG8Ljh+OCnD5TA3jywOTLzM10mq7QA705Y9/aaIAiCIAgNhOoke1S2rdmdoBaSJq+55holIlMAZiLk2fTh2N8ePHiw6l9qaqMoPL3RrzT5mBO2mc97q8/HJBIWJ2U9JRYNtSbnMJOegjiz4W+99dayZBies6fVkfWJCOkXCBTs6lK0O3XqFLKystTsFO/nzZunBqjXXXcdxowZ4/W9LFrAQS8HaPwj/fbbb9WyaIrW1Vku8t///hejRo1SxcNYxIBLYjhT9/7777ssZ9Ews4oD+J9//rlsIM4fM25/9dVXqz/op556Ss2IcWaPA2P+Uf/xj3+s1rXhD8hll12mKhIz644CBasMn+3SGMIZQWbhsagCi4NRWJ80aVKl7+MAmT/KDfHHqSEwdSqwezcLagAtWgCs7aYFcD5HmxeulmKiYWBg+USVeQXIpk3AO+/QCgC47jqAtWn1pLcI6YIgVAbFuroU7M6n+E2hmvGb9jKM3xSKWciIWTCHDh0q21dsbKwS0Jklc/z4cXWeFPLtdrtaNsvlsczwqS60kFmzZg0++eSTMnsaDc+PBcL//ve/10m/gZlJzFTiZAizdIYOHaqy8DnwYnY9Jz9Y0JTnyc+RfQlOyJsLUPlS34GCN2/FpcUoPV2KK+OuxKThk9TfEs9/+0Xbse/MPtyRcAfi28S7TFbpVSC0gLHn2RETFoPrul6nBHf7cTsycjLUdiKkC4IgCIJwrrA/qm0Q3fVzq9KH40pM9lnnz5+vROqPP/5YJV1YBevKYBFPit2e7F1ok9iqVSuX5yiAUyNj4kdJSYl6zmaz4eabby7LtGf/nqs0Z8+erc6HYwwSFham7AJ5/9vf/hZ/+tOfVN+f58X+L/uaAwcOREOjXj3SWW2VgwDzzTrQsMIqtNwmMDAQCQkJaoBnhgM2Dpr44XJ/HMRYl6LyA+GHzA+MAuRjjz1WVjVWY20Xb1YPH86kcODGwQSXCrsbbFyofPfddyozmgPR5ORkVU2YXkr8w3H342DmpptuUtsyi4weTu+++67yixo2bFiVj0/fUv6wUAjg94HfFQrezCDzlpXGgSMHvGY4s8dBI9vNc+FnzQE5f7AWLFhQ7SXRHNCz2jIHxJdccom6VvxR5Hf6bKGIwO/f559/rvbJJfG8fpUxc+ZM3HnnncpX6kJn2jQgORm4+WZm/5X7oHPVPieee/asmEU+fDgwZw4wcaLxGoX3Rx+l/YCxLz5H4XzNGoA6EgV1+qrzK1Ofq0ME4VyR+H3+cj7Fb3a8KQbzO8MOPDN6GNMZ+yicP/TQQ2XbMoPn+++/x4EDB1THn99V7pMdeQ5ezgZmo/OY7v42eAwujbX+HdRmv4ET7BMmTFCr1zipwAwoLhPWgyz+TXE1wNdff62Ow2O++OKLDb7vMO2XaUienoybP70Z3d7qhucWP6fE8PDAcDRr0gwdwjtgVMIoF+F7eMfhmDN6DsZcMkY9T3uXCYsmqH1RUE/unKxE9DVZa7Bw50LMzJiJWRtnIf1gOhIiEup1hYggCIIgCOcX7G/ydrZ9uAceeAC33HILfv3rXys7QboVmLPTq8rhw4drJJN9586dal+aKVOmqDEbxwQcZ+jb//3f/5Vtw745E3eYwMoEEorzHB82RPxYcbQ+B+IU/syZSlzGazXX1yxbtkxdUH55eIE5o0HBkNk+PalyAWrmZffu3Wogdt9992Ht2rVl2UmEpvYTJ05Uyw44uNm7d6/KwKFhPdui4cCbgz9+QTUcxOlBC4/BY/K9HGhxxoYDPX6Z3S3HcAeznjjzwi+U9Y+Gy2Z5DP5hmAdK/Lg4y8PrdC4ZzHWJL7WZBcs44OYfNAfgddVufleZRU7Pqbq61vxh47mmpaV5nKn09D2saTizSkGBfrPehJLahML38uWG7zlvnToB1GQ4GUuXHFqyjBwJvPWWa5v374/AokX+sNvpW2a89sADwPjx5QK9zkgnS5cCHToAb75Z9xnpDeE6XyjtPtc2e4sPDQGJ3xK/Gxr1Fb/r61rXVL+hulj7Du7aXVd9Bw1F9OX25Sg+U6xunVp0wie3fqLEdGaVMzN9ZJeReOvatyr8PlMo53azt8zGpsObMChmEFLuMpaUUVR/Z/U7ZRnp/PfevL24pcctZfuqK3wxDvpqu8/3+H2hU6OfTz14pJc6/JBTEouIxvvhLx7p59VvT33g69eprvobvtQfrk8cPnCdvH1nqhMf6t3ahRfZah7vCWY5cWDMJad6VoYZwVx+wKwZQm8essdDEQAOnplto+nUqRNeeOEF3HXXXWUfunng7altPB4vvl7qy1khLinmLEpVB+JCw4NZjlwSY549qw2YrceqyyzUxuXZLBD6n//8B3UJ/0Z4zOou9zlfGTXKuOfvKe1Y6IOuLZGYZW6G4vi77wL9+xsFSJllHhHBIhmGYG7ONqcTgnZDoBjPIqW0dmH2Om+kXz8gM1N80wXfQuK3cCHG7/qiIfQbGmrfgdnmhF7omw5twuiE0WWWSI/Ocw3gFMffXf0u+of3x+hBo7Fo9yKVaX76zGnEt44v2xdhtjpvmtUHViPrRFbZfpilzu3N2wiCIAiCIAjnN/UupLOqK7PPOBtA/xtmq5krwJpZvny58swxw0Evl6CeC3rGwVrUisuAma0WFxenMtfuueeespkVtsWdyb43v2z6jvJmnvHQM4HWqrJ8zBkdfTOjH9fjYoJq40ttNhdwqK12r1q1SnnO5ufnq+8XRSZ6QtXEcaraZhZ04M3bdvr75+47WpPo73ttV1f2xl13GTczujkUwimQ08qF9d1YX3bv3lIEBDiQkFCqstkPHAAOHeJ1ZbG88vea4fPPP8/Cfcb2XNHPnwTaw+TmApMnA3/4gyG2n6/X+UJp97m22RfOVeK3xO8LMX7XJt7aXJv9hurgru9gbXdd9R00dyXcpW5m9HEpcseExqgCoxTC//HTP7D32F4EnApA1M4oDO80HD/u+RE5J3JwY7cb1X48tdm8r4k/TMTSfUvxS9Yv+PvivyOoaRD+POjPuKuXpSNxAcdBX233hRC/fRnajnFFmhVaGPz73/+ulzYJgiAIFxb1KqTTu4e+pFwimpmZieeeew6XXnqpWr5NA3orNKSPjIx0eY6PtVH92cDMJWbG3X///S7PswDU8OHDlf8jPTMZnAsKCpQfq7e2cHDN5cXMjLJCkYHnaIUFr7jEwAyLYbEjxiw7bdhP2LGjbyhpqMslrPhim2u73VYfV2L+nBtKm9kmfg/pscUiErUFj0FBjO2vz2VlFLh/+skoIkrRXBMbC4wda7w+a5bxuFOnUgwcmIfLL3egSxd/bN9ueKtv2QJcdRXAGmzO2houmPfFmiLa3rlNm3LrF12M9Hy9zhdCu8+1zRTLGjISvw0kfjdMfLHdlbW5tvoNtdHuuuo7WGF2+U97fsJlF12mPNBJbKNYjO0yVv178s+T0drRGu1atMOwqGEY3Gqwev2pvk9hfdZ6bMncgqumXIVBsYPw+/4VA7h5X32b90VuUC4ccADUTYuAz1d/jqujrq6Vc/PFOOir7T7f47evw+J5+jeHsN/B1Tq33XZbvbZLEARBuHCoVyGdRaw09DjlwLxDhw6YNWuWyrKpbThoHjlypPJapd+rGRr5a/r06YMTJ07glVdeKRuInw0sUGnOyOPxY2NjVaFIdx6r7Igxy86aaUfqcmBSU/him3213TXVZn73OIhg8b/a9kjnAJx/C/U50Jo+HVixwsgUpzbGwqNTpri+/vPPQLt2/HsuRX6+HwIC2iAiwl9ZuwwZYti7MLs8I8PIPjdDr/WPPy5//OOPhjWMmZYtgf37jcz28/U6XwjtPtc214Wv8Lkg8Vvity/gi+32xTZb211XfQcr0xdOxwr7CszLnIeiJUW4ufvNmHJdeRBfdWwV1h5fi+4tu6NFeAsEhAUoX1jehnQfgqhXo5BbmIuMExl4/jrXAE6v9Y/XGQH87t53Y83xNUg7nuayTctTLTF1+1SVsZ4YXbNB3BfjoK+2+3yP374OPxczL730krJ6M69IEgRBEITz2trFDD1Nu3btih07drh9nX6n2dnZLs/xcVU9Ws1wkEu/VmbOffXVV5UOXCgSMPONS7sDAgI8toUDanfZbITv480KO2nWjhofsxOnbxpmR+jHvpRl5Wtt9tV213Sb9ffP3Xe0pqmr43iC/uUhIca/KWQT2jHTC11De5emTYFLLzU8zbdu9UNBgT/69y9v8003ASwuzXvrqTALfeZMoKAASEgwfNVp8UIaNQJYp5HCOj3Y6Zt+Pl7nC6nd59JmXzpPIvHb9TmJ3/WLL7bbF9vsqd112XcgLCbKgqHZJ7JxvPg4jp06pjLFv9jyBd694d2ybWi/0qZZG7QKaYX0rHQUNC1A/5j+Zfu5qcdN+HLzl+re2m5mu8/ZMQf5p/Ixe9tsRIZEqmOojHSK6IEtERYYhlmbZgF+QL92NR/EfTEO+mq7L6T47csUFxdj+vTpaqLbl343BUEQBN+mQQnpXHq9c+fOsoJjVujBmpqa6uJjymJlfL46MJOMfqgcFH/zzTdVyhxYt24dWrRoUTaQ5jHnMW3VxNm0RRCEuhfMU1MrFvbkc7RUMa/IveUW1/dSYGemOjPJhw41hHcWHDXz3nvGzR08JmvD0c6FOh7dJZi9zn0ycTUujrYTNXm2glA3SPwWBKGmhXFbnE0VDK3sNT5O2ZGCzYc344zDWOYV1DgIt/QoD+LcZvOhzSg8XYiWQS2R2CoR/eNcA/h717+nbu7g8ezH7fhm6zfILMjE0aKjiAuPw77j+3Cm9AxCmoYoIT2iWYTaVhCE2od1Vo4dO4bf/OY3NVLjpNo46l68L3X4weHwU/d1e2Df8v73xfoM9YGvXyfdfto91XbNGF+svVMfOBr4ddLfFXff++r8HdSrkP7EE0/g+uuvV8vBDx48iIkTJ6JRo0YYNWqUen3MmDFo166d8iYlf/jDH9Syrddee00t6f7000+RlpaG90yq1ZEjR7Bv3z61P7J161Z1zww03hg8r776apw8eVLNYPOxDqhcKsbjf/vttyo7beDAgWqQzgH2iy++qNqrYfGyt99+G+PGjcO9996LRYsWqSXtc+fOrdNrKAhC9QTzqVMB/pna7eXPc1s+ppjNjHPazrIwqFUQ50/Trl3MVDIy0ul1zqxyb8czw+cnTTKy0qn/LVliZKIzoZbZ6D17AmFhwLffAqyX1KePYQcjCA0Nid+CINQWWhgnZiGdIvqERRNUYVDza9Gh0Thy8gj8HH7gfxeFXYRZt89yeS/F7dlbZmNv3l5EBkfi1vhblaVLZcK9hs/zxkzz5398HlkFWShxlCA8MByHTx5GyZkSJaKzbZd9dBlGdBqBr+74qlavkyBc6HzwwQfKao6Fzz1RnRon1aYkFnVNqQPIO9NarYbxr0stvbYKONUSvlifoT7w9eukxdADBw6gdevWtWaPp4+jV50KvnmdWMeKv/1sIydhrW2sTo2TehXS7Xa7GnSzGBEHwUOHDsWKFSvKvM84oDb/QQ8ePBgzZszAM888g7/+9a/o0qWLmonuSfXJCTPU7rnnnrLHd9xxh7rnIJ8+qmvWrMHKlSvVc507d3Zpz+7du1UlcP4Bsur3448/rr4M3O6f//wn7rvvvrJtO3bsqAbd3OaNN95ATEwM3n//fZUpJwhC/UNRO8UYh3sUts3b0tOc4jiTVihua4HczJgxQHy8sb25GGl1jsd98EaLafbf9cTnyZNGxnthIbB3r/HcmjWGzQu3F4SGhMRvQRBqC53Rbc3spthNodqa9Z2Zn6myxQvPFMLfzx/dWnerIIjz8ZvXvqn2oQuRVibcu2PMJWMwM2OmEtIDGgWgqMQQ4k6UnEBhSSHWZK1Rj7/e+jUenfcoxvYeW+k+BUGoPnv37sXChQvxJf0Ua6jGSbVp7PSCrEOYic7/2jS2w9+vDjM+3Q2MGjC+WJ+hPjgfrhNXnWZlZalbbaIFYsE7Df06BQcHq0SwpsyePIcaJ/UqpDMjzRuLFy+u8Bwrcnurys2lXd6Wdw0bNqzSZQb0XuWtMrivtWvXVrqdIAh1DzPDzfcaeo/TxpkZ4JMnG6/rbShsf/65kXHuTKytAEVy3iiAm5MzPB3PE3r/sbHAhg2G//p11xliPgue6lWo77wjQrrQ8JD4LQhCbaGzv60w85wi+qgEI4BO/nmyEtR5YzHQNZlrENw4uOx1T/vlIC/HFMA9Cfee0Pvn/e6juzEjYwYuv+hydGrRCUv2LkEpjBly+q3HNI8RIV0QaoGPPvpIrSrhKjdvVKfGSbWpSyHbfFg/ZqMbtzqjAQtj51N9hvrA168TxU8KoyUlJcq2ozZgv4HJQyyi7qvXqS4obeDXiauXGzdu7DFbvjptblAe6YIgCDWFFrytUKhm1jfFat6T8ePLt504sWaPV1lmuhVaxHzyCbBpkyHoU2AXBEEQhAsdZp4z65v3vOks8vFDx2PWbbNqXLj3lpXOm2bisIllFjHRq6JxIP8AGvk1QoewDuKXLgi1JNZQSB87dqwSRQRBuLChMMpVqbVl7cLfHO6bon1DFIgbCqUX0HU6v89OqBeYUXjTTTeV/Zt/RFw6ob2SeNvBlGAnXIbz6KOPIi4uTmUMcLkdvXdZmE4QzhUK01deCXToANAikRnjtFVhRjjvq5pBXhftZIY8PdyLi42MeRYj3bLFsHcRBEGobSR+Cw0JCtNXTrsSHV7vgOcWP6dE6eTOyWVZ6PrfDaGdzI6fum6qKjoa2SwSF4VfhOCmweo5vi4IQs1BSxdayLHOiSAIgiDUNTKFK9Q6XGbPgnLmZRTaR3fPnj0YMmQIwsPD8corryAhIUEVAZg/fz4eeeQRbKGKKAhVwFOxTz63dKlhlTJjhpFxzte5/Zw5wOzZLD5YTfsUfTD6stDY3HzQqlQddYP2WE9IoIczsH49kJsLHD5sFCcVexdBEOoaid9CXeCp2CefW7pvKU6dOaXsU5j5zde5/ctLX4Y9z67sXqplnaJj9ODBNeb5qz3WEyIS0DG8I9Znr8eRwiOqAOquo7vE3kUQahgWHq/M6k0QBEEQagsR0oVah1lqUVFRbv2IHn74YfXcqlWr0KxZs7LnL774YskyENR4lxnamzcbxTgPHmQRQ6BrV4A1CbVWzYztF14wrFDsdlcdm/cLFgDbtwOjR5fvm9ssXGjst9pCNd/83XdAt27A1q3Gc1o0r6TqqCed3eqxrs+bNq6hocb7qmMdIwiCcK5I/BbOha+3fI0Xlr8AP4cfjhcfx768fejaqivu6X2PEsBpz8JinRTJ+V1iwVCzoM77BbsWYHvudoxOKA/g3GbhroU4efqkKvxptlmpcvxmUZRly6o9Ee5O9Ld6rDMLPftEdtl7eF58n4jpgiAIgiAIvo8I6UK9ceTIEXz33Xd44YUXXAbhGma5CRc2HM/OnWv4mjPxRBfgpHZNrZoCOgVyFv7k6xScs7ONsTFrHU6aZIyFKZjrMfKjj5YXHaXlC4V3T4VFPaLVbmakM32cj/UAPDraq2cMN/noI+DDD4Gnny4X8N15rD/8MLBtG3DoEJCYKEK6IAgNA4nfQlWYu20uNh/arP59xmEUANuau1Vlb+84skN5iWvCAsKU+Lxs/zIs3rMYk4ZPUsLzwjHOAA5g2i/TlHA+MGYgroy7UmWkeyos6hFzdXEK6ubZ90omwgkz4RfuXKiKm2pfdqvHOv9986c3Y/7O+SpDPadZjhLfRUgXBEEQBEHwfcQjXah15syZgxYtWiA0NBQhISG47bbb1PP0WeWyvO7du9d3E4UGik4Ka9fOyEIPCjKeDwkxVmSzIOfp0wALdFNMZ30R+orzNWZym216KbrfeSfw6afl4vysWYboHh9v+JNTC68WFNKffNJopB6Ac8fm6qVuzomi/4EDRia8J7i79HRj8uDoUWPMT31e/NIFQagrJH4L58IlUZcogTysaRiCGhkBPMA/AI39G+Ng/kGXbZv4N1He4hHNIpBzwhCezQJ68vRkvLLsFSy3L8cK+wolYi/73TLEt4lX/uTV9iHv0oXeRca/Gbu1mF5J8RSK9ydLTqp7b3CygIVRtx/Zrs5p9pbZ6jwEQRAEQRAE30Yy0i8UztK3uSa44oor8Oabb5YtDdfZa+JtJ5hhZjntTPgVpUD+6quG4NyjBxAWZojjR44Y9i4U1Bctcn1/p07G+HfsWOOx/rrrrz690Clet2oFjBzpOk6ukIRW2d+LXhpORX/IEOM5vUNmpFOV9/BePsVMdIro3jLh+Xbuirp8r17AihXA8uXGa+KXLggXEBK/BR+ANivHTx2HAw7kHs9FTn6OyjoPCwxTr7dAC+zJ24PAJoHYkLNBbUf84Y/urbtjeMfhGNvbCODaOoVQIH/hpxdU9npceBwGxQxyyULX/uQkidp8ZX8rOn7/6lfGRPiaNeXvqQIP9ntQZcVXlgnfqUUnbD68GV1adkH+qXxsOryp+jY0giAIgiAIQoNDhPQLhSosV60tOPDu3LlzBY/VLl26qMdSkEwgLK7JsS2hLcuGDYaQXlhoJI4xw7ykBPD3N3zNmYVOTScqCujbFxg3zvWrrf9NTZtf/ZgYQ5CneG0Voq3+5GV/L1bDdQ0Vbqr5LVpUPJHVq4GMjIrvNYlhY8YkVUkMv/lm456TA5xcINW2oREEwbeR+C34ABTReTt95rQqDsr7TYc2YWSXker1xXsXw9/PX2WoM/v8EA6hTXAbzLlzTgXLE2vBUX7X2oW2wxNDnqggRLv4k39s+ltRb65C/Db7qukOg35eY47fSWMqFcMp/nNiQE8O8DqQatvQCIIgCIIgCA0OEdIvFCoohfVPy5YtMWLECPz73//GY489VsFn9dixY+KzeoFAC5MdO4yMc/574EBgyRJDMOd4l+LxO+8AwcFGEnjv3kBxsSGOWwX0554DZswwCotOnFjxq8+xsLVwZwV/cj3QpuE6RXG9kYZp4lT46bliFbsSEozUeArpZjGe9+725QG+hZtzV+b2aTsYyUoXhAsEid9CA4cC+pHCI+rfIQEhOOUsaBLYOFAJySy+SduWwtOFCG4cjE4tO6GRfyNVQNQsmj+3+DlVeJTPTxw20UUo18VJrUU7XfzJozcZsZsx3NMElDV+m1d8WFeW6WVh1Y3fu1KRkZOB5M7JLu1jRjqRrHRBEARBEATfRYT0CwV3lQwbAByEDxkyBP3798fzzz+PXr16oaSkBAsWLMCUKVOwebNRpEo4f+EYloI3hXGK6YcPG4U1//OfcvsTjmOzsgxrFnqir1sHXH11uQ+6/mrffz/w/vtGJjvFdO5Xv1YtLVsPtDt3NhpjFbBoWM4GsdFW8Z1VTKly08ycJ6TFeHrT6AF+FeAh2V7etPDP6/HDD8bt7beN7bj7e+81zlUQhPMQid9CA+VE8Qklop88dRJFJUUqc7xlo5ZoG9oWBY0LcG+fe5WoPHf7XJwqOYW8U3lKTM85maPsXCi0a+7/9n68v+Z9ZflCMV0L6VqEnrBogvJONz/nMXbz3tMElDV+01OOhVMYbN96y/hb05npjNncX3Xjt0n8p387H1NE/2HPD/hx74/4ed/PWJe1Dttyt+H2i2/He9e/V80rLwiCIAiCINQXIqQL9UpcXBzWrFmDF154AX/+85+RmZmJNm3aIDExUQ3EhfMfCtwUxFkklNnmHTqUJ4FNmlTulsKxLW1ddu8GTpwwxrw33uhqSf7ll4aITgcCZqR7ShbX42oK1BunpsGGVMSMNS3/Ng/A3S3vnj/fULC3bq04gGdjdQYbH4eGlldJ1QP8KmCuX6onCzip8OOPxm6Yuc8Cq+T55417EdMFQagrJH4L2s6lMRor25amjZqieUBznHKcQkDjALWN/bhdCd8szrkma40SykOahqhsbbPQ/OXmL9VrfvBTGelmKMZTRGfRTrN3uvZSLxPWrbG7qvHbug3FdsZtFhTfv7/68duZhc5z0/7ttHWhiM4Jh1kbZ5X5yf93zX/V6yKmC4IgCIIg+AYipAs1zv/+9z+Xf7MoGbPUPBEdHY23335b3YQLAyZr62xzPe7lePWyy4zHHMd+9BHw4YfAJZcAO3caCWF/+xswZ46hUT/4oJH4bbY0veUWQ0znvVlUdquLp6WhYEIqQtfbURqQAcSY0tQ9ZYBqZZueMrQtYIMtGem5m7NxYm4GTsQlIDsoGQlH0uGXvhXLTyeh6cBkXFWJPYO7Veb6nufLiYQ33zQmE5yr55WgTt2K2fzaU70BJrAKgtDAkfgtVMbhk4eVjUvLoJZKNCdN/ZoiOCBYPW7WtBly83Nx4vQJTF41GUdLjqJH6x6qSOecbXOUoM5/097ELDTf0uMWJabzXmeju/NB16I5RfRdC2ZhQPZi4HeTyuO2tyKj7uI3V5Fx9Vi/frB/ORWlKXPhH9wMpSdPoHlRHo4dPYifThxDdPekSuO3FWu7dx/djQ/XfogzjjMoOFWAMzijXmcGfkAjY+KBVjgeM+4FQRAEQRCEekeEdEEQ6hyK6MuXG//muJbFQpmVTrGcWeeEIjqtXFq1Mp7n60wGmzXLdV9msXn8eOA9N0ldamwNp0INp5qemorEnBRs65AA/0RTmronxZ8qts2GL74ApnxvQ6df98Vzw41l5uaM9N3FnZGORGw8acP6pkl4rfhRtMkHdp+JxFv7x2OrSch3VwiNT/EcWXCVGfk8J2tT+vc3POQbNzaS5Aiz+bk6nVAnECFdEARBqGkootPShXRt1RXBTYLVZIu5IG1IkxA1CXPy9EkUnC5Q2eT0N5912yyPQvP4tuM9ZmUnHQSSltJ0HUBbI37eOc+OrK0B6HbQ4vEGDzGW8XvTF5gS9j06XflrPNd/eAU7mNQ4oKgzcLylH5ofAeJDg1GalYPU1kVYkbAfW/UxPMRvZsnTD94siPM27ZdpypqGWek3dLtBTRi0btYauSdzlageHhiu7G9ITPMYEdIFQRAEQRAaMCKkC4JQ51AMNt+b0eNTWrOsWGFsEx9fPmY9a/tga+Gx6GiERgQhcZTT05wH1h4x5sqeWvHnNklJeGBXEnLzgPVflarCpmU4GxccbcOxzCSMKErDLSsmo8UV/bA1OAY/bLe52M2wPfmzUpC+GAiZlOTiKkMR3er/zqbQ1oXOMcy4v/RSoyirzkJnUh1fMzVFEARBEGoUZqKb7915pgcgAGGBYejfrj86R3RWXuhaNDfjUii0OvF76lTEzJ2LGP77pkRXjzcdNM3e586OwgMX70JuYR7Wb/kKz/V/rsJs/MVtgdTuMei5rwjNflqBrqdCURgQgZ4lBYgz280wfn89C+m7FyOk7SSXLHl3gjj90Zfbl8Oeb8fxouMoPlOMKzpeoQqwkn7t+mH1ASOAu7tOgiAIgiAIQsNBhHRBEOocatIUx19+GXjnHcOmhcVDzeNlepnrcTM55wxr02CZAnjwW5m4LaQQF2lPc5qOU0y323H/e0nKIuaDDgNxY+QuQ7GGq33MzTe7PwzPK34MgMmpQGEKEJiMuIXjcZWaIZgMpJVnx1FEn5ljQ5xJMOc9M9GtEwecUGBTaelCy1Z9bfTEA4+ra5xOmFCeRC8IgiAINUXr4NbqdjD/IDZkb1CCepugNi6e6UF+QcoH/b83/BeBgeUFRWskfi9+DnG/fIyRhX4ok/IZHJ2VxO/PfE9lfKesbY5+x44ZfuhOtH3Mzd1vdjsbz/8r8Zui/JZCIKEzMOohPEmhfmUmEJJWHr93L8bMyBzE7UotE8wpgtMTXv9bw0x0cuD4AeQW5qJdaDuMGzKuTHyPbxOvrG50RjtvYvEiCIIgCILQMBEhXRCEOsNsT0LteuFC4ORJ4NNPy4V0qy94jWEaLM+4Exh4JBr9Twbhos2bjay1Zs3KNqVQnpsLrDsZiBv7RwMmIYDWMbzRlzzn00XY/ux0fHD8DtzafjUGHDZlvzk901m0zP7oZGSl29HtVAZCTW1hJjpFdOu5usuy16K4vn6eEvWsSfSCIAiCUJPe6BTSj5w8gqIzReo5LaRrz/QARwBOwLB/qfH4/dadGNj6ONofCsTlfIIBkP5vjLfR0fhy8ytKrF5bWoB+4W2NSuZOaB3DW2lpKXI+/RTb33gWaxtno+/pNuj6+7+VB00dv1u3Vp7uWatmo9vBU67xu+0kJaKbBXNPGfYUyXmjxQuz0ymsW4uR8rGnjHZBEARBEASh4SBCuiAIdYZZ5KUYTC9vhwO4446zsGo5W9LS8HZsKrKy7egQXghk5wLMWmvd2mhQv3641A7Mnw/k97cByV5U/Z9+QuSuFbjijAOHC0KBpkb2G7PEC2ZmIjGnEKErVqB0ayHy8iOQ0zIIoUVFRrZbdDSSMjORZPFY9QbH+FZx3DrxoEV2Fm9lVr9kpguCIAg16Y1OIT0kIAQlRSUIDVDysoKFRnkrYpyrrfidEYusw9noEBAOHDlizMqzYAiL4mZm4pb4WzBr4ywsvaIdrnUMR8wtYz3H7w27MNxxBoGOfKODMsbICi9YNhOJx3KM+J21FXmN85Hj3xKhFNid7UhKTTXidzXEbi2ou/OIN2e0Z5/IVvdsi4jpgiAIgiAIDQsR0hs4zJoRhPPl+0eHlF27DJGXY9YmTYDbbwfuusvwBD9n0tKw4C+peGODDUU9k/DSS2406tRUXFWSAtydAMQkG5Yu9EvRlTszM9EtPw2XOFLR6qRz0OyJbt1Q3CIS20sHYkCnw8DhcJX9xizxXTk2IAIYNrAIzfNWoG0zIKJpoaHQZ2UBUVFA06bGfjwI6R7qmbngbuIhLw9YtgwoKDAS5EVIF4S6R+K3cD5Bq5bikmI0bdRUFQ719/NHUOMg9bhGvvuM3+/9BW+EbEBR75546cqXKorIjN/bSoBL7jYmvmfPNqxbAgKAdu3UCrAHFh5DbtMwrOxwBp/0jsF4T8GT8btVC+wMKEDnwuAyCzdmhe+KzAEuicCw6IFovjgPbf2aIaKwKTBnjtF5CQ0t77R4mQinEM79USB3J4i7y2CniD5/x3xVqHXz4c1YOGZh9a+lIAiCIAiCUGuIkN5Aadq0Kfz9/XHw4EG0adNGPfbz84PD4UBJSQkaN26sHvsCvthmX213TbWZ+ykuLsahQ4fU95Dfv+pgFYD1YxbGZEIXPb45BuVq7HOycLEeKDUVzZemYESRHVmHU7Fxqg1JpkEuvdFPTInG70KC0PVeU5FR7oMNY2abzYb2/0lFz1MpaLItCEgprDhY1u8pKkLrnlF47JpAwDYWSI1R7zdOKQkhNrZpMsKbFiJ8WGcgJhFYsMDIgI+LA666Sm3vSTC32rZ4s8vh5MSiRcC+fcCZM4b1DPGRr64gnDdI/K5/fLHdDanNJ4tPouB0AUKahKjH/HdJaQkalTZCYWEh+F9A4wAE+QcpGxdmoLPdpMp9B3fxe9FSjGhZhKwNh7GxeCqSfmeK34ufw4kNU/C7kyHo2u9eI34zbpuF7RUr0GTPSsS3L6A/C+48aAcCnb7m7uJ3p56IoHUbJ9GdFm4qO/wqIIT3H6ci3NEU4T2HlQv3mzYZRUluvNGI317Ecj5vtm6xYrZ6+WrzV5izbY56vsRRou63524/h09REARBEARBqA1ESG+gcADSsWNHZGZmqsG4eaDFTB++Xt8Drarii2321XbXdJuDg4PRvn17tb/qYBWA9eOEBMNuxKRZq9fdJa9VJRu7woGio9GiXRB67d+MW/3molE2i34lOQX0abjq8EzknglF7lEj85widNo7wNgYIHGcs1ongF4jNiHgyyA0uXQgMDCwXO3XjWIW+5o1wPXXA9dcU95I56xBUupkk2WLyXuFj5kizrT8li0rnEaU3Vgurretil+8tsthrTWucqdVDmGCHq1hn3iiWh+dIAjniMTv+scX292Q2pxXlIfCkkKVcU74b2aeN/JrhEb+jXCm9AwCmwQioFEAChwFFdp9EiexJHcJ8kPzPVuTuIvfLduhw6kDuGxfIFrtMgnoH0zBVSsPI7fJGeQWHTXiN0Vo/5kY9dIojDkdXyaOtzxuR/OL/PDX4u6IWZ0BhDmreev4zRi8YQMwbJgRv80dEqtli469On7TsoYFVHr2rCCWR22xIynXtdNitW6xQhF9ud3wu/tx749lAnpgo0AENwnGvX3uPfcPUxAEQRAEQahRREhvwDCThyImM5TOMMXUuVw2NzcXrVq1qra4WV/4Ypt9td012eZGjRpVOTPOXESUWrRVADbfV9X/3G02tlVdtx4gMxNdYwrRNaIYyALAGmNpaQh+KxVX5s7GxdiEzYjHhtgbMchmw8wJQP81qWiyJQWItMM+NRWpsMEWkImYnoWGiD5+fMVGFRcbj8PDgSefpHJWScNN9OsH7Nhh/Nu5nY2Z67y3O9/Lgb5zMJ803vsFM3ui64x03TSudBcEoe6R+F2/+GK767PNX2/5GnO3zcXIriNxU/ebsCFnAzbs34BBkYPU6+rfsYPQM6Jnpe1m3+GfK/+pxOVTOFUupFclfgfHoOtFfYH8fKB7P/X0jIwZ+NfybPSzA5sjgA0DY434nTEBhSuWoOTbXcDIp1VR0Oyt6eha2hRPxt1u7LetqZq3js26MGmXLkaBFvO1Zv0Sc/y2eqcxa10vq9u2zWj+3U6xfJ4dWFYev3lcrojz5nHOTHR9H9g4ELO3zlaPg5oEYdyQcRg/1NT/EARBEARBEBoEIqQ3cChiNmnSRN30gIX/DgwM9KnBoa+12VfbXddtZqb3jBmGlQhtWwiFdOvY82wKiLrNxq5MpNYbM2tsxQpDtE5NxW0hKdhYFIP94WHIHTwKfcaxkYYInZZnw+kYvsmO0rkpYIm0dUnRiOFAWxcWc7f/lSuNgXhlDbe2mZlvXEbOpeiWYxS3jjaeo+drRobn8/RSgFR/JrR9Z9L8O++IR7og1AcSv+sPX2x3ncfvxc8pkXp0wmissK9QmdGHiw/jjt53IKl9krppzP+uSrvdZmJbY6G1Y6BjJoVo2rQwVjqLi27uehgxYUHITR6MPg+PU9nio5qMUiL6tTv91Ex+haKg3vZ/6BBw9Gjl8dsq/kc7YzRnrimmm+N3ZOvqx29T8VHe8zP5cO2Hyo/+k/WfID0zXQnqUnBUEARBEASh4SBCuiAIZw0F2507gdatgUGDjFpdTOiqTua5J6xjYI5nN9ptsLFGqFWkNmWAqQxyNsJZOJTPXQTgImejEiqI0ElIS0vCnJenoXezHWjXIxq9IzOBHOf79cGdPuo8VHb6YXQuLgS2bwdeecX7CXtKzzeJBRtXpyF8biqKQtKBgq3GvuiBcxYG8kuWAHv2GIVcaU9LW1dBEARBMEMRfefRner+6cueVs8NjBmIyT9P9lgcs6pYi2jSR3xjKztsgxPK47eb+iQqfnOJG1dt8XlncdGrksep11ziNwXoJ+Nh/3IqlmRtRsfjzRAVEIKIwiblsdt8HPqZ321D1n9exhC/YmNlmTV+WzsenibCKaI7+xgb561G+Py5OFIahTgWJO3cGUhMPLv4vW8JsgqyVCHXM44zsOfbkRidKEK6IAiCIAhCA0KEdEEQzprRow0xnfcTJ1ZcFX1WeKhUShH9o4wkZCUnocztxGZTenRWuh3dlqUgVB/Y6iVTSWN4uPC0TPRCIa5LdA7oU8tFb1q+MFu9eVQQSrMKkZcfgUOtgxDCoqHr1rmecGVZd/qxSSygpUspUtDCUaxWtK/5CWCJt8SzKMRKbZ/6AL3S2fxx46q/D0EQBOH8hpnoOiNdZ0ZTRPdWHLPa6Pjdyo6PGmcg69pkjDfFyvyvZyGntAAR/iHl8VuL1VpcJ55E6aQkfFKUivA35qLDSSC+70hj9ti8vfM4W1fNxoHmQMm+vTieF4SArVsB3vRxq2I/Y131xvi9xY7SHUCzmGDYT2djZvGPiLzmCYy5pPrXj8VFi0uLlRd9u9B2GBAzwKO/uiAIgiAIglA/iJAuCMJZQ/GcN01VimN6VYCnTjUytc3Lop3CNDPRKaKrfZsGu5/EjEebb59Dx4JMY3Cr3+dpYOzG252Z9IEjbfD3IL6vy45G62NByG0di45R+xEVArRpWohCGpHrYqNeLoKLh3y8qeCZUyyI6RcN7AgCBl6BD2cE4vRuOwYtNHzbK6+46sq99wJTpgAhIcbxNm0CHnvM0BZo584V6YIgCMKFzcRhE9XNTGXFMSvFGm91/B6coER0tV9TJnr6JRHI23wQQ3fllsfvanQkWHB09pbZuCwxDv5degC3jK0YK6OjlVh/fP8+xBc2RUmblmge1hynunUDOnasILpbJ8KnNdmk/NhpJTMG8cZr9LLT8Zte7mt3AEGhWH9kDZqvL0DJiy8AIw3f92rF7z73YsrqKQgJCMHfLv+beu6xeY8hJiwGTw5+ErGNJIALgiAIgiDUNyKkC4LgFj3W5dh2/nxDiGV2s7fx4Nl4oZexfj3w3XdAz56utiZOT1KKzeO11/fkVJdCnS0+XIGww9lGhpi58Sqz3DIwNr326qtJSmimhn/ffUnIsiUhxtx+57aDjqTDH1vRuCgPoS2bIvzyziiNSUTh4MHAkCGuxcr0ReB7nT43M2cmYfly4+Uxw5ztSUgoP0+2h4PywEA0fno81r2ThqQYpsTbq53iz4kN1kPj2ziGX7zYaEp6OnDgAPDSS8aqdBYmrQkLHkEQBKFhQSuV1F2pyvt8wa4FCA8Mx4u2F8v8uKtqyXLOE+E6fnfvh/FDncf+2Ll0LTkZIX+bhNib70RY3oHy+I2q1UWhiP6n+X/CieITQFQftBgaA1tbVf7EeRHKJ62Z8X4qKABtmoUgvHUMSg8dQg69z/7wB9f47UbEn5kxU/nHkzEHhnmO350743T0lYjatBIDnL7t6nk3bfcEJzZYdJSrAjLzM7F4z2KkZaYpr/QDeQfw0uCXsP/Mfizaveic7XcEQRAEQRCEs0OEdEEQ3KLHr1u2GLWznPXyyqw/a1yA7dULKCiouHPzMm+NLvgVHW1s+vQoYCYNXgfC/uhkw+rlVIaxVNxT8U+7HffkpGJdaTQ6Z2Vi1yy+nuR6Xs5tW9ErJRxA95jyC9C3r+Fx7gmTADBqlLFTZogj3mI7Y8Zmw5gkw7ddyQFmr9hqYD5lvnXtWuDIEWNXFNBZJ5WfKREhXRAE4fwS0ZnFvDdvL3ILc3HqzCmcOH1CCcLxbeKVwF7jIqynifDK4jfb8MjT5UvDJk9G9pZ0nFqXBv88O2KYYU7c+KqXzH0B3VsVYMtFISpju4IljY7BEREIDY9APAt8M2ZHRhpxnH0OK24mwkclMHDDuO8e7zV+JyYlIdGcheC0f6kO5lUB0aHRWJu1FkdOHsGOIzuwaNcirDy2EtknjAAuQrogCIIgCELdI0K6IAgVuP9+YNYsoGtXwyJEi666PmatCLBdurhmd6elKW9y2qr0Tkg2CpSZMszU4Hz1aqTPycSiLdEY3n0YEg/bUTo3A3tPJaCgVzKGufNI14P47GyMCs/A0JIgtAkpxOEAO6LsqUCaaYBszoo3L9FmO1ikjD4pTO+22TB3LpA9IxWRo20YObE8E4/3qqiptnRBFQbV1vM0ixCWTaxNI+ZT5v2cOYbGERZmJAsyS51zA4T/VpYz3hMVBUEQhAbOc4ufw1ur3kLBqQKUOEoQ2SwSeafyVEY6hWCK6DXqgV7JRPimXtHI2R2EiF7RiHcTv+1bVmNGZDb2P9oZ438+jJgfMmBvVYy0zkBgHDDWGr+Z9c5gGxWFa4v90OpIJGKaDECLgIuxb3M+IiJN8dVa2FsXAdVBU+Ns19zYIrxdukJdpzGpmWUT4WPGj8eY0/HAd1zJFm8URK0KJvsXTysGKJQz89w8sWFeFcD7Odvm4Lsd3yE6xDi3LYe34EjRkTJbmwf7PVjpSgNBEARBEASh5hAhXRAucMwuKGTsWMNXm+zaBfzzn8A77xjWLtddVy7auvh+18QYTi8Nt9mQhiQUTEhFu/UpCC6OwKmW+QB9xHlw87Jqux1NFqag9/Eg+O0pRFrPBPglJSMA0egcaRm8WgfxnTsj6p5ERDmzxi4KzTZUZmrdRF8Ud4NmvkZlmh6rzmJl2TOArjtTsG0G12dbCqbp97DtFNety72ty9dNmXRajHfXBG5CQTwryzgtb5MbjZ2/9ocOAadOAaWlRp1UPz/jvSKkC4Ig+B5alKUY++bKN5XI6gc/NPZvjA5hHbDsd8tctiVFJUVInp5siMY1LMLyGBvnTYVtF7DLPxsHcrdi74HViF9fMX6XpsxFZMhhFIWcwZutQ/HrSy5D0DXX4VioIS5XiN96Vj8mBlGJN+JGBq9fMoBt+YhjXG2yGuBxzJPoVt92ZpozfjP2L1tWZkeTHZmJ5X2M/Y+JHuUae00r2SrULTmb+O2c0CguKUbWiSzYj9s9TmxwMqR1cGu0CWmjstKPFh5FXnGemiDh55x/Ol+EdEEQBEEQhDpEhHRBuMAxjw9p/aFFdNK6NfCnPwG33AK8957r+yZMQLnv95gaXBput6NgRyry1u1E98JM9G5kR/MDhw3rlkmTjG11Rlm/fjidHYN1W6LRKDsTX6TbEBSchIVXOT1YKYqbBrv5s1KwNSABUYnJiBlrGlRzAH76tNqnfWcRdixORWJOimEN424grmcdYmNxoGlHLLLbUHI5sI2D3tGu3u5lg2h32e0aq/2M873H9hyBY8cuFIeuRqTlIutN6X1OIZ36gnMlultBvaTEENHbtAECAgwhnad85ky5ri8IgiD4FhRlZ22cpfy0S86UqOeCGwcjPCgc63PW4/5v78d717/nIrhPWDSh3Pe7JkRYc/w+vQMh29ej9EgALvdvhjOZx3AqJBt4xGTT4oyr/qHARRk/otPGTZhz+iR+M3gzMtqMQzwzwgMBtNUnmYr8r2dha9sARN0x0rB8STLZn7EwyK5dOHbmJI7/PNewhfE0s6yDZ1ER8md/hu3RTRE1OBGRPa/AIGdGOt5fbUySr15tdHDM2e1Wz3YP8Tu38AhOZO2C/5bViIElfjsnCeh9TiGdVi2Tf57s1nKHInoT/ybYengrCvwLlE2PxgEHCoslgAuCIAiCINQlIqQLwgXKzTcbRUT79y9LDlOrnym0Hj9uPLdkCZCbC3z5JTB0KPDqq4DDATz5pNPvW/t+e8IqQHt6jX7jemm43a5E7OLTmQgryUZjqvmNImGPHYhPUpNUcdGk1PJiZYmzxiMRQIcOwP7TQOx2lU5WMRPMZkP6YmBmjg1xMUkYr5ujB7+zZys1+tSPKzAzZBIQAcMaxlvBs9hYvLLiDsyZ64+RI4G3tlbi7U7i4yvOPFiXrzvfW5BdDBQA6SuBdmmum+i3UEdYswaYNw9YtapiEwktYZmRzgQ8CufUHCikMzOdXHSRl89QEARBaFBQHKd43rVVV/y+/+8R0SwCOSdy0C+mH/bn7cfohNHK4oWi65ebv8TQ9kOVeH7y9EmV/ezi+10TmOP3Lzk4dDoIbfJOIyioFCgCcg8fweSiVNjutiHpIMpmfWNefAsxaWl4/e/X4YfIQ/Bz+LmPt4zfuxdjZmQO4q6KwXj9vI6zXD6XnY2CxkVIiW9k2MLotln3xxv7HEuXYvHe7/Fhiz1oPzQRb107ESPLTmi16/m5y263vqZxtinH/whyiw9jee6PuOJgmotAru1bOLnx8NyH8c3Wb7Bk75Ky18ywYCyFdl6bM4Fn0MivEZo2bqo+S2akX9RCArggCIIgCEJdIkK6IFyAcCxI3Zii+NKlhk82fdH37nXNPudzFNH5HG1cdLY6/81xaaWZ6O4GxGaLFVqp6KXSgwcbCv306Qhlhlni5Yb/uLNA2I79gdiVnoZ201OxqbAIw4OCEGMSyu+9F1j/YRoe7JIKrHbjLZ6UhJBJSYizjIH5/LRNSUBOEW4IzEXA5QMxOADobD4PdwVLmX1HA3kMcX/unoqcmq+FJ/R7dhYh88sVSPPrhy2pLD9acWKCp3jggCGSUyB3OTeU2/UQTpywaGyjRsDJk4YdPd/To4f35giCIAgNA4qv09dPR2FJIVYfXK28suNaxKl/J7ZNxMIxC9V2B/IPKBH9lh63qEKjmQWZSoTVWeiVZqJXZSJcr7CyxO/QA6eBI/uAMADh4djd9CR2LZiFdjum40DOcQw4GoQokwjd6rmX0TJjpiHs/4KKE+GM320nIc6ZUV8GBfaD6cjy24EhRYHANSNw7OZOFbZxuddtHzwYqbcmImNHJtq7C5r0s7O+x5Pdmxnne1puScfpTDsCcnLVSgA1gWC5nhTNdx3dpQrC0qrFpd1O9GTHxpyNSjgPaRqCUkcpTuCEsu/p0VoCuCAIgiAIQl0iQrogXCCYx4Gq5qUT2nyQH38Ejh0z7q2WIPTSZsY1BVuK716z0M1YB7BmQdnkk1rBr5QieKdOhqLvbHhEtA2jZqai+dIU5J4MwpHmhYgxCeUTJwII1GK1c98WVdmaOKbhxEB/eyD2BUejV6dAjKUnjNkaxpyNxkw6DvCvuUZl4d3dH2jXzo2A7e49btpU4cMxHS9m8mSEdShESPEchE6fic3TQxHTNKfccsZ5aRcsADZsAHr2dP8x6F0+9hiwe7fRXtq8tGoFXH65csjBo4+W6wc1XkhWEARBOGsollMMp6jK4pRhAWFKSCfbc7erLPRjRcfw457yAN4utB0a+zXGot2LMLzjcCRFJyEmLAZje5flanvHOvlr7UTomh+M3db43b27EWgGDlSztcG9ojFq9kw0T1uqRPX9fuGIMgnlStjXBT09FNk2F+EsfzIJz2zJR/+thxEbHIxexQEYvxSutjDWWMz9M2CGhODum+5Gu7B2FQVsd+/hxL9+zRvO90ZOm4aAPXZceqYEbcZPx+aA6YgpbOoSvwknOjjhcWn7S5Xgrs/V5dpcMga//uzXyMnOQbuAdjhQcABN/Zuia+uu6vM0fz/EL10QBEEQBKF2ESFdEM5z9NiXXtr8N8eDFEuHDzfqezKTm4weDcyYYdzr98yaBeTlGdYht95q2KBW77hOK5YkD+I6X3juOcPke9s2YOVKY8l1cjI2RdswRYm7SRg71tgHXVHSXwYy1xShR9CKikW8rPs2XwBP5uFOe5q0PBtOxzjfy9R7c0acNYuegjiz73JykBhhCNEe0YID3+Mpk81TtrrNpgbdPWbPRtGaTTjaNB7pQ240LGecbUqy2bBwYZIa6yvtP9XzOP/uuw0LmOBg4NJLyzPVtd89tQvdVBHUBUEQ6hftaz57y2xsOrwJeUV5Kuv8V/G/UlnMFM5p40JmZMwo+zffR2uX3MJc5JzMQZeWXVyKjlbluBtb2WEbnIAYd6uqylZN7TQ84Lh6jP/mbC7jd69oTHHQHuWwEnopDMe3iUd6wMtovGklYo8GVbQ9s06ym+Kc1/idMAppg/NwulsMkJkNzJ3rWn3bGr+5f+dEeGJ0Ivq18xLA3bWpwsXy0MbMTIQ7mqLP1lwUHdiLo1FNkX7lkHLLOCf0r+eNHuksQErcFR59YvATmLd2HjYVbkLXNl1VEVK2nd+P6RnT1YSKPd+uJlrcea0LgiAIgiAINYMI6YJwnqPHgcXFxmMWpeRzL73kOuZjRrfK6nbal/I9Xbsa+jbvV6wwio+6Kzzq6bi7ZqVhwOJUYJJpgGk296aCy8EtG8V0eG3oPX48vp1sjIcJV1jrtycmAomRh4EMU8aaZdk1dz1zgiGQj8ms3FKFFjVjxvC18sKkSlWeM8dIV3fay3gdTFsxL32v7D2elp7rZeRFRXAcyMWxLiMQMmm80Uz9ITnPy13yvxVeO/qo81TofX/nnUBYmLHSgCsPeKMewutuvuaCIAhC3UORlOIqM8nDAsMQGhCKjJwMJHdOxvihrhOzE4dNdHkfLUCKSooQFRKlstdbv9xaZT9TtK3KcXftX4YORyLA+WWFOchwspmecOb4zclnziqPGYNvf56MuelGAI9pHmOIuklJSEyahUQVoGd6nAifG1uEt0sXY1STaIz5LrPy+G22qdFLq4g+jrv4zQl7Pled+O0pIHrK3Nfv3bkTjoXzceyyLgj52yTA5BFv3qfOindn70Io+q/augo5h3PQyL8Rfsn6BZsPb0ZJaYnyT2eG+sG8g3hz5ZvKB1+EdEEQBEEQhNpBhHRBOM/RY19tZcpxr7txKRPDdUY6bVz0e/U2FNF14dGqCOl8L0V0Fg4ts0gxwwEu06DbtzeWf/fpY/jGONO7r49OQ/eoVGyNsWG4rVzgzp+Vgm0BCYhMTHbJlOPzLCZKH3S9azJmVDSOFQfh5/RoRFkKdloLr44YAXz1lfNiURTYsgXYt89Ihb/xxvKLWBXMmWxUpSk8eMqss3rO6Pdq//jDhxHUMRqXXhVYpvVblXP6pyepC83HSZXWhHv9dUP/oGc6C8zyI6BvOrPV6ZlelbkCQRAEofYwi6u6OCVFbqvY+tzi58oy0imoW99HEZ3Z6bQQqYqQzvcNyF6sCoeWLXMyxym9jEnH79hYwwPOGR+vz49G93VR2No7BsN1W9PSYP9yKrK2pqPbsVMItUyEM4v92yHA7C3zVfY9GRM9Csf8ivFzfjqiLAU7NTd/ejPm75yPEZ1G4Kt+NwOsscJ+hO4InEv89raSrLJ6KDr2BwQgqH1HXNrtKoDt/9h1ElxfgyTnCjO1jQd6RfVCQZMC/CftP9h/fD/25u1VhWS7t+muJk62H9mOwtOG5Y8gCIIgCIJQO4iQLgjnOVaN1pzsbIYiOldm856Z6dR9J+is7jFGJjqtXkZGpsH+aCpixnrJ0NLjQ2aiK23XjSqrjdadHqrKY5VZ4KtXq8FuvN2O+KYZQKJpsGmzYdFs4MO9NgTEJGHk1DTYpk5GTL9opEckY2aOTRUT1btW95mZOJ5ViANZmdjowfaEY9qep9IQ/20qkGYzBtuFhdgd2B2HG7fDqZ6jMHT8mPIscC1wUzxgCrc7cVyfs565oDDP8yu7OF6Whlvf6y4T/iw9XLt0AYYMMXzVDx8GmjY1xPVJkyQDXRAEoSGhhWOzd7Y7MZki+s6jO9U9hfRNhzZh8Z7FiA6NVtszE33WxlkYmRcJ+18fRcwt3r271DF+N8l9Z4GYg+xddwGffgps3WrE8cmTjfi9uynQPbFcGE5NReG3X2JjeD62xHVAeH46erMt+VCxK2d3EFIGFpZl36sim99l4vixLBzYmoWNLNjp5twposfvLUT3xbORHdMEkU5/9Z+HxCLgwGqcurYnhlIMt8ZvFkiNiKg8A0HXOGG/wFtmuvW9nuK3+d5qO1NZ/G7ZBUO6D0HRmSJMXjoZZ0rPKH98ZqXT8oc3UmUffEEQBEEQBKHaiJAuCOcpVsHcWsvSitkjnbhkdY8xstBZ/zP83VSUzk2BWu/tacBnXtrsrWEcZHKQymKjvXsbYjPTpLWPqRvxeGFiErA3DcNWTkbMyXQ0O5MGYCRCJr2F61+ehqGzJyD8wVEYk+Jc6p1mg78daJcdjZH2yYZQbm73tGlY0HgmDp4KRcemzL4rH+i+PtuGaSVJGLQfUPljOlM9Oxv5yzcgM6YbguxbEeZsm9tzZ2Ycs9JMA3Fz9r+y07EuDTeL5HpfnjLpquLh6gZa+0ydWt5EXYBWxHRBEIT6xZx5ru1diCe7Dmaimz3SWXhyud0I4LQ9YRZ6pxadEP7GuyjdMRcI8+LdZbUWc/e84YdmPPfKK2V1TvKz9+No6QkE9E5CpDUe2WzYtGU21hdvR6ei4whavhSlRwKAoSNV7OqcsxMfTPkRfqOvQNwfnDY1p9Pgn2dHO/9sjJxnBwIrLitjJjpF9Ot2NsL+5nZEJt+ojvXClsVYProEg2L2V4zf6cux/eAGnE75DLG3jHFfU8Usvpsmwp8rmOuS/V9p/LaK7+ZOmN5/NeM3j9uxRUf1OQ+MGYjAxoHiiy4IgiAIglBHiJAuCD6Mu3EaBXCK3u3aGZ7XmkpsRpVPNq1beE+YbM3k8KEcuE42DsLCoRvtNvhzA2+FwKjQ0mg7KspIeTYf2Dro1INbUliI7JBO2ByUiPatoxGHiuKxKpCZPhXdts/FyUbNEMzdZ2cjKZVZ2bONVPp38lyyx2J4K6vGabkIM2diEJYjq308cIMxANcD3aRoYPtM4ObYNCxOTkWPULuR7da5M9YUJCLDLxbHIjriCvPg13l+9twgZO4qxM+hyWj18nilOegPLfityQg/asOMGUmGkK6vgXXiQQ+4LX7oZl5YYUPJKqBxCxuefsuUoV5J5py7sbx191Wo8yYIgiCchUjObHFzYcj0zHS8++O7SM1ORadWnVSWcVW8s8mB/APKuoX3hNnctPlYm7lW2b5ou5eNI+zw71JJMQ2rtZgOAO6KYvM5ToRzsphJ2GF++KkVEDgkEmN/Z7FESUpCuxfexPXPT0D3rbtxpHlTNA8PMXzj6KW+fAOwNRv4v/lAUaD7+B1mWVY2bRq+mlkEe6/fYUen/Whx4ygg2Qi2o5oYWfO/9x+osvCzt6ajKy1lGL9bFeDoiUNo//NyIKxdxf6J3Y4v3noYqUWbYOsQj1tt95b1KWYsv9Ml+7/S+O0lJtMPPjsyE5GDrsDI0eOrHL/N3vD6++RtMkYEdkEQBEEQhJpDhHRB8EE8rQaePh344gtg+3bD7/rhhyskhHmE3udmD/QNGwwv7RZrUoGjxsAyKSYVScrSxTlAti6VttkwbVMSOnyWjYH5xxDAATKrWZoHmNblzU4bFUVQEFajH14tHINnV0xGXGFFdVf9k6uXs4DQuEjD5JuwHRzM83gsLmYd8FuXauuB6qhR6ocwRnvYmNBJdxTRw5enYH98AiJvNLLGQhx90WhVDkL6RwBJamrB5TgzP4nGkcJMLCqyoeVMYEx8+Yd2Q5MMhDexo2WYq5WMx6xzm824xHYbLrYk5L2+JAm5hUmw/ZCGp3le6enGF4RvqKL67a5QKXdx001Gk3SRUkEQBOHsmfbLNLzw0wvw8/NDx/COqggoodD58bqPsSFnA/Yd34dSlOLhfg+XiaCVCaH0Pjd7oFNg5XE67DiE4CVvAfs6IikzE0m2scDvLOIu0fG7ySZg5X9xw+4ChDdu7NrBcBcotIjcoYPqQASMGIBjiYHKI92dIKzOY/AoYO9MRJ0+DezaZRQo4YR7VeK3tRPjXDpHGT9Gv8dahHTyZOxLmYu9rU4h/5JeGDZ2LEKiHTiYOg/+EV3dd5LsdkQtWYvQuBI8cPUu3Kr7BmlpeDEtDKlFgbAFhhnXsbL4zRouX89C+u7FCGk7yeWz/Eve59h88U7cuv5jjNwfWGWLF5fdu1mxQBH9pk9vUhM1iZsTseo+CeCCIAiCIAg1hQjpguBj0BbkrbeAkBDD3pPj0K5djddoU1paaliO33uv66rsysZkl15qjF0jI42xIce0qtZmFxtwFbDyCzvwfgqOLwCuWpjk3gvUbkfJ96nKezu3STjaciBu8j3n9kZJzKTykpiWjPR+IavxbFAmOscWAfvdZHnptHQ2UA86O3c21F49aDenUluXaj/6qJEtT8FZv8cyANfot/YcaJxnxCguaTfOPbG0VGXtWy1W9fkV3QrMcQB+fk47WZMFS2bvZLRcb0e/w84MeU8ZbaYP75PUJKP2mSUh7/FL01AyPxVD2/EzyACKi71+zpxk4YIBs77hzu6HzeVHxu/Ttm1edykIgiBUgXdWv4Pdx3YjOiRaWXJ8vvlzfLL+E5WdToIaByEsIAz39rkX44dWUuTSBD3QmSFNiw+K9RSQaT0SvOAt3LYvBNvefh5HjxzA8Z0LcFXSQtfscqLjd873iE/bjcaH/YDm2Uacdfqeq/h9t2E1YzvoFG0Z2xkgWrVS8Ttux2GML2JsXu1ZENbCs45VPEZV4/e0aa7FW1wKorjPyL6+VzSa541EfhzQ/tqxyrNdxe+rYxFxVwTgXz4RntYWSB3CYqn9kHV8JdLDtqlra85e6Lf+MNqVNEXbxofLrde8xW+bTYnoMyNzEGfxeb94TyGuW1OKhOKjgF+K0aHwsi+uMpi6fSpsncqzzN2tWOB5U0TnhMy2XAnggiAIgiAINYkI6YLgY9Bb+8gRI+Oc49tjx4AffzReu+MOYNky4MEHga++AoKDgREjjH9XBmt+/vIL0PlYGgompOLZUTYkJiYpOxcObl/+Txq6nkpFxgYb1pQlmjkV2GnTcCx9BzJ/zMbwUxn4pV0CMgc8gLYXFwErVpT7nnOAB0MQvjh9GpA/08g+46A6PBy45hpE2u2IzEkxRHSLCG/swHVQbV+8AzN/7IfIJ8Zojdv7MnW2hfBxTo7XWYYyrSE5CeNTqpYdtnFqGsLnpiJwpA0ZGWaflPKMuhAkoeDlaWhu31Huf27OaLN6qqSl4U57KqISbLiYn4dpm6ebpANRacDFSUBisvGl4DWn8bkbFi0CPv648oR17oaTNUwavP12sXkRBEE4V1hIc8OhDUosP3zyMA4cP4CTp08qr+vnr3geq4JW4eVuL2Pe9nmIejUKIU1C8LdhfzMyq73ALHQWnSxcsQQlK18Anow3bEdCRqof7nEbXkejrFPICNmANT9PVuJyPJye3Js24ZhfMTI3/ojhR05hb3QbHIttg5CgSKOAaF5emUUbRWZmP0dtsSMp1xRPufqsZ8/ySXUtCJtE+ArZ5UVFsH//OWa22ILIa67DmEuSKo/fs532bWyT7hd4mAgvy9TunIzxL76FqpTf3DhvKsLnz8XqESMxduoq3KpfMHmZ+yePxOmszWi++6T7+O2Gzi07Y3BsZ1xMsdsUTJ8s6osm9iNockkfoM9VxgQ/rzn7PZYVcmTRrkX4eOfHsOfby4R0dysWODHTKrgVjhUdwxUXXSE2L4IgCIIgCDWIyY+g7nn22WfV8lbzrXv37l7f89lnn6ltAgMDkZCQgHnz5rm8/uWXX+Lqq69Gq1at1P7WrVvn8vqRI0fw6KOPolu3bggKCkL79u3x2GOPIY+dcie//PILRo0ahdjYWLVNjx498MYbb7jsZ/HixRXazlsWCy4JQi3CApUtWxrjVI5bWQBUFwi96y7+XRn3XC3NsR3vqwLHoyNHAn9qNRUD17+L+NVTlVatx7697k3Cl13Go6inIYSr4pR6efjq1TieVYjNuZHY0yMZN34xFomzxhup8WwE09ydhbR4HP5zqN1ZzXTzZmPA3by5cSAKwNyAGWa8J/Qcn5qKxfdMRfbz76p7HpoD1yMHCtF8y2oUTJiMqY+mGc+npiLroxR8eGcq1q0oMga4PI5OuX/gAWO2wVNxL+d5XR+d5rqJPl91EGd6Nwut8bHztes2v4xfH3sX12Q7K3lqeCGdF5Q6QM4vmSg64hx864uibWeYMl52kY3ziclIwVha6+gxMLd5911j2QDh7MrixcDGjZUO6qsCtfgzZwyv/PvvL9czdJOECxuJ34JQfcYNGYchsUPQpJFhSXZl3JXoG9VXeZqbYXZ59ols7Dq2S4nsVYH7GHOoHa7d6edaPXr8eDT77UP48oYuKOrdU4nLqw+sLn9jZiaOH8vCZv9c7BnYA5f/ew5ilmYAPXqUZ4zr+B1nQ3LnZFyzNNuIP4w7XBLHuP3kk8ZqMW7LOMs4xECSkoL0j1/GKw/0QkZiDF75772Ydk206h8cOXIAzddvQcHzEzD1/UeV4Kvi92cf4cNX7sS6PStc4zfbMmiQce8uIJlitG6rN295K7ZdQPIO4971BWeMHjsWi37dD9tP7EdR/hHX+K07CtZ+AuP3sgyMzY0xRGxTME1MvA69LhqAHj0uRU3CbPQm/k3URExM85iySQV3XuqCIAiCIAiCj2WkX3zxxVi4cGHZ48a0gvDAsmXL1AD5H//4B6677jrMmDEDN910E9asWYOeVBQBnDhxAkOHDsXtt9+O++67r8I+Dh48qG6vvvoq4uPjsXfvXjz44IPquc8//1xtk56ejoiICEyfPl0Nxnnc+++/H40aNcLvf/97l/1t3boVzbUASOsHq8+DINQwFLtpnUI4ZjVnBzOZiV7W/fsbmegU0XlfFcrsPR4FTn0JpKUrn5Ky/bMoJm/mzGSzXYn/yGQ0zY7G6SOZylamoDvw7HU2xCcDu4qisW9xJiKigaQxzn0WDQRyd5UPuJmFxRv/ljlIjo83MrK4lHvHDizaHI2ibXa0Lwb27gWyaHFis+E0E7JX2tH5ZAp+ngukxrBIaDT2Hw7CyhPRSMidCZRkG0vPb7yxPINM718PevXzPDHnebHt8eNNF9i8HL5vX2D9eqPQmiYlBa24XD0cQKTna01b1/b7opHQLAhR5iXczEJj5n5CguvA3NvScf7msLIsM/b27zeUb14/D0vDhw835jd27gS6dTMmYVTBUwucx6BtEK1pyj7vSnz2hQsLid+CUH2Ynczb2N5jXbKDJy+ZjK2ZW1HQpEDZskxJm6KEUKvI7gmVtT4SOPbRO5iTn46og2ll+2d2Om9lmcnz7MAyZyyz2eCfZ1fWJwX9+uH2bS/Dvs6OpweNwMiYB7CpVzS+Dc2Era0p+/nN243lcMxIZza6nlTWcYfCuraFWbECi4q2oPcPm9B+3xn0zs/CPy+diTG2STh9MB1Rm1ai8+aT+Nl/LlK7xxjxu/gwVjpOIGFpLpBdUh6/rfYv1ponphidNH68a/a1ufPC+K19zkyxP6Z7P2DtDoD3Zkz+ZzOnT0B7xz4kFDcz4re1qKjV59wav82PdTY7O2ucyIuLMwKzhxVlw+OGIzAsEDuP7US3t7qp74laeWCBkwezt8zG3ry9ZY/N94IgCIIgCIIPC+kceEdFRVVpW2aVXXPNNXiSWS8AJk2ahAULFuDtt9/GO++8o567++671f2ePXvc7oMD9i9YjdFJp06d8MILL+Cuu+5CSUmJas+9VAFNxMXFYfny5SpbzjoQ58A7nJYUglBHcBzIMRp1VqvFBm07qEWzSGhV7Fzc0q8f7N/vwPdH+qERxWq4enq4+Glvcg4I+/VDzJgxiJk8GesnpyApz46c7alYHWlD/FvjsS95sirWSSMV6tdqf5wN4ECSWW8ccFMA7tixfGk44YEoLm/dig4hq/FS87E40SgGpy8zMtv5euKsJCSmpamM9UA4n0/NRGzrQgxolYlTl48C9juVYQrneom23j8H0vRM5++Qc/m6R9XY+nyvXsbFdi6PV9fiiiuUUr0p2oZvK9ZaU7ApYZtWo/tJ5xJuDqadExL2hGSkwrBwSdrk9IPVRdnMy72tPvH6OtIqh5Y17mxi+vZFly7AkCHG5hTTaRXkTkjnYfhZvfyyUcSWkzTjxomti1COxG9BqB4UsTNyMlSmtNVigyJpyOkQ9I/rj37t+rkVSCtl9Wo4tm7BKf+9SL0mFUkH4Rq/tRC+a5ohGDuF4JikJIxNS8PiKRPQ8shP6HLsFOYPBUb+bRlWv/+osjvZOMKOJBYqJZwA598OAwn9zSmYc1JZr0JjB4UrsBhvCwsxPLA7Fl1BF7IjWJfU0pgcuCQJiUmzjPj95VQExjmF3qWpiG3aGgP8WuHU7ZcDS/eXx29zkVS9f7P1C8/H00ozTxPhus0a54ouT3YobHvJt7sQ29TPiLPW6u96IpzXhPfavk7HZLMVjBbV9SQkPfvYD/GwoqxLyy4Y0n0Ievy7B3Ye3alWLrj7nrC9b177Jqaum6pWNvDeOnEjCIIgCIIg+KiQvn37drRt21Yt9R40aJDKVuNybXdwMPynP/3J5bkRI0bg66+/Pqc2cFk4s9K8ZdNxm5b007DQu3dvnDp1Sg3wudR9CBUqD3A73jTHjx9X96WlpepWFbidw+Go8vYNAV9sc0NuNzOK9b21aVdcUYqQEAf69eN36iwPkJmJ1qFFGBSRiZDhpShlRjgtGA4cMAaflm3VYFFXpYyORovYYHRplIPL/DcgzI9P90WbUcOR4wdE3DEcpRxwcvCqB5vDh6O0Tx84Dh1CaZs2hrJLhZcDTO6TKdH+/ujWww/D7uyLocP7qnE7YDrHTZvQduePuPuOaNXG0k3RiIgLxm/4+K67UIq7jO34BusFdO5fCfk8Pz7fty/SHX2xKBUY7ih1Hs85+HZeA/X96NwZpVxmzvfzvHgtOCh/8kl8+wpP02ig9bLRegdpfsA8f5Ty+DxXGtr364dPMu/C7s/S0X7nZJTmfWMsaQ8LAxo1MgRyCgdsIxvFHVPh5gyKfo6qN6vO6uunrzfb3Lt32XeamejcjL76nr4r3D3nCfhx8CPWh6xrGurfYm222RfO9UKK34JQE3jLDE6MTkRso9hzXhkR3DgIHcI7oAOP8bFJPDbPgrrz9Z46FYPmrUdUYCBO+jWCo0eM0dZdQOkOICAkG8h1ith6Ildndy9dasQ+XaPDkn1NC5PEMbPUUwnWBmshXz+O3oSoNh1x70ineO5JDDdne+s6LFrANxUbLRPCPU2Em9usZvqNbco81p3CtDXzXy0t43tMK/PsgxOwzj8bvfOAmMXpxgS7dZWYuR18Lz8H9oeuuqri9bNm0TthJjpFdN57gm2mgL5w50IENQlSFi8ipAvnAwcOHMD48eORkpKCkydPonPnzvjoo4+Muk2CIAiCcL4L6QMGDMD//vc/5XeamZmJ5557Dpdeeik2bNiAUGZwWKB/aSSzYEzw8bn4mh4+fFhlxnHptye4NPz//u//MJdZq06io6NVFh2DNgfi77//PoYNG4aVK1eirweliSIDz9HKoUOHUMTOcxXFFYoCFGj8Kd75AL7Y5obcbo7JOIYlulamJiamFKGheQgLcyAn5yzbPHiwqjLZo0ULYNUU5FDAZdYZl1ZbDxgbi8OtumH90qZonzUFncMPo0liFBKGhwGFLYCOTZEzZQpa9eqFVh8ajc7ZbuxfDWKZHs1rnZNTfq3z843scN7zeLfdZix17tULY7sYxy9rBpdmM6uMA09mci1YYLyP2e7mfXi7gKyySjuWyy4rF9lzcpRFDrP72VS+pdLvx2DTeeXk4IrY7ejbbT2i84uR8/BW4xyYaabP23Re6hyc7R08OAd9d6/CRXlbkdOtPwriErA3oBsiY5uiNQ5XbJQ+H16LKVMqnrupXebr/MAD/soq3t33yCr686Pn6n3uytu2F9rfYm22OZ+fXwPmQovfMhHuO21uyO2mHzpvxNq2GmnzmDFoEhODvoxlUX1RGr3JmKTVE6sa5+Tt0tO78I/p1+KOnnfgLj8/NGkahK4d4oEmTYDE61Rb2t48BgiLUZPppdrKjCtL3E0qv/aa6+S6dbLdHXryd8AAQ4znpL2n91gnwvX+GVM502vKMEjdmYrvdn4HOIzr7nEi3NxmBjznNsMzHeq9wzsOr/iZmM/LPBHeMRPhb6UgYSdQ2jnRyNrXq8QoinN7ttG5MgcOR/nnwddyc13P3cNE+ITLJqibPhdP+Dn80KxJM7QPa+/+PC7gv8ULfSLcVzl69Kia9L7iiiuUkN6mTRs1qd+CYxZBEARBuBCE9GRdSFAlhvRSA/MOHTpg1qxZ+O1vf1vrx+dAeOTIkcprldlo7qAocOONN2LixImqCJqG4gFvmsGDB2Pnzp14/fXX8fHHH7vd11NPPeWSkcfj08OVnQCzT2tlnTMWReN7fElQ8rU2+2K7jcTkUgwa5IcuXc6hzcyGY2Ym/U515vjQocbgz5opt38/shdvRevj+xDavBARtghDwD1xwhg47ttnZFsx60tne+r9mxpeumgR/AYNQpsuXeBPg3duz3tua97emn1NWxa2kdtwIEtBnF7JbDP/PrkP+oab35OejjWvLsLH9uFIfCARd2XuN8Rp2sqYzs+lGfstx3X3/bCcV8T+qcDW71A4Pwt+2dlwREYi6KIotdP0wCFYtCwCw4cPMXZH8cB5sIjECGB3KPBpFtC9AzZvjcJnOUPRsUsinoyeDqxcaQzMdVud12TLwgM4vTYDTfokILRHN+z++jASZtyLsAfuAB56yH2bTToG68Yxae6JJ8ouU9kpM2u9PvG1v8WaaDOzvBsyF1r8lolw32mzL7b77VVvY8X+FRgRMwJ39ruz0jYv2r0IP+35CZdddJkSSMuobGJV45yw3nMkG1El+fBb8yVyOl4OXHmlkeHNAr67dxv74GSv3qee/DXty+VaWyaUXSaYmbXOSWM9mWydCF+71qik3qED8KtfVTiOoqgIO05lYdHSN9G5JBnDeb5sEyfETRPhZHCrwcoqp9/xFmpC32Xy3lubyfbtiF2/HiOP5eP0Oy9gU3xPtA6PVttsbwXsiy7CJcnD0HrQYNeJ8FaDcaxfFk6HHEbOlcnY3jsW+9IW4ZKseLTOPAb8/LMhlOuJcP47JAT71/2EM3t2otFFnRCUPAy/RBWh/Zal6OJhItz6/bB+H7Yf2Y71WesxsOVAdOvTDb2ieqnVDjn1MBPua3+LF8JEuC8zefJkNXZmBrqmI/vvgiAIgnAhWbuYoVdp165dsWPHDrev04s1m8s3TfBxVT1arZ0c+rUyc+6rr75CE2bfWNi0aRNsNpvKdnvmmWcq3Wf//v3xMzvJHggICFA3K+ykVaejRnGmuu+pb3yxzb7W7qeeon0CrUT9MHRo5W2mYwtXJmvrUY1eSXx9tA3x1ziXH3taLmmzoSQdOL1sJzqU/Aj/3EbIP1qCJds6Yc/pvhjctwi9Hc5lytb2mH1FN2yAX0gI/IcOhT+LbHkotKWUXb2sm9uYl0izjfqkmEJN/PyUd3rp3BQEpNsRmbhIHa/pggyEn+Sm/TBmVLQhZFva6NKMyZbjOvHbsQP+06bB39014v4CA7GtzaU4lbcfpzsNxKVXBaq2Tpvqj31fpqH77FT0e9NWfjBeE05gMLN+zRo1CZDUtBWaB9gRaV8Ef14rCg6Zmdg0fQ1yZqaiR6gdkTkZ2L47AWsKrkFIdjT6Nc5Eq5XzEVC0CQftx4DVmYgZaywNV9/ptWvhz2vpbDcvGYVzusgwGY9NsV5qdx+dt6/Ghfy3WBNt9qXzvBDit0yE+06bfbHd7215D0cLj6p/Pz7y8UrbPH3hdKywr8C+M/twx4Dymc7p66fj0w2f4vm0UPT9Jcd1Utk8Ee6cKW4RW4Qxb76L/lvWIfCiPUCLFljV4iSWnNmNS491RP+jweUT4dZJcHcT4fx7Nm9jfo+enNf7s06Ec8UKJ76HDXP6nwGbv5uOnG8/RfvuA9CxyMhWz06dgTPtT2J661zcsaozwILHXO1lmfGlRQ79xCscV38/duxAm2++gT8FeOdkcxkvvqgs7QJOHEGj/AIU/7wMLeMuVvt4secB7PvlK4QcbY/4K64tn3UPDUXEt8uAA2eA3XY1QbB5yXQsiciB/crb8ORnZ4wgGxyMA2++iEVxwPBdQLvlGVjZZA/WNMrGsfBitB3UBWc+nopL3vwftl3SGR0je6Bd//4ojYgwvtP798P/hx9cJvb/PeffWJu1FhknMtT3Yer2qfhu33e4ptM1eHLYk64fWWY6Fu1apDz5aSdU2/ja3+KFMBHuy3zzzTfKFu62227Djz/+iHbt2uHhhx92W6BcEARBEC4IIb2goEBlhemCY1bowZqamoo//vGPZc+xWBmfrw4cADMIU9RmQHbX4dm4cSOGDx+OsWPHqmJmVWHdunVqybgg1AdM7qLzgIcaVRWgeErhndosa/0xG/m664x/790L2G9JwltvVaKSOot9Kl/S5dnAyVZIj7gRr263YVlxEv6zaTJ6Rxdi87TVWP5CJiJH2zByonOf2leUg2hmwOllmd5UWr3kmabd5oJjVt9XZrg5i4excCfzRRPtFJwNH9PTVybjqN2mJhHcesVqdFs8FTDTxcqc18IF537bDOiET259z1kc1fnaVGBwYSri96awgeXXQxcrYyYgBfPCQoTGBCEx1On/qn3lbTbkTEhVBVz3xycg8sZktNoZjQ4/ZmJQxGrE5GRg/0Ux2J0XhuyToeg8NwWIcS4j1541bLuz3bwOTEDUlree6q1aa6rp+m51KagLDZPzPX7LRLhvtdnX2n1Tj5vw9eavMTB2YJXafEfCHXDAgZiwGAz9cKi6HzdkHGZumIkl+5bgH01a4Y1BNyDmljHuf5ydk7fX8d9vzweK1gBnzgDXXINXDv8Hs0OP4E/b/TGwsA/W7VmBNaN6IPLG0Rg5eqJrMOAM7Jo18CsqMibCOQHsLX4fPGjc8/ysE+GM6VtNBbhtNhz6ZiZaLF4OrNkJ/6BoFQNLrhyOo63s6hr4r1xtWKAwtnmarOff9TXXGMcxbeOXkQH/776Dv7vZYu6PYmrnzjhUnIeAoZfDP6KT2ocjZyoGbTuB+D174N/NOSFN2G4GRtZr4Hk2aoSkE0fRvG0AIk/b4Z/UD2jXTgVOR8ocFHUGFo0YibFh16BxbBFWla7A7/0H4pIUO7DsBFruzkSoPQuO6J3wz3eo2W2/wYPhv2wZ/C2z3O3C2mHz4c3qnt8dWycb4Gd48evvkvaMtx+3q4K39ny7ymS3FlO90P8WL8SJcF9i165dmDJliprY/utf/4rVq1fjscceQ9OmTVXMry1rNo84/FDXlDr84HD4qfu6PbBvWRb5oq1UfSDXqWrIdbowrlNpNdpdr0L6E088geuvv14tBz948KBaft2oUSOMUuoWs2THqJlmLqkmf/jDH3D55ZfjtddeU0u6P/30U6SlpeG9994r2+eRI0ewb98+tT+ylZ1yZzYcbwyeXOLN4iTTp09Xj3VAZeYBj8/l4ByEc7DOQK09XPkatyH/+te/1FKyiy++WC3rpsfqokWL8P3339fxVRR8gbrI4L33XsNmlCuzq4Lzz0zZkXLsu2WLsdqbIjrvOT6m9fD8+UaiGO1L2Ud12/5Ro3AsD8gIHqhcR5igVXQSiBxhAwKBbf+xo+uBFGybAUAL6bpIGKGQffSo+2JiZiiQM6v1xx+BbdsqbqMHtKaCXRfbkpAak4TgaPqTGh9CIicA9HvSvCjGvAj898iRwFtvVTxviv88h+hobJqWpjLEI0bZED+mvLBZTHQ0xmdO5gGQlpakdsuxrwM2qLPndszMo4czz4VCuW6/9lbt3NnIPDN9gSJGAVykzeNhTBKGTp6ModtSgB4JQEwydhfZ8NQXSejRJA3j+qaivS5spj3WeRx+BpMnY4zNhjHLkir9vlqbSSHd00clnN9I/BbOd6b9Mg0zM2ZiYMxABDYOrFXB8b3r38M7I9+psvUGC17yljw9GWuy1mBL7haVXTwqYRQ2HdqEnxqfxC0tViJ7yTeIXBuJAe0GYGzvse7b/+CDOPbRO/h5SAyi7rah17YibMiYgauCYlVsPr10NboWHMM2zAC0kK7jNO3UzFQlfrNTwQkw60S4jsGmwBJx4ygV55r3GAgwI90av8fGu84Au2uLqQCp2/hdVAT7Xx9Fahxw8bXOa+QsphoeHY1wp6if1hZKiO7Xrh8cNwJBu0wFQ53FRtWxZs82zjMgAKHt2iGxUSiwLMPwmWc70tLgHwoEOo+HtkkYmZaGkamB5bPU8QOQEbAMc1vmokvb1lAlnDlpz9itLWxM58xJFH7+/I5WKLKqL8euVMzaOAsBjQKQ2Na4gu6KqQpCQxc5WN/kRa4aAdCnTx8V91n3xJOQXhPWbB4pcVNEqZYpdQB5Z1qryVT/utTS66NI0gVmK1UfyHWqGnKdLozrlF8Na7Z6FdLtdrsadOfm5qoB7tChQ7FixYqywS4H1OYPgD6mM2bMUMu0OQvdpUsXfP311+jZs2fZNsxQu+eee8oe3+Fc6slBPn1U16xZowqKEVb5NrN7925cdNFF+Pzzz1Vw5UCdNw0Fgz179qh/FxcX489//rOqHB4cHKw8YhcuXKiKnwiCRjuNaD201gTHtDRMDEzFhKeisWxtPu7r2x/5Xfth3DjPx6OdS3w88PLLxuPu3Y2MdP5ZUD9m8saXX1LcMsR26tY8l0cf5d9TxZ29mzkG4e9ORvKuFLzEIpbjeWDj1mrnNAR8uQNdL492TWumgM6LY85It9nUS+vSo9H70cmGJYk+Ca3+0w9VD8TdQV9YZ5Z5kk7IS2OWuJtt+aL1IllFAg6KdQY8t9XnQJGOF2b1auTsyFQZ4vyYlZCu98v3OUWBVCRh16w0DIhIxbBJ3Nf4coWacMbCPOBnx/+XXwD+rliEAB5DHUdjye6bPzgN12+ajOXBNixMHI/EJGDpfzcBe3dje+ckXDp+jEvbzNfAmxZibiabp2uuCRcWEr+F8513Vr+jRGoK0x1bdKw9wdGUOb1jz1o8XfwD8i/pqsRRb8ejYBoaEIquLbuie5vuZcLp6gOrMXf7XGzL3Ybjp47jQP4B9W9mqz/a/1FMHDaxbJKAwjsnvd6Ny8SuBbMw6vkJmPi7SZj46MSydjXZ0wJYugRdEy4vP7ieCO/a1YjlTu9xHYd2BRZh34PJSgiPTx7jGr8Z890FGB0z2XGiRVR0tHpv2ftRhfhtngXWbWSAMsdvDSfvnSvYSrO2qgzx1O4xxjV3F7+HQF2jAdkRGPa7ScDvnPvatMk4DmfI2bHicdlZYuFQ/l7RL828oi0pCTFJSXCR/KxifHQ0vvt8Eb6KLsaxnnm4NawfDqz9Cd9mLUZIk1CMcQry+rwoLOrvyuSfJ7sVyPn9WLxnMXJO5CCmeQyiQ6ORfjBdWb3wuyRiuuALcOUYa6OY6dGjB7744otatWbzSOP9qGuYic7/2jS2w9/PWbC4LrDWyWrg+KKtVH0g16lqyHW6MK5TYDWs2epVSGdGmjcWL15c4Tl6ovHmid/85jfq5olhw4apGRJvcMDuqXiZZty4ceomCN6gTQqzvTnWvPNOz7rvWWP12ggORuPCKERtKsBX+/phZGQakrykwvMlCvyXX24kdbFvynGg3i0dVHRGOvVijjtnzDCEdKvHOg+x0W4zlklbTnRowGqg+VaA96mZrpYu1oz0pCR8kpqE8LmT0QscDNqVzzktWi4eOwZJKV4G1dbBKP3Ine38e2gqEmnt4jyGu2uZ/nIqptptGDHChpGspcgBMZVia+o1j8PsMF40J8wMp4jefqCR5b2rKBr7VmSqx3HOtvCqDFjsbEeqqR3O7LcKXxB3GXyeZkb0wN85uH4o2I7C4Az06AB0txnvSfs2Ewkxxcj4NhNB9xmfl40J7JbjurN1cddM3TzeCxcWEr+F8x3apWw5vAWRIZEIahyEopIiJVLWWGa6u/gdWIioE5vwVcg+lWHs7TjMMKYoemevO1Wb+Jgw85xC6c6jOzF/x3zVfgrp9GCfkTFDCekU0Zfbl6vtmdnO9w/IXoxE+qqzTTqeJCWhN8XazUeB7IByQZo2JlwxQk8wWh6Z4jdvFNHDFy/HyUN5wPpMbOoVjW/jMmH7YBKSuOBE90s8XQ/nRLgW/Gl5MnK/lxjI+P3xy7BvWokBR4NQVnnBYvXmMhFOX3X2QQYMgP+pzipD/Pp8Z/zmRMDmFWjfY2B5/G6LitcIbuzh2CHijYVCadFmqQ3hFn0tdJ9j9Wrcti9EPXVy1GhgZSYyD+1CflYA3l3+GuIj4lHw/gTVllB9XnpXcTaXew2/S5OGTyrLVud91oksdeO/RUgXfIEhQ4aUrVbTbNu2TU2W17Y1m1vqUsg2H9aP2ejGrc7wQVHMF22l6gO5TlVDrtP5f538q9HmBuWRLgi+jDs7DIqOtEzRk/hMXDpnixfzgazCcVERQjIyEXBRNK7sTTMR19RiaxvdrKQ2j59dYPYxRfTRo1091gnHjMZ7nFnopkwpt0U4nQJ61u5CbD0WirjIIDTRGenOMWVWejSa24NUca5ma+YiJMiuLFq8Xjer5yrbOcFo59R4GxJvdKMOa5h5tzAFLU4Cb4eNx8gUD1lu5nNgphkzam02nEQSVg5LQmf7ZCAjBU13F6NrdhYO5o1E3DLDFkbtkZno1DuctiplbTWJ4GXPecvg8+S/4vxO9OB34qpk9FWvGy+tChmOYoRgXUh/FHOzjCRkJSdhPFyP6+7z14c2P6+bp+8FQRB8EWbk/mXhX7A9dzvu7XOvEpu1XYb2lGZxz8ISQ5A9a9Gxsvi9MwMBLS7ClZ16l4m6+nfZJYvcKX4TLYrqLOTxQ8dXaN9zi59TIvroBCOAcx/me7U9s6y1p7g5Dul4x0KZ9PZih4HLX0/lY1teHiJaxLjEb0IB2rF2F8KLg9U55uwOQspA57UbOt59gHEzET4zY4IS/G9buwvIdtYw8PDeJt8vRFZ4AeZ16oh7zXFei9P6Oa4A43FYn4ECeGAgsu4bi6xdqWg+b7WyYXEUZiI8Lxv7AMS948zu5v/0NTLv39wvMF+3Bx8sX5JYmQeaNQM+IQEXjXoIT+p9haRh6Yo3sD7KD34OP/V574rMAS6JwDDLcflZevp+Wl/jd9ud6C4IDZXHH39crXCjtcvtt9+OVatWKYs4s02cIAiCINQ2IqQLQg3BsdWkScBf/mLoq7RHYdIjs7n571mzDEHdnBh1tgfKn5WC9MVG9nMcB7itWxuvbdqE1qdO4plr0uD/JlPLnaKtc6BHG5eFC8vbY9Zv3SWImWEWutnSxauIavUGMacy8zVehM6dMc+RiNNH7AgvOYRIndGmr01iJpBTCJwsRnAQrRmA3J1p+LBbannRUquY7EYB1u1LGpWkvMQ9YrPhdDrKi5B6Q2egMaPtD39QWRqpzvFvVIINY5OBoAXp8M/PQsdgpy2MeTDPZdnubFWs101ntpmLpukBs3Pb7HQ7NueXe7PPLbLRvQX7Gtvwq7GuEw/JzyRi2bJYJA+OUKsPnKdduZeLB3TzBEEQfA2zlzTvf9j9A0pRin8u/6cS0mnpMnvLbAQ3DUZCRILyxs7Mzzw30ZHx++tZSN+92Mh2NluCTJ6M1o2b45muv4X/bRVjhLaaySvKU0K6O8HUU9t4PrxZPdZdcGNnoh7reGf2Qx87Fp/lfI/T+*****************************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", "text/plain": ["<Figure size 1500x500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== ALIGNMENT SUMMARY ===\n", "Original drone points: 13,848\n", "IFC points: 14,460\n", "Aligned drone points: 13,848\n", "\n", "GCP Quality Metrics:\n", "  Number of GCP pairs: 5\n", "  Mean residual: 4.200m\n", "  Max residual: 7.204m\n", "  RMS residual: 4.524m\n"]}], "source": ["def visualize_gcp_alignment(drone_points, drone_aligned, ifc_points, gcp_pairs=None, sample_size=5000):\n", "    \"\"\"Visualize the GCP alignment results\"\"\"\n", "    import matplotlib.pyplot as plt\n", "    from mpl_toolkits.mplot3d import Axes3D\n", "    \n", "    # Sample points for visualization\n", "    if len(drone_points) > sample_size:\n", "        indices = np.random.choice(len(drone_points), sample_size, replace=False)\n", "        drone_sample = drone_points[indices]\n", "        drone_aligned_sample = drone_aligned[indices]\n", "    else:\n", "        drone_sample = drone_points\n", "        drone_aligned_sample = drone_aligned\n", "    \n", "    if len(ifc_points) > sample_size:\n", "        indices = np.random.choice(len(ifc_points), sample_size, replace=False)\n", "        ifc_sample = ifc_points[indices]\n", "    else:\n", "        ifc_sample = ifc_points\n", "    \n", "    # Create visualization\n", "    fig = plt.figure(figsize=(15, 5))\n", "    \n", "    # Before alignment (XY view)\n", "    ax1 = fig.add_subplot(131)\n", "    ax1.scatter(drone_sample[:, 0], drone_sample[:, 1], c='blue', alpha=0.5, s=1, label='Drone (Original)')\n", "    ax1.scatter(ifc_sample[:, 0], ifc_sample[:, 1], c='red', alpha=0.5, s=1, label='IFC')\n", "    ax1.set_xlabel('X (m)')\n", "    ax1.set_ylabel('Y (m)')\n", "    ax1.set_title('Before GCP Alignment (XY)')\n", "    ax1.legend()\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # After alignment (XY view)\n", "    ax2 = fig.add_subplot(132)\n", "    ax2.scatter(drone_aligned_sample[:, 0], drone_aligned_sample[:, 1], c='green', alpha=0.5, s=1, label='Drone (GCP Aligned)')\n", "    ax2.scatter(ifc_sample[:, 0], ifc_sample[:, 1], c='red', alpha=0.5, s=1, label='IFC')\n", "    ax2.set_xlabel('X (m)')\n", "    ax2.set_ylabel('Y (m)')\n", "    ax2.set_title('After GCP Alignment (XY)')\n", "    ax2.legend()\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # GCP residuals\n", "    ax3 = fig.add_subplot(133)\n", "    if gcp_pairs is not None and len(gcp_pairs) > 0:\n", "        residuals = gcp_pairs\n", "        ax3.bar(range(len(residuals)), residuals, color='orange', alpha=0.7)\n", "        ax3.set_xlabel('GCP Pair Index')\n", "        ax3.set_ylabel('Residual (m)')\n", "        ax3.set_title('GCP Alignment Residuals')\n", "        ax3.grid(True, alpha=0.3)\n", "        \n", "        # Add statistics\n", "        mean_residual = np.mean(residuals)\n", "        max_residual = np.max(residuals)\n", "        ax3.axhline(mean_residual, color='red', linestyle='--', label=f'Mean: {mean_residual:.2f}m')\n", "        ax3.legend()\n", "    else:\n", "        ax3.text(0.5, 0.5, 'No GCP residuals available', ha='center', va='center', transform=ax3.transAxes)\n", "        ax3.set_title('GCP Residuals')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # Print summary statistics\n", "    print(f\"\\n=== ALIGNMENT SUMMARY ===\")\n", "    print(f\"Original drone points: {len(drone_points):,}\")\n", "    print(f\"IFC points: {len(ifc_points):,}\")\n", "    print(f\"Aligned drone points: {len(drone_aligned):,}\")\n", "    \n", "    if gcp_pairs is not None and len(gcp_pairs) > 0:\n", "        print(f\"\\nGCP Quality Metrics:\")\n", "        print(f\"  Number of GCP pairs: {len(gcp_pairs)}\")\n", "        print(f\"  Mean residual: {np.mean(gcp_pairs):.3f}m\")\n", "        print(f\"  Max residual: {np.max(gcp_pairs):.3f}m\")\n", "        print(f\"  RMS residual: {np.sqrt(np.mean(np.array(gcp_pairs)**2)):.3f}m\")\n", "\n", "# Create visualization if alignment was successful\n", "if 'transform_params' in locals() and 'drone_aligned' in locals():\n", "    print(\"\\n=== CREATING VISUALIZATION ===\")\n", "    visualize_gcp_alignment(\n", "        drone_points, \n", "        drone_aligned, \n", "        ifc_points, \n", "        gcp_pairs=transform_params['gcp_residuals']\n", "    )\n", "else:\n", "    print(\"\\nSkipping visualization - transformation failed\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== GCP-BASED ALIGNMENT COMPLETE ===\n", "\n", "Alignment successful with 5 GCP pairs\n", "RMS Error: 4.524m\n", "Scale Factor: 0.999576\n", "\n", "Results saved to: /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/gcp_alignment/csf/\n", "  - Aligned point cloud: trino_enel_gcp_aligned.ply\n", "  - Transformation params: trino_enel_gcp_transform_params.json\n", "  - GCP residuals: trino_enel_gcp_residuals.csv\n"]}], "source": ["print(\"=== GCP-BASED ALIGNMENT COMPLETE ===\")\n", "\n", "if 'transform_params' in locals():\n", "    print(f\"\\nAlignment successful with {len(transform_params['gcp_residuals'])} GCP pairs\")\n", "    print(f\"RMS Error: {transform_params['rms_error']:.3f}m\")\n", "    print(f\"Scale Factor: {transform_params['scale']:.6f}\")\n", "    \n", "    if save_results:\n", "        print(f\"\\nResults saved to: {output_dir}/{ground_method}/\")\n", "        print(f\"  - Aligned point cloud: {site_name}_gcp_aligned.ply\")\n", "        print(f\"  - Transformation params: {site_name}_gcp_transform_params.json\")\n", "        print(f\"  - GCP residuals: {site_name}_gcp_residuals.csv\")\n", "    \n", "else:\n", "    print(f\"\\nAlignment failed - insufficient GCP pairs or other error\")\n", "    print(f\"Check GCP coordinates and search parameters\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**Next steps**:\n", "  1. Use aligned point cloud for pile detection\n", "  2. Compare with other alignment methods\n", "  3. Validate against ground truth data"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}