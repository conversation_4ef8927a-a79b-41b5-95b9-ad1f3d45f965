# Parameters (Papermill)
ground_method = "ransac_pmf"  # Ground segmentation method
site_name = "trino_enel"
gcp_search_radius = 5.0  # Radius for GCP matching
pcrnet_epochs = 100
learning_rate = 0.001
batch_size = 1

output_dir = "../../../data/output_runs/gcp_pcrnet_alignment"
enable_visualization = True
save_results = True

# Imports
import numpy as np
import open3d as o3d
import torch
import torch.nn as nn
import torch.optim as optim
import matplotlib.pyplot as plt
import pandas as pd
from pathlib import Path
from datetime import datetime
from scipy.spatial import cKDTree
from sklearn.neighbors import NearestNeighbors
import json

# Setup
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
output_path = Path(output_dir) / ground_method
output_path.mkdir(parents=True, exist_ok=True)

print("GCP-Anchored PCRNet Point Cloud Registration")
print(f"Site: {site_name}")
print(f"Device: {device}")
print(f"Output: {output_path}")

# Define file paths
drone_file = f"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply"
ifc_pointcloud_file = f"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply"
def load_point_clouds():
    """Load drone and IFC point clouds"""
    drone_pcd = o3d.io.read_point_cloud(drone_file)
    ifc_pcd = o3d.io.read_point_cloud(ifc_pointcloud_file)
    
    drone_points = np.asarray(drone_pcd.points)
    ifc_points = np.asarray(ifc_pcd.points)
    
    print(f"Loaded drone points: {len(drone_points):,}")
    print(f"Loaded IFC points: {len(ifc_points):,}")
    
    return drone_points, ifc_points

def load_ground_control_points():
    """Load the ground control points from project data"""
    gcp_data = {
        'Point_ID': ['1', '2', '3', '4', '5', '6'],
        'Easting': [435267.277, 435280.92, 435267.22, 435280.438, 436224.793, 436226.921],
        'Northing': [5011787.189, 5011792.074, 5011921.754, 5011925.654, 5012459.896, 5012459.942],
        'Location': ['NE', 'NE', 'NE', 'NE', 'NW', 'NW']
    }
    
    # Additional points from project data
    additional_points = {
        'Point_ID': ['4_sw', '4_sw2', '5_SM', '5_SM2', '6_SE', '6_SE2'],
        'Easting': [436719.919, 436718.628, 436302.667, 436305.828, 436112.667, 436118.344],
        'Northing': [5011825.557, 5011831.735, 5011417.757, 5011410.901, 5010904.99, 5010917.949],
        'Location': ['SW', 'SW', 'SM', 'SM', 'SE', 'SE']
    }
    
    # Combine all points
    all_data = {}
    for key in gcp_data:
        all_data[key] = gcp_data[key] + additional_points[key]
    
    df = pd.DataFrame(all_data)
    
    # Convert to 3D coordinates (assuming Z=0 for ground level)
    ifc_gcp_coords = np.column_stack([
        df['Easting'].values,
        df['Northing'].values,
        np.zeros(len(df))  # Assume ground level Z=0
    ])
    
    return df, ifc_gcp_coords

drone_points, ifc_points = load_point_clouds()
gcp_df, ifc_gcp_coords = load_ground_control_points()
print(f"Loaded {len(ifc_gcp_coords)} ground control points")
print(gcp_df)

# CRITICAL: Check coordinate scales and ranges
print(f"\nCoordinate Scale Analysis:")
print(f"Drone points range: X[{np.min(drone_points[:,0]):.1f}, {np.max(drone_points[:,0]):.1f}] Y[{np.min(drone_points[:,1]):.1f}, {np.max(drone_points[:,1]):.1f}] Z[{np.min(drone_points[:,2]):.1f}, {np.max(drone_points[:,2]):.1f}]")
print(f"IFC points range: X[{np.min(ifc_points[:,0]):.1f}, {np.max(ifc_points[:,0]):.1f}] Y[{np.min(ifc_points[:,1]):.1f}, {np.max(ifc_points[:,1]):.1f}] Z[{np.min(ifc_points[:,2]):.1f}, {np.max(ifc_points[:,2]):.1f}]")
print(f"GCP range: X[{np.min(ifc_gcp_coords[:,0]):.1f}, {np.max(ifc_gcp_coords[:,0]):.1f}] Y[{np.min(ifc_gcp_coords[:,1]):.1f}, {np.max(ifc_gcp_coords[:,1]):.1f}]")

# Check if coordinate systems are compatible
drone_center = np.mean(drone_points, axis=0)
ifc_center = np.mean(ifc_points, axis=0)
gcp_center = np.mean(ifc_gcp_coords, axis=0)
print(f"\nCentroid Analysis:")
print(f"Drone centroid: [{drone_center[0]:.1f}, {drone_center[1]:.1f}, {drone_center[2]:.1f}]")
print(f"IFC centroid: [{ifc_center[0]:.1f}, {ifc_center[1]:.1f}, {ifc_center[2]:.1f}]")
print(f"GCP centroid: [{gcp_center[0]:.1f}, {gcp_center[1]:.1f}, {gcp_center[2]:.1f}]")

# Check for major coordinate system mismatch
drone_to_gcp_dist = np.linalg.norm(drone_center[:2] - gcp_center[:2])
ifc_to_gcp_dist = np.linalg.norm(ifc_center[:2] - gcp_center[:2])
print(f"\nCoordinate System Compatibility:")
print(f"Drone to GCP distance: {drone_to_gcp_dist:.1f}m")
print(f"IFC to GCP distance: {ifc_to_gcp_dist:.1f}m")

if drone_to_gcp_dist > 100000 or ifc_to_gcp_dist > 100000:
    print("WARNING: Major coordinate system mismatch detected!")
    print("Point clouds appear to be in local coordinates while GCPs are in UTM.")
    print("This will cause PCRNet training instability.")

def normalize_coordinates_for_training(drone_pts, ifc_pts, gcp_coords):
    """Normalize coordinates to prevent numerical instability in neural network training"""
    # Combine all coordinates to find global scale
    all_coords = np.vstack([drone_pts, ifc_pts, gcp_coords])
    
    # Find global centroid and scale
    global_center = np.mean(all_coords, axis=0)
    global_scale = np.std(all_coords)
    
    print(f"Normalization parameters:")
    print(f"  Global center: [{global_center[0]:.1f}, {global_center[1]:.1f}, {global_center[2]:.1f}]")
    print(f"  Global scale: {global_scale:.1f}")
    
    # Normalize all coordinate sets
    drone_norm = (drone_pts - global_center) / global_scale
    ifc_norm = (ifc_pts - global_center) / global_scale
    gcp_norm = (gcp_coords - global_center) / global_scale
    
    # Verify normalization
    print(f"After normalization:")
    print(f"  Drone range: [{np.min(drone_norm):.3f}, {np.max(drone_norm):.3f}]")
    print(f"  IFC range: [{np.min(ifc_norm):.3f}, {np.max(ifc_norm):.3f}]")
    print(f"  GCP range: [{np.min(gcp_norm):.3f}, {np.max(gcp_norm):.3f}]")
    
    return drone_norm, ifc_norm, gcp_norm, global_center, global_scale

def denormalize_transformation(transform_norm, global_center, global_scale):
    """Convert normalized transformation back to original coordinate system"""
    # Extract rotation and translation from normalized transform
    R_norm = transform_norm[:3, :3]
    t_norm = transform_norm[:3, 3]
    
    # Scale translation back to original coordinates
    t_original = t_norm * global_scale
    
    # Rotation matrix stays the same (scale-invariant)
    transform_original = np.eye(4)
    transform_original[:3, :3] = R_norm
    transform_original[:3, 3] = t_original
    
    return transform_original

def extract_gcp_correspondences(drone_pts, ifc_pts, ifc_gcp_coords, search_radius=5.0):
    """Extract corresponding points near GCP locations"""
    drone_correspondences = []
    ifc_correspondences = []
    
    # Build KD-trees for efficient nearest neighbor search
    drone_tree = cKDTree(drone_pts)
    ifc_tree = cKDTree(ifc_pts)
    
    print(f"Searching for correspondences within {search_radius}m of {len(ifc_gcp_coords)} GCPs...")
    
    for i, gcp in enumerate(ifc_gcp_coords):
        # Find drone points near this GCP
        drone_indices = drone_tree.query_ball_point(gcp, search_radius)
        
        # Find IFC points near this GCP
        ifc_indices = ifc_tree.query_ball_point(gcp, search_radius)
        
        if len(drone_indices) > 0 and len(ifc_indices) > 0:
            # Use centroid of nearby points as correspondence
            drone_centroid = np.mean(drone_pts[drone_indices], axis=0)
            ifc_centroid = np.mean(ifc_pts[ifc_indices], axis=0)
            
            drone_correspondences.append(drone_centroid)
            ifc_correspondences.append(ifc_centroid)
            
            gcp_id = gcp_df.iloc[i]['Point_ID']
            location = gcp_df.iloc[i]['Location']
            print(f"  GCP {gcp_id} ({location}): Found {len(drone_indices)} drone + {len(ifc_indices)} IFC points")
        else:
            gcp_id = gcp_df.iloc[i]['Point_ID']
            print(f"  GCP {gcp_id}: No points found within {search_radius}m radius")
    
    if len(drone_correspondences) == 0:
        print("No GCP correspondences found, using centroid fallback")
        drone_center = np.mean(drone_pts, axis=0).reshape(1, 3)
        ifc_center = np.mean(ifc_pts, axis=0).reshape(1, 3)
        return drone_center, ifc_center
    
    drone_correspondences = np.array(drone_correspondences)
    ifc_correspondences = np.array(ifc_correspondences)
    
    print(f"Successfully extracted {len(drone_correspondences)} GCP correspondences")
    return drone_correspondences, ifc_correspondences

drone_gcps, ifc_gcps = extract_gcp_correspondences(drone_points, ifc_points, ifc_gcp_coords, gcp_search_radius)

class SimplePCRNet(nn.Module):
    """Simplified PCRNet for point cloud registration"""
    
    def __init__(self, feature_dim=1024):
        super(SimplePCRNet, self).__init__()
        
        # Point feature extraction (simplified PointNet)
        self.conv1 = nn.Conv1d(3, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, feature_dim, 1)
        
        # Transformation prediction
        self.fc1 = nn.Linear(feature_dim * 2, 512)
        self.fc2 = nn.Linear(512, 256)
        self.fc3 = nn.Linear(256, 12)  # 3x4 transformation matrix (rotation + translation)
        
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.3)
        
    def extract_features(self, x):
        """Extract global features from point cloud"""
        # x: (batch_size, 3, num_points)
        x = self.relu(self.conv1(x))
        x = self.relu(self.conv2(x))
        x = self.conv3(x)
        
        # Global max pooling
        x = torch.max(x, 2)[0]  # (batch_size, feature_dim)
        return x
    
    def forward(self, source, target):
        """Predict transformation from source to target"""
        # Extract features
        source_feat = self.extract_features(source)
        target_feat = self.extract_features(target)
        
        # Concatenate features
        combined = torch.cat([source_feat, target_feat], dim=1)
        
        # Predict transformation
        x = self.relu(self.fc1(combined))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        
        # Reshape to 3x4 transformation matrix
        transform = x.view(-1, 3, 4)
        
        return transform

# Initialize model
model = SimplePCRNet().to(device)
optimizer = optim.Adam(model.parameters(), lr=learning_rate)

print(f"Model initialized with {sum(p.numel() for p in model.parameters())} parameters")

def prepare_training_data(drone_pts, ifc_pts, num_points=1024):
    """Prepare point clouds for training"""
    # Downsample for training efficiency
    if len(drone_pts) > num_points:
        drone_indices = np.random.choice(len(drone_pts), num_points, replace=False)
        drone_sample = drone_pts[drone_indices]
    else:
        drone_sample = drone_pts
    
    if len(ifc_pts) > num_points:
        ifc_indices = np.random.choice(len(ifc_pts), num_points, replace=False)
        ifc_sample = ifc_pts[ifc_indices]
    else:
        ifc_sample = ifc_pts
    
    # Convert to tensors and transpose for conv1d (batch_size, channels, num_points)
    drone_tensor = torch.FloatTensor(drone_sample).unsqueeze(0).transpose(1, 2).to(device)
    ifc_tensor = torch.FloatTensor(ifc_sample).unsqueeze(0).transpose(1, 2).to(device)
    
    return drone_tensor, ifc_tensor

def gcp_loss(predicted_transform, drone_gcps, ifc_gcps):
    """Compute loss based on GCP correspondences"""
    # Convert GCPs to tensors
    drone_gcp_tensor = torch.FloatTensor(drone_gcps).to(device)
    ifc_gcp_tensor = torch.FloatTensor(ifc_gcps).to(device)
    
    # Apply predicted transformation to drone GCPs
    # Add homogeneous coordinate
    drone_homo = torch.cat([drone_gcp_tensor, torch.ones(len(drone_gcps), 1).to(device)], dim=1)
    
    # Apply transformation
    transformed_drone = torch.matmul(predicted_transform.squeeze(0), drone_homo.T).T
    
    # Compute MSE loss with target GCPs
    loss = nn.MSELoss()(transformed_drone, ifc_gcp_tensor)
    
    return loss

def train_pcrnet(model, drone_pts, ifc_pts, drone_gcps, ifc_gcps, epochs=100):
    """Train PCRNet with GCP anchoring"""
    model.train()
    losses = []
    
    print(f"Training PCRNet for {epochs} epochs...")
    
    for epoch in range(epochs):
        # Prepare training data with random sampling
        drone_tensor, ifc_tensor = prepare_training_data(drone_pts, ifc_pts)
        
        # Forward pass
        optimizer.zero_grad()
        predicted_transform = model(drone_tensor, ifc_tensor)
        
        # Compute GCP-anchored loss
        loss = gcp_loss(predicted_transform, drone_gcps, ifc_gcps)
        
        # Backward pass
        loss.backward()
        optimizer.step()
        
        losses.append(loss.item())
        
        if (epoch + 1) % 20 == 0:
            print(f"  Epoch {epoch+1}/{epochs}, Loss: {loss.item():.6f}")
    
    print(f"Training completed. Final loss: {losses[-1]:.6f}")
    return losses

# Train the model
training_losses = train_pcrnet(model, drone_points, ifc_points, drone_gcps, ifc_gcps, pcrnet_epochs)

def apply_pcrnet_transformation(model, drone_pts, ifc_pts):
    """Apply learned transformation to full point cloud"""
    model.eval()
    
    with torch.no_grad():
        # Prepare data
        drone_tensor, ifc_tensor = prepare_training_data(drone_pts, ifc_pts, num_points=2048)
        
        # Predict transformation
        predicted_transform = model(drone_tensor, ifc_tensor)
        transform_matrix = predicted_transform.squeeze(0).cpu().numpy()
        
        print(f"Learned transformation matrix:")
        print(transform_matrix)
        
        # Apply to full drone point cloud
        drone_homo = np.column_stack([drone_pts, np.ones(len(drone_pts))])
        transformed_drone = (transform_matrix @ drone_homo.T).T
        
        return transformed_drone, transform_matrix

def evaluate_alignment(transformed_drone, ifc_pts, method_name="PCRNet"):
    """Evaluate alignment quality"""
    # Sample points for evaluation
    sample_size = min(5000, len(transformed_drone), len(ifc_pts))
    
    drone_sample = transformed_drone[np.random.choice(len(transformed_drone), sample_size, replace=False)]
    ifc_sample = ifc_pts[np.random.choice(len(ifc_pts), sample_size, replace=False)]
    
    # Compute nearest neighbor distances
    tree = cKDTree(ifc_sample)
    distances, _ = tree.query(drone_sample)
    
    # Metrics
    rmse = np.sqrt(np.mean(distances**2))
    median_dist = np.median(distances)
    good_points = np.sum(distances < 2.0) / len(distances) * 100
    
    # Centroid alignment
    drone_center = np.mean(transformed_drone, axis=0)
    ifc_center = np.mean(ifc_pts, axis=0)
    centroid_error = np.linalg.norm(drone_center - ifc_center)
    
    results = {
        'method': method_name,
        'rmse': rmse,
        'median_distance': median_dist,
        'good_points_pct': good_points,
        'centroid_error': centroid_error,
        'sample_size': sample_size
    }
    
    print(f"{method_name} Alignment Results:")
    print(f"  RMSE: {rmse:.2f}m")
    print(f"  Median distance: {median_dist:.2f}m")
    print(f"  Good points (<2m): {good_points:.1f}%")
    print(f"  Centroid error: {centroid_error:.3f}m")
    
    return results

# Apply learned transformation
transformed_drone, learned_transform = apply_pcrnet_transformation(model, drone_points, ifc_points)
pcrnet_results = evaluate_alignment(transformed_drone, ifc_points, "GCP-PCRNet")

def coordinate_baseline_alignment(drone_pts, ifc_pts):
    """Simple coordinate-based alignment for comparison"""
    drone_center = np.mean(drone_pts, axis=0)
    ifc_center = np.mean(ifc_pts, axis=0)
    offset = ifc_center - drone_center
    
    aligned_drone = drone_pts + offset
    return aligned_drone, offset

def gcp_baseline_alignment(drone_pts, ifc_pts, drone_gcps, ifc_gcps):
    """GCP-based rigid transformation using Procrustes analysis"""
    if len(drone_gcps) < 3:
        print("Insufficient GCPs for rigid transformation, using coordinate baseline")
        return coordinate_baseline_alignment(drone_pts, ifc_pts)
    
    # Center the GCP sets
    drone_gcp_centered = drone_gcps - np.mean(drone_gcps, axis=0)
    ifc_gcp_centered = ifc_gcps - np.mean(ifc_gcps, axis=0)
    
    # Compute optimal rotation using SVD
    H = drone_gcp_centered.T @ ifc_gcp_centered
    U, S, Vt = np.linalg.svd(H)
    R = Vt.T @ U.T
    
    # Ensure proper rotation (det(R) = 1)
    if np.linalg.det(R) < 0:
        Vt[-1, :] *= -1
        R = Vt.T @ U.T
    
    # Compute translation
    t = np.mean(ifc_gcps, axis=0) - R @ np.mean(drone_gcps, axis=0)
    
    # Apply transformation
    aligned_drone = (R @ drone_pts.T).T + t
    
    return aligned_drone, (R, t)

# Compare with baselines
print("\nBaseline Comparisons:")

# Coordinate-only baseline
coord_aligned, coord_offset = coordinate_baseline_alignment(drone_points, ifc_points)
coord_results = evaluate_alignment(coord_aligned, ifc_points, "Coordinate-Only")

# GCP-based rigid transformation baseline
gcp_aligned, gcp_transform = gcp_baseline_alignment(drone_points, ifc_points, drone_gcps, ifc_gcps)
gcp_results = evaluate_alignment(gcp_aligned, ifc_points, "GCP-Rigid")

# Plot training loss
if enable_visualization:
    plt.figure(figsize=(10, 6))
    
    plt.subplot(1, 2, 1)
    plt.plot(training_losses)
    plt.title('PCRNet Training Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.grid(True)
    
    # Compare alignment methods
    plt.subplot(1, 2, 2)
    methods = ['Coordinate-Only', 'GCP-Rigid', 'GCP-PCRNet']
    rmse_values = [coord_results['rmse'], gcp_results['rmse'], pcrnet_results['rmse']]
    
    plt.bar(methods, rmse_values, color=['blue', 'orange', 'green'])
    plt.title('Alignment Method Comparison')
    plt.ylabel('RMSE (m)')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(output_path / f"{site_name}_gcp_pcrnet_analysis.png", dpi=300, bbox_inches='tight')
    plt.show()

# Summary comparison
print("\nMethod Comparison Summary:")
comparison_df = pd.DataFrame([
    coord_results,
    gcp_results,
    pcrnet_results
])
print(comparison_df[['method', 'rmse', 'median_distance', 'good_points_pct', 'centroid_error']].round(3))

if save_results:
    # Save aligned point cloud
    aligned_pcd = o3d.geometry.PointCloud()
    aligned_pcd.points = o3d.utility.Vector3dVector(transformed_drone)
    o3d.io.write_point_cloud(str(output_path / f"{site_name}_gcp_pcrnet_aligned.ply"), aligned_pcd)
    
    # Save comprehensive results
    results_data = {
        'experiment_info': {
            'site_name': site_name,
            'ground_method': ground_method,
            'timestamp': datetime.now().isoformat(),
            'gcp_search_radius': gcp_search_radius,
            'pcrnet_epochs': pcrnet_epochs,
            'learning_rate': learning_rate
        },
        'gcp_info': {
            'num_gcps_loaded': len(ifc_gcp_coords),
            'num_correspondences': len(drone_gcps),
            'gcp_locations': gcp_df['Location'].tolist(),
            'gcp_point_ids': gcp_df['Point_ID'].tolist(),
            'drone_gcp_coords': drone_gcps.tolist(),
            'ifc_gcp_coords': ifc_gcps.tolist()
        },
        'learned_transformation': learned_transform.tolist(),
        'training_losses': training_losses,
        'final_loss': training_losses[-1],
        'alignment_results': {
            'coordinate_only': coord_results,
            'gcp_rigid': gcp_results,
            'gcp_pcrnet': pcrnet_results
        },
        'best_method': min([coord_results, gcp_results, pcrnet_results], key=lambda x: x['rmse'])['method']
    }
    
    with open(output_path / f"{site_name}_gcp_pcrnet_results.json", 'w') as f:
        json.dump(results_data, f, indent=2)
    
    print(f"\nResults saved to: {output_path}")
    print(f"Best performing method: {results_data['best_method']}")

print("\nGCP-Anchored PCRNet Alignment Complete")
print(f"Final PCRNet RMSE: {pcrnet_results['rmse']:.2f}m")
print(f"Training converged with loss: {training_losses[-1]:.6f}")