{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# GCP-Anchored PCRNet Point Cloud Registration\n", "\n", "This notebook explores using Ground Control Points (GCPs) as anchors for deep learning-based point cloud registration using PCRNet architecture.\n", "\n", "## Approach\n", "1. Extract GCP correspondences from drone and IFC data\n", "2. Use GCPs to initialize and constrain PCRNet training\n", "3. Apply learned transformation for full point cloud alignment\n", "4. Validate results against coordinate-only baseline"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Parameters (Papermill)\n", "ground_method = \"ransac_pmf\"  # Ground segmentation method\n", "site_name = \"trino_enel\"\n", "gcp_search_radius = 5.0  # Radius for GCP matching\n", "pcrnet_epochs = 100\n", "learning_rate = 0.001\n", "batch_size = 1\n", "\n", "output_dir = \"../../../data/output_runs/gcp_pcrnet_alignment\"\n", "enable_visualization = True\n", "save_results = True"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GCP-Anchored PCRNet Point Cloud Registration\n", "Site: trino_enel\n", "Device: cpu\n", "Output: ../../../data/output_runs/gcp_pcrnet_alignment/ransac_pmf\n"]}], "source": ["# Imports\n", "import numpy as np\n", "import open3d as o3d\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy.spatial import cKDTree\n", "from sklearn.neighbors import NearestNeighbors\n", "import json\n", "\n", "# Setup\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "output_path = Path(output_dir) / ground_method\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"GCP-Anchored PCRNet Point Cloud Registration\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Device: {device}\")\n", "print(f\"Output: {output_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Preprocessing"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded drone points: 517,002\n", "Loaded IFC points: 5,480,340\n", "Loaded 12 ground control points\n", "   Point_ID     Easting     Northing Location\n", "0         1  435267.277  5011787.189       NE\n", "1         2  435280.920  5011792.074       NE\n", "2         3  435267.220  5011921.754       NE\n", "3         4  435280.438  5011925.654       NE\n", "4         5  436224.793  5012459.896       NW\n", "5         6  436226.921  5012459.942       NW\n", "6      4_sw  436719.919  5011825.557       SW\n", "7     4_sw2  436718.628  5011831.735       SW\n", "8      5_SM  436302.667  5011417.757       SM\n", "9     5_SM2  436305.828  5011410.901       SM\n", "10     6_SE  436112.667  5010904.990       SE\n", "11    6_SE2  436118.344  5010917.949       SE\n"]}], "source": ["# Define file paths\n", "drone_file = f\"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply\"\n", "ifc_pointcloud_file = f\"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\"\n", "def load_point_clouds():\n", "    \"\"\"Load drone and IFC point clouds\"\"\"\n", "    drone_pcd = o3d.io.read_point_cloud(drone_file)\n", "    ifc_pcd = o3d.io.read_point_cloud(ifc_pointcloud_file)\n", "    \n", "    drone_points = np.asarray(drone_pcd.points)\n", "    ifc_points = np.asarray(ifc_pcd.points)\n", "    \n", "    print(f\"Loaded drone points: {len(drone_points):,}\")\n", "    print(f\"Loaded IFC points: {len(ifc_points):,}\")\n", "    \n", "    return drone_points, ifc_points\n", "\n", "def load_ground_control_points():\n", "    \"\"\"Load the ground control points from project data\"\"\"\n", "    gcp_data = {\n", "        'Point_ID': ['1', '2', '3', '4', '5', '6'],\n", "        'Easting': [435267.277, 435280.92, 435267.22, 435280.438, 436224.793, 436226.921],\n", "        'Northing': [5011787.189, 5011792.074, 5011921.754, 5011925.654, 5012459.896, 5012459.942],\n", "        'Location': ['NE', 'NE', 'NE', 'NE', 'NW', 'NW']\n", "    }\n", "    \n", "    # Additional points from project data\n", "    additional_points = {\n", "        'Point_ID': ['4_sw', '4_sw2', '5_SM', '5_SM2', '6_SE', '6_SE2'],\n", "        'Easting': [436719.919, 436718.628, 436302.667, 436305.828, 436112.667, 436118.344],\n", "        'Northing': [5011825.557, 5011831.735, 5011417.757, 5011410.901, 5010904.99, 5010917.949],\n", "        'Location': ['SW', 'SW', 'SM', 'SM', 'SE', 'SE']\n", "    }\n", "    \n", "    # <PERSON><PERSON><PERSON> all points\n", "    all_data = {}\n", "    for key in gcp_data:\n", "        all_data[key] = gcp_data[key] + additional_points[key]\n", "    \n", "    df = pd.DataFrame(all_data)\n", "    \n", "    # Convert to 3D coordinates (assuming Z=0 for ground level)\n", "    ifc_gcp_coords = np.column_stack([\n", "        df['Easting'].values,\n", "        df['Northing'].values,\n", "        np.zeros(len(df))  # Assume ground level Z=0\n", "    ])\n", "    \n", "    return df, ifc_gcp_coords\n", "\n", "drone_points, ifc_points = load_point_clouds()\n", "gcp_df, ifc_gcp_coords = load_ground_control_points()\n", "print(f\"Loaded {len(ifc_gcp_coords)} ground control points\")\n", "print(gcp_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. GCP Correspondence Extraction"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Searching for correspondences within 5.0m of 12 GCPs...\n", "  GCP 1: No points found within 5.0m radius\n", "  GCP 2: No points found within 5.0m radius\n", "  GCP 3: No points found within 5.0m radius\n", "  GCP 4: No points found within 5.0m radius\n", "  GCP 5: No points found within 5.0m radius\n", "  GCP 6: No points found within 5.0m radius\n", "  GCP 4_sw: No points found within 5.0m radius\n", "  GCP 4_sw2: No points found within 5.0m radius\n", "  GCP 5_SM: No points found within 5.0m radius\n", "  GCP 5_SM2: No points found within 5.0m radius\n", "  GCP 6_SE: No points found within 5.0m radius\n", "  GCP 6_SE2: No points found within 5.0m radius\n", "No GCP correspondences found, using centroid fallback\n"]}], "source": ["def extract_gcp_correspondences(drone_pts, ifc_pts, ifc_gcp_coords, search_radius=5.0):\n", "    \"\"\"Extract corresponding points near GCP locations\"\"\"\n", "    drone_correspondences = []\n", "    ifc_correspondences = []\n", "    \n", "    # Build KD-trees for efficient nearest neighbor search\n", "    drone_tree = cKDTree(drone_pts)\n", "    ifc_tree = cKDTree(ifc_pts)\n", "    \n", "    print(f\"Searching for correspondences within {search_radius}m of {len(ifc_gcp_coords)} GCPs...\")\n", "    \n", "    for i, gcp in enumerate(ifc_gcp_coords):\n", "        # Find drone points near this GCP\n", "        drone_indices = drone_tree.query_ball_point(gcp, search_radius)\n", "        \n", "        # Find IFC points near this GCP\n", "        ifc_indices = ifc_tree.query_ball_point(gcp, search_radius)\n", "        \n", "        if len(drone_indices) > 0 and len(ifc_indices) > 0:\n", "            # Use centroid of nearby points as correspondence\n", "            drone_centroid = np.mean(drone_pts[drone_indices], axis=0)\n", "            ifc_centroid = np.mean(ifc_pts[ifc_indices], axis=0)\n", "            \n", "            drone_correspondences.append(drone_centroid)\n", "            ifc_correspondences.append(ifc_centroid)\n", "            \n", "            gcp_id = gcp_df.iloc[i]['Point_ID']\n", "            location = gcp_df.iloc[i]['Location']\n", "            print(f\"  GCP {gcp_id} ({location}): Found {len(drone_indices)} drone + {len(ifc_indices)} IFC points\")\n", "        else:\n", "            gcp_id = gcp_df.iloc[i]['Point_ID']\n", "            print(f\"  GCP {gcp_id}: No points found within {search_radius}m radius\")\n", "    \n", "    if len(drone_correspondences) == 0:\n", "        print(\"No GCP correspondences found, using centroid fallback\")\n", "        drone_center = np.mean(drone_pts, axis=0).reshape(1, 3)\n", "        ifc_center = np.mean(ifc_pts, axis=0).reshape(1, 3)\n", "        return drone_center, ifc_center\n", "    \n", "    drone_correspondences = np.array(drone_correspondences)\n", "    ifc_correspondences = np.array(ifc_correspondences)\n", "    \n", "    print(f\"Successfully extracted {len(drone_correspondences)} GCP correspondences\")\n", "    return drone_correspondences, ifc_correspondences\n", "\n", "drone_gcps, ifc_gcps = extract_gcp_correspondences(drone_points, ifc_points, ifc_gcp_coords, gcp_search_radius)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Simplified PCRNet Architecture"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model initialized with 1324172 parameters\n"]}], "source": ["class SimplePCRNet(nn.Module):\n", "    \"\"\"Simplified PCRNet for point cloud registration\"\"\"\n", "    \n", "    def __init__(self, feature_dim=1024):\n", "        super(SimplePCRNet, self).__init__()\n", "        \n", "        # Point feature extraction (simplified PointNet)\n", "        self.conv1 = nn.Conv1d(3, 64, 1)\n", "        self.conv2 = nn.Conv1d(64, 128, 1)\n", "        self.conv3 = nn.Conv1d(128, feature_dim, 1)\n", "        \n", "        # Transformation prediction\n", "        self.fc1 = nn.Linear(feature_dim * 2, 512)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.fc3 = nn.Linear(256, 12)  # 3x4 transformation matrix (rotation + translation)\n", "        \n", "        self.relu = nn.ReLU()\n", "        self.dropout = nn.Dropout(0.3)\n", "        \n", "    def extract_features(self, x):\n", "        \"\"\"Extract global features from point cloud\"\"\"\n", "        # x: (batch_size, 3, num_points)\n", "        x = self.relu(self.conv1(x))\n", "        x = self.relu(self.conv2(x))\n", "        x = self.conv3(x)\n", "        \n", "        # Global max pooling\n", "        x = torch.max(x, 2)[0]  # (batch_size, feature_dim)\n", "        return x\n", "    \n", "    def forward(self, source, target):\n", "        \"\"\"Predict transformation from source to target\"\"\"\n", "        # Extract features\n", "        source_feat = self.extract_features(source)\n", "        target_feat = self.extract_features(target)\n", "        \n", "        # Concatenate features\n", "        combined = torch.cat([source_feat, target_feat], dim=1)\n", "        \n", "        # Predict transformation\n", "        x = self.relu(self.fc1(combined))\n", "        x = self.dropout(x)\n", "        x = self.relu(self.fc2(x))\n", "        x = self.dropout(x)\n", "        x = self.fc3(x)\n", "        \n", "        # Reshape to 3x4 transformation matrix\n", "        transform = x.view(-1, 3, 4)\n", "        \n", "        return transform\n", "\n", "# Initialize model\n", "model = SimplePCRNet().to(device)\n", "optimizer = optim.Adam(model.parameters(), lr=learning_rate)\n", "\n", "print(f\"Model initialized with {sum(p.numel() for p in model.parameters())} parameters\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON><PERSON>-Anchored Training"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training PCRNet for 100 epochs...\n", "  Epoch 20/100, Loss: 20838270317633782939648.000000\n", "  Epoch 40/100, Loss: 14629605619541003468800.000000\n", "  Epoch 60/100, Loss: 15782136436880176054272.000000\n", "  Epoch 80/100, Loss: 39201240682874802274304.000000\n", "  Epoch 100/100, Loss: 26000388534318206550016.000000\n", "Training completed. Final loss: 26000388534318206550016.000000\n"]}], "source": ["def prepare_training_data(drone_pts, ifc_pts, num_points=1024):\n", "    \"\"\"Prepare point clouds for training\"\"\"\n", "    # Downsample for training efficiency\n", "    if len(drone_pts) > num_points:\n", "        drone_indices = np.random.choice(len(drone_pts), num_points, replace=False)\n", "        drone_sample = drone_pts[drone_indices]\n", "    else:\n", "        drone_sample = drone_pts\n", "    \n", "    if len(ifc_pts) > num_points:\n", "        ifc_indices = np.random.choice(len(ifc_pts), num_points, replace=False)\n", "        ifc_sample = ifc_pts[ifc_indices]\n", "    else:\n", "        ifc_sample = ifc_pts\n", "    \n", "    # Convert to tensors and transpose for conv1d (batch_size, channels, num_points)\n", "    drone_tensor = torch.FloatTensor(drone_sample).unsqueeze(0).transpose(1, 2).to(device)\n", "    ifc_tensor = torch.FloatTensor(ifc_sample).unsqueeze(0).transpose(1, 2).to(device)\n", "    \n", "    return drone_tensor, ifc_tensor\n", "\n", "def gcp_loss(predicted_transform, drone_gcps, ifc_gcps):\n", "    \"\"\"Compute loss based on GCP correspondences\"\"\"\n", "    # Convert GCPs to tensors\n", "    drone_gcp_tensor = torch.FloatTensor(drone_gcps).to(device)\n", "    ifc_gcp_tensor = torch.FloatTensor(ifc_gcps).to(device)\n", "    \n", "    # Apply predicted transformation to drone GCPs\n", "    # Add homogeneous coordinate\n", "    drone_homo = torch.cat([drone_gcp_tensor, torch.ones(len(drone_gcps), 1).to(device)], dim=1)\n", "    \n", "    # Apply transformation\n", "    transformed_drone = torch.matmul(predicted_transform.squeeze(0), drone_homo.T).T\n", "    \n", "    # Compute MSE loss with target GCPs\n", "    loss = nn.MSELoss()(transformed_drone, ifc_gcp_tensor)\n", "    \n", "    return loss\n", "\n", "def train_pcrnet(model, drone_pts, ifc_pts, drone_gcps, ifc_gcps, epochs=100):\n", "    \"\"\"Train PCRNet with GCP anchoring\"\"\"\n", "    model.train()\n", "    losses = []\n", "    \n", "    print(f\"Training PCRNet for {epochs} epochs...\")\n", "    \n", "    for epoch in range(epochs):\n", "        # Prepare training data with random sampling\n", "        drone_tensor, ifc_tensor = prepare_training_data(drone_pts, ifc_pts)\n", "        \n", "        # Forward pass\n", "        optimizer.zero_grad()\n", "        predicted_transform = model(drone_tensor, ifc_tensor)\n", "        \n", "        # Compute GCP-anchored loss\n", "        loss = gcp_loss(predicted_transform, drone_gcps, ifc_gcps)\n", "        \n", "        # Backward pass\n", "        loss.backward()\n", "        optimizer.step()\n", "        \n", "        losses.append(loss.item())\n", "        \n", "        if (epoch + 1) % 20 == 0:\n", "            print(f\"  Epoch {epoch+1}/{epochs}, Loss: {loss.item():.6f}\")\n", "    \n", "    print(f\"Training completed. Final loss: {losses[-1]:.6f}\")\n", "    return losses\n", "\n", "# Train the model\n", "training_losses = train_pcrnet(model, drone_points, ifc_points, drone_gcps, ifc_gcps, pcrnet_epochs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Apply Learned Transformation"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Learned transformation matrix:\n", "[[ -65916.03      4920.9507   77280.55    102861.35  ]\n", " [ -47555.367   -14800.642   281735.16    379097.47  ]\n", " [ -31072.244    24955.896  -298901.47   -336439.47  ]]\n", "GCP-PCRNet Alignment Results:\n", "  RMSE: 146503458204.66m\n", "  Median distance: 146503144481.23m\n", "  Good points (<2m): 0.0%\n", "  Centroid error: 146503454229.138m\n"]}], "source": ["def apply_pcrnet_transformation(model, drone_pts, ifc_pts):\n", "    \"\"\"Apply learned transformation to full point cloud\"\"\"\n", "    model.eval()\n", "    \n", "    with torch.no_grad():\n", "        # Prepare data\n", "        drone_tensor, ifc_tensor = prepare_training_data(drone_pts, ifc_pts, num_points=2048)\n", "        \n", "        # Predict transformation\n", "        predicted_transform = model(drone_tensor, ifc_tensor)\n", "        transform_matrix = predicted_transform.squeeze(0).cpu().numpy()\n", "        \n", "        print(f\"Learned transformation matrix:\")\n", "        print(transform_matrix)\n", "        \n", "        # Apply to full drone point cloud\n", "        drone_homo = np.column_stack([drone_pts, np.ones(len(drone_pts))])\n", "        transformed_drone = (transform_matrix @ drone_homo.T).T\n", "        \n", "        return transformed_drone, transform_matrix\n", "\n", "def evaluate_alignment(transformed_drone, ifc_pts, method_name=\"PCRNet\"):\n", "    \"\"\"Evaluate alignment quality\"\"\"\n", "    # Sample points for evaluation\n", "    sample_size = min(5000, len(transformed_drone), len(ifc_pts))\n", "    \n", "    drone_sample = transformed_drone[np.random.choice(len(transformed_drone), sample_size, replace=False)]\n", "    ifc_sample = ifc_pts[np.random.choice(len(ifc_pts), sample_size, replace=False)]\n", "    \n", "    # Compute nearest neighbor distances\n", "    tree = cKDTree(ifc_sample)\n", "    distances, _ = tree.query(drone_sample)\n", "    \n", "    # Metrics\n", "    rmse = np.sqrt(np.mean(distances**2))\n", "    median_dist = np.median(distances)\n", "    good_points = np.sum(distances < 2.0) / len(distances) * 100\n", "    \n", "    # Centroid alignment\n", "    drone_center = np.mean(transformed_drone, axis=0)\n", "    ifc_center = np.mean(ifc_pts, axis=0)\n", "    centroid_error = np.linalg.norm(drone_center - ifc_center)\n", "    \n", "    results = {\n", "        'method': method_name,\n", "        'rmse': rmse,\n", "        'median_distance': median_dist,\n", "        'good_points_pct': good_points,\n", "        'centroid_error': centroid_error,\n", "        'sample_size': sample_size\n", "    }\n", "    \n", "    print(f\"{method_name} Alignment Results:\")\n", "    print(f\"  RMSE: {rmse:.2f}m\")\n", "    print(f\"  Median distance: {median_dist:.2f}m\")\n", "    print(f\"  Good points (<2m): {good_points:.1f}%\")\n", "    print(f\"  Centroid error: {centroid_error:.3f}m\")\n", "    \n", "    return results\n", "\n", "# Apply learned transformation\n", "transformed_drone, learned_transform = apply_pcrnet_transformation(model, drone_points, ifc_points)\n", "pcrnet_results = evaluate_alignment(transformed_drone, ifc_points, \"GCP-PCRNet\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Baseline Comparisons:\n", "Coordinate-Only Alignment Results:\n", "  RMSE: 30.59m\n", "  Median distance: 10.64m\n", "  Good points (<2m): 1.8%\n", "  Centroid error: 0.000m\n", "Insufficient GCPs for rigid transformation, using coordinate baseline\n", "GCP-Rigid Alignment Results:\n", "  RMSE: 29.83m\n", "  Median distance: 10.35m\n", "  Good points (<2m): 1.6%\n", "  Centroid error: 0.000m\n"]}], "source": ["def coordinate_baseline_alignment(drone_pts, ifc_pts):\n", "    \"\"\"Simple coordinate-based alignment for comparison\"\"\"\n", "    drone_center = np.mean(drone_pts, axis=0)\n", "    ifc_center = np.mean(ifc_pts, axis=0)\n", "    offset = ifc_center - drone_center\n", "    \n", "    aligned_drone = drone_pts + offset\n", "    return aligned_drone, offset\n", "\n", "def gcp_baseline_alignment(drone_pts, ifc_pts, drone_gcps, ifc_gcps):\n", "    \"\"\"GCP-based rigid transformation using Procrustes analysis\"\"\"\n", "    if len(drone_gcps) < 3:\n", "        print(\"Insufficient GCPs for rigid transformation, using coordinate baseline\")\n", "        return coordinate_baseline_alignment(drone_pts, ifc_pts)\n", "    \n", "    # Center the GCP sets\n", "    drone_gcp_centered = drone_gcps - np.mean(drone_gcps, axis=0)\n", "    ifc_gcp_centered = ifc_gcps - np.mean(ifc_gcps, axis=0)\n", "    \n", "    # Compute optimal rotation using SVD\n", "    H = drone_gcp_centered.T @ ifc_gcp_centered\n", "    U, S, Vt = np.linalg.svd(H)\n", "    R = Vt.T @ U.T\n", "    \n", "    # Ensure proper rotation (det(R) = 1)\n", "    if np.linalg.det(R) < 0:\n", "        Vt[-1, :] *= -1\n", "        R = Vt.T @ U.T\n", "    \n", "    # Compute translation\n", "    t = np.mean(ifc_gcps, axis=0) - R @ np.mean(drone_gcps, axis=0)\n", "    \n", "    # Apply transformation\n", "    aligned_drone = (R @ drone_pts.T).T + t\n", "    \n", "    return aligned_drone, (R, t)\n", "\n", "# Compare with baselines\n", "print(\"\\nBaseline Comparisons:\")\n", "\n", "# Coordinate-only baseline\n", "coord_aligned, coord_offset = coordinate_baseline_alignment(drone_points, ifc_points)\n", "coord_results = evaluate_alignment(coord_aligned, ifc_points, \"Coordinate-Only\")\n", "\n", "# GCP-based rigid transformation baseline\n", "gcp_aligned, gcp_transform = gcp_baseline_alignment(drone_points, ifc_points, drone_gcps, ifc_gcps)\n", "gcp_results = evaluate_alignment(gcp_aligned, ifc_points, \"GCP-Rigid\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Results Analysis and Visualization"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/png": "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**********************************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", "text/plain": ["<Figure size 1000x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Method Comparison Summary:\n", "            method          rmse  median_distance  good_points_pct  \\\n", "0  Coordinate-Only  3.059000e+01     1.063800e+01             1.76   \n", "1        GCP-Rigid  2.983400e+01     1.035200e+01             1.56   \n", "2       GCP-PCRNet  1.465035e+11     1.465031e+11             0.00   \n", "\n", "   centroid_error  \n", "0    0.000000e+00  \n", "1    0.000000e+00  \n", "2    1.465035e+11  \n"]}], "source": ["# Plot training loss\n", "if enable_visualization:\n", "    plt.figure(figsize=(10, 6))\n", "    \n", "    plt.subplot(1, 2, 1)\n", "    plt.plot(training_losses)\n", "    plt.title('PCRNet Training Loss')\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Loss')\n", "    plt.grid(True)\n", "    \n", "    # Compare alignment methods\n", "    plt.subplot(1, 2, 2)\n", "    methods = ['Coordinate-Only', 'GCP-Rigid', 'GCP-PCRNet']\n", "    rmse_values = [coord_results['rmse'], gcp_results['rmse'], pcrnet_results['rmse']]\n", "    \n", "    plt.bar(methods, rmse_values, color=['blue', 'orange', 'green'])\n", "    plt.title('Alignment Method Comparison')\n", "    plt.ylabel('RMSE (m)')\n", "    plt.xticks(rotation=45)\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig(output_path / f\"{site_name}_gcp_pcrnet_analysis.png\", dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "\n", "# Summary comparison\n", "print(\"\\nMethod Comparison Summary:\")\n", "comparison_df = pd.DataFrame([\n", "    coord_results,\n", "    gcp_results,\n", "    pcrnet_results\n", "])\n", "print(comparison_df[['method', 'rmse', 'median_distance', 'good_points_pct', 'centroid_error']].round(3))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Save Results"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Results saved to: ../../../data/output_runs/gcp_pcrnet_alignment/ransac_pmf\n", "Best performing method: GCP-Rigid\n", "\n", "GCP-Anchored PCRNet Alignment Complete\n", "Final PCRNet RMSE: 146503458204.66m\n", "Training converged with loss: 26000388534318206550016.000000\n"]}], "source": ["if save_results:\n", "    # Save aligned point cloud\n", "    aligned_pcd = o3d.geometry.PointCloud()\n", "    aligned_pcd.points = o3d.utility.Vector3dVector(transformed_drone)\n", "    o3d.io.write_point_cloud(str(output_path / f\"{site_name}_gcp_pcrnet_aligned.ply\"), aligned_pcd)\n", "    \n", "    # Save comprehensive results\n", "    results_data = {\n", "        'experiment_info': {\n", "            'site_name': site_name,\n", "            'ground_method': ground_method,\n", "            'timestamp': datetime.now().isoformat(),\n", "            'gcp_search_radius': gcp_search_radius,\n", "            'pcrnet_epochs': pcrnet_epochs,\n", "            'learning_rate': learning_rate\n", "        },\n", "        'gcp_info': {\n", "            'num_gcps_loaded': len(ifc_gcp_coords),\n", "            'num_correspondences': len(drone_gcps),\n", "            'gcp_locations': gcp_df['Location'].tolist(),\n", "            'gcp_point_ids': gcp_df['Point_ID'].tolist(),\n", "            'drone_gcp_coords': drone_gcps.tolist(),\n", "            'ifc_gcp_coords': ifc_gcps.tolist()\n", "        },\n", "        'learned_transformation': learned_transform.tolist(),\n", "        'training_losses': training_losses,\n", "        'final_loss': training_losses[-1],\n", "        'alignment_results': {\n", "            'coordinate_only': coord_results,\n", "            'gcp_rigid': gcp_results,\n", "            'gcp_pcrnet': pcrnet_results\n", "        },\n", "        'best_method': min([coord_results, gcp_results, pcrnet_results], key=lambda x: x['rmse'])['method']\n", "    }\n", "    \n", "    with open(output_path / f\"{site_name}_gcp_pcrnet_results.json\", 'w') as f:\n", "        json.dump(results_data, f, indent=2)\n", "    \n", "    print(f\"\\nResults saved to: {output_path}\")\n", "    print(f\"Best performing method: {results_data['best_method']}\")\n", "\n", "print(\"\\nGCP-Anchored PCRNet Alignment Complete\")\n", "print(f\"Final PCRNet RMSE: {pcrnet_results['rmse']:.2f}m\")\n", "print(f\"Training converged with loss: {training_losses[-1]:.6f}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}