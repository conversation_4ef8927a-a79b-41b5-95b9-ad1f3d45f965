{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# GCP-Anchored PCRNet Point Cloud Registration\n", "\n", "This notebook explores using Ground Control Points (GCPs) as anchors for deep learning-based point cloud registration using PCRNet architecture.\n", "\n", "## Approach\n", "1. Extract GCP correspondences from drone and IFC data\n", "2. Use GCPs to initialize and constrain PCRNet training\n", "3. Apply learned transformation for full point cloud alignment\n", "4. Validate results against coordinate-only baseline"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Parameters (Papermill)\n", "ground_method = \"ransac_pmf\"  # Ground segmentation method\n", "site_name = \"trino_enel\"\n", "gcp_search_radius = 5.0  # Radius for GCP matching\n", "pcrnet_epochs = 100\n", "learning_rate = 0.001\n", "batch_size = 1\n", "\n", "output_dir = \"../../../data/output_runs/gcp_pcrnet_alignment\"\n", "enable_visualization = True\n", "save_results = True"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GCP-Anchored PCRNet Point Cloud Registration\n", "Site: trino_enel\n", "Device: cpu\n", "Output: ../../../data/output_runs/gcp_pcrnet_alignment/ransac_pmf\n"]}], "source": ["# Imports\n", "import numpy as np\n", "import open3d as o3d\n", "import torch\n", "import torch.nn as nn\n", "import torch.optim as optim\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy.spatial import cKDTree\n", "from sklearn.neighbors import NearestNeighbors\n", "import json\n", "\n", "# Setup\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')\n", "output_path = Path(output_dir) / ground_method\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"GCP-Anchored PCRNet Point Cloud Registration\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Device: {device}\")\n", "print(f\"Output: {output_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Data Loading and Preprocessing"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded drone points: 517,002\n", "Loaded IFC points: 5,480,340\n", "Warning: Could not load GCP data: [Errno 2] No such file or directory: '../../../data/validation/Trino_piles.csv'\n"]}], "source": ["# Define file paths\n", "drone_file = f\"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply\"\n", "ifc_pointcloud_file = f\"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\"\n", "gcp_file = f\"../../../data/validation/Trino_piles.csv\"  # Ground truth pile locations as GCPs\n", "\n", "def load_point_clouds():\n", "    \"\"\"Load drone and IFC point clouds\"\"\"\n", "    drone_pcd = o3d.io.read_point_cloud(drone_file)\n", "    ifc_pcd = o3d.io.read_point_cloud(ifc_pointcloud_file)\n", "    \n", "    drone_points = np.asarray(drone_pcd.points)\n", "    ifc_points = np.asarray(ifc_pcd.points)\n", "    \n", "    print(f\"Loaded drone points: {len(drone_points):,}\")\n", "    print(f\"Loaded IFC points: {len(ifc_points):,}\")\n", "    \n", "    return drone_points, ifc_points\n", "\n", "def load_gcp_data():\n", "    \"\"\"Load ground control points from validation data\"\"\"\n", "    try:\n", "        gcp_df = pd.read_csv(gcp_file)\n", "        gcp_coords = gcp_df[['X', 'Y', 'Z']].values\n", "        print(f\"Loaded {len(gcp_coords)} GCP coordinates\")\n", "        return gcp_coords\n", "    except Exception as e:\n", "        print(f\"Warning: Could not load GCP data: {e}\")\n", "        return None\n", "\n", "drone_points, ifc_points = load_point_clouds()\n", "gcp_coords = load_gcp_data()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. GCP Correspondence Extraction"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No GCP data available, using centroid-based correspondence\n"]}], "source": ["def extract_gcp_correspondences(drone_pts, ifc_pts, gcp_coords, search_radius=5.0):\n", "    \"\"\"Extract corresponding points near GCP locations\"\"\"\n", "    if gcp_coords is None:\n", "        print(\"No GCP data available, using centroid-based correspondence\")\n", "        # Fallback: use centroids as single correspondence\n", "        drone_center = np.mean(drone_pts, axis=0).reshape(1, 3)\n", "        ifc_center = np.mean(ifc_pts, axis=0).reshape(1, 3)\n", "        return drone_center, ifc_center\n", "    \n", "    drone_correspondences = []\n", "    ifc_correspondences = []\n", "    \n", "    # Build KD-trees for efficient nearest neighbor search\n", "    drone_tree = cKDTree(drone_pts)\n", "    ifc_tree = cKDTree(ifc_pts)\n", "    \n", "    print(f\"Searching for correspondences within {search_radius}m of GCPs...\")\n", "    \n", "    for i, gcp in enumerate(gcp_coords):\n", "        # Find drone points near this GCP\n", "        drone_indices = drone_tree.query_ball_point(gcp, search_radius)\n", "        \n", "        # Find IFC points near this GCP\n", "        ifc_indices = ifc_tree.query_ball_point(gcp, search_radius)\n", "        \n", "        if len(drone_indices) > 0 and len(ifc_indices) > 0:\n", "            # Use centroid of nearby points as correspondence\n", "            drone_centroid = np.mean(drone_pts[drone_indices], axis=0)\n", "            ifc_centroid = np.mean(ifc_pts[ifc_indices], axis=0)\n", "            \n", "            drone_correspondences.append(drone_centroid)\n", "            ifc_correspondences.append(ifc_centroid)\n", "            \n", "            print(f\"  GCP {i+1}: Found {len(drone_indices)} drone + {len(ifc_indices)} IFC points\")\n", "    \n", "    if len(drone_correspondences) == 0:\n", "        print(\"No GCP correspondences found, using centroid fallback\")\n", "        drone_center = np.mean(drone_pts, axis=0).reshape(1, 3)\n", "        ifc_center = np.mean(ifc_pts, axis=0).reshape(1, 3)\n", "        return drone_center, ifc_center\n", "    \n", "    drone_correspondences = np.array(drone_correspondences)\n", "    ifc_correspondences = np.array(ifc_correspondences)\n", "    \n", "    print(f\"Extracted {len(drone_correspondences)} GCP correspondences\")\n", "    return drone_correspondences, ifc_correspondences\n", "\n", "drone_gcps, ifc_gcps = extract_gcp_correspondences(drone_points, ifc_points, gcp_coords, gcp_search_radius)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Simplified PCRNet Architecture"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model initialized with 1324172 parameters\n"]}], "source": ["class SimplePCRNet(nn.Module):\n", "    \"\"\"Simplified PCRNet for point cloud registration\"\"\"\n", "    \n", "    def __init__(self, feature_dim=1024):\n", "        super(SimplePCRNet, self).__init__()\n", "        \n", "        # Point feature extraction (simplified PointNet)\n", "        self.conv1 = nn.Conv1d(3, 64, 1)\n", "        self.conv2 = nn.Conv1d(64, 128, 1)\n", "        self.conv3 = nn.Conv1d(128, feature_dim, 1)\n", "        \n", "        # Transformation prediction\n", "        self.fc1 = nn.Linear(feature_dim * 2, 512)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.fc3 = nn.Linear(256, 12)  # 3x4 transformation matrix (rotation + translation)\n", "        \n", "        self.relu = nn.ReLU()\n", "        self.dropout = nn.Dropout(0.3)\n", "        \n", "    def extract_features(self, x):\n", "        \"\"\"Extract global features from point cloud\"\"\"\n", "        # x: (batch_size, 3, num_points)\n", "        x = self.relu(self.conv1(x))\n", "        x = self.relu(self.conv2(x))\n", "        x = self.conv3(x)\n", "        \n", "        # Global max pooling\n", "        x = torch.max(x, 2)[0]  # (batch_size, feature_dim)\n", "        return x\n", "    \n", "    def forward(self, source, target):\n", "        \"\"\"Predict transformation from source to target\"\"\"\n", "        # Extract features\n", "        source_feat = self.extract_features(source)\n", "        target_feat = self.extract_features(target)\n", "        \n", "        # Concatenate features\n", "        combined = torch.cat([source_feat, target_feat], dim=1)\n", "        \n", "        # Predict transformation\n", "        x = self.relu(self.fc1(combined))\n", "        x = self.dropout(x)\n", "        x = self.relu(self.fc2(x))\n", "        x = self.dropout(x)\n", "        x = self.fc3(x)\n", "        \n", "        # Reshape to 3x4 transformation matrix\n", "        transform = x.view(-1, 3, 4)\n", "        \n", "        return transform\n", "\n", "# Initialize model\n", "model = SimplePCRNet().to(device)\n", "optimizer = optim.Adam(model.parameters(), lr=learning_rate)\n", "\n", "print(f\"Model initialized with {sum(p.numel() for p in model.parameters())} parameters\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON><PERSON>-Anchored Training"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Training PCRNet for 100 epochs...\n", "  Epoch 20/100, Loss: 108475141776726525214720.000000\n", "  Epoch 40/100, Loss: 268025264653890165407744.000000\n", "  Epoch 60/100, Loss: 143813906267984040558592.000000\n", "  Epoch 80/100, Loss: 173936727771610613809152.000000\n", "  Epoch 100/100, Loss: 136216270646215230619648.000000\n", "Training completed. Final loss: 136216270646215230619648.000000\n"]}], "source": ["def prepare_training_data(drone_pts, ifc_pts, num_points=1024):\n", "    \"\"\"Prepare point clouds for training\"\"\"\n", "    # Downsample for training efficiency\n", "    if len(drone_pts) > num_points:\n", "        drone_indices = np.random.choice(len(drone_pts), num_points, replace=False)\n", "        drone_sample = drone_pts[drone_indices]\n", "    else:\n", "        drone_sample = drone_pts\n", "    \n", "    if len(ifc_pts) > num_points:\n", "        ifc_indices = np.random.choice(len(ifc_pts), num_points, replace=False)\n", "        ifc_sample = ifc_pts[ifc_indices]\n", "    else:\n", "        ifc_sample = ifc_pts\n", "    \n", "    # Convert to tensors and transpose for conv1d (batch_size, channels, num_points)\n", "    drone_tensor = torch.FloatTensor(drone_sample).unsqueeze(0).transpose(1, 2).to(device)\n", "    ifc_tensor = torch.FloatTensor(ifc_sample).unsqueeze(0).transpose(1, 2).to(device)\n", "    \n", "    return drone_tensor, ifc_tensor\n", "\n", "def gcp_loss(predicted_transform, drone_gcps, ifc_gcps):\n", "    \"\"\"Compute loss based on GCP correspondences\"\"\"\n", "    # Convert GCPs to tensors\n", "    drone_gcp_tensor = torch.FloatTensor(drone_gcps).to(device)\n", "    ifc_gcp_tensor = torch.FloatTensor(ifc_gcps).to(device)\n", "    \n", "    # Apply predicted transformation to drone GCPs\n", "    # Add homogeneous coordinate\n", "    drone_homo = torch.cat([drone_gcp_tensor, torch.ones(len(drone_gcps), 1).to(device)], dim=1)\n", "    \n", "    # Apply transformation\n", "    transformed_drone = torch.matmul(predicted_transform.squeeze(0), drone_homo.T).T\n", "    \n", "    # Compute MSE loss with target GCPs\n", "    loss = nn.MSELoss()(transformed_drone, ifc_gcp_tensor)\n", "    \n", "    return loss\n", "\n", "def train_pcrnet(model, drone_pts, ifc_pts, drone_gcps, ifc_gcps, epochs=100):\n", "    \"\"\"Train PCRNet with GCP anchoring\"\"\"\n", "    model.train()\n", "    losses = []\n", "    \n", "    print(f\"Training PCRNet for {epochs} epochs...\")\n", "    \n", "    for epoch in range(epochs):\n", "        # Prepare training data with random sampling\n", "        drone_tensor, ifc_tensor = prepare_training_data(drone_pts, ifc_pts)\n", "        \n", "        # Forward pass\n", "        optimizer.zero_grad()\n", "        predicted_transform = model(drone_tensor, ifc_tensor)\n", "        \n", "        # Compute GCP-anchored loss\n", "        loss = gcp_loss(predicted_transform, drone_gcps, ifc_gcps)\n", "        \n", "        # Backward pass\n", "        loss.backward()\n", "        optimizer.step()\n", "        \n", "        losses.append(loss.item())\n", "        \n", "        if (epoch + 1) % 20 == 0:\n", "            print(f\"  Epoch {epoch+1}/{epochs}, Loss: {loss.item():.6f}\")\n", "    \n", "    print(f\"Training completed. Final loss: {losses[-1]:.6f}\")\n", "    return losses\n", "\n", "# Train the model\n", "training_losses = train_pcrnet(model, drone_points, ifc_points, drone_gcps, ifc_gcps, pcrnet_epochs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Apply Learned Transformation"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Learned transformation matrix:\n", "[[ -32173.332   -55633.164   606011.44    604503.75  ]\n", " [  -5508.3726    6139.288  -259571.81   -285575.97  ]\n", " [  58193.895   -86553.44    654770.2     671822.1   ]]\n", "GCP-PCRNet Alignment Results:\n", "  RMSE: 503348927262.47m\n", "  Median distance: 503353087064.16m\n", "  Good points (<2m): 0.0%\n", "  Centroid error: 503348917764.442m\n"]}], "source": ["def apply_pcrnet_transformation(model, drone_pts, ifc_pts):\n", "    \"\"\"Apply learned transformation to full point cloud\"\"\"\n", "    model.eval()\n", "    \n", "    with torch.no_grad():\n", "        # Prepare data\n", "        drone_tensor, ifc_tensor = prepare_training_data(drone_pts, ifc_pts, num_points=2048)\n", "        \n", "        # Predict transformation\n", "        predicted_transform = model(drone_tensor, ifc_tensor)\n", "        transform_matrix = predicted_transform.squeeze(0).cpu().numpy()\n", "        \n", "        print(f\"Learned transformation matrix:\")\n", "        print(transform_matrix)\n", "        \n", "        # Apply to full drone point cloud\n", "        drone_homo = np.column_stack([drone_pts, np.ones(len(drone_pts))])\n", "        transformed_drone = (transform_matrix @ drone_homo.T).T\n", "        \n", "        return transformed_drone, transform_matrix\n", "\n", "def evaluate_alignment(transformed_drone, ifc_pts, method_name=\"PCRNet\"):\n", "    \"\"\"Evaluate alignment quality\"\"\"\n", "    # Sample points for evaluation\n", "    sample_size = min(5000, len(transformed_drone), len(ifc_pts))\n", "    \n", "    drone_sample = transformed_drone[np.random.choice(len(transformed_drone), sample_size, replace=False)]\n", "    ifc_sample = ifc_pts[np.random.choice(len(ifc_pts), sample_size, replace=False)]\n", "    \n", "    # Compute nearest neighbor distances\n", "    tree = cKDTree(ifc_sample)\n", "    distances, _ = tree.query(drone_sample)\n", "    \n", "    # Metrics\n", "    rmse = np.sqrt(np.mean(distances**2))\n", "    median_dist = np.median(distances)\n", "    good_points = np.sum(distances < 2.0) / len(distances) * 100\n", "    \n", "    # Centroid alignment\n", "    drone_center = np.mean(transformed_drone, axis=0)\n", "    ifc_center = np.mean(ifc_pts, axis=0)\n", "    centroid_error = np.linalg.norm(drone_center - ifc_center)\n", "    \n", "    results = {\n", "        'method': method_name,\n", "        'rmse': rmse,\n", "        'median_distance': median_dist,\n", "        'good_points_pct': good_points,\n", "        'centroid_error': centroid_error,\n", "        'sample_size': sample_size\n", "    }\n", "    \n", "    print(f\"{method_name} Alignment Results:\")\n", "    print(f\"  RMSE: {rmse:.2f}m\")\n", "    print(f\"  Median distance: {median_dist:.2f}m\")\n", "    print(f\"  Good points (<2m): {good_points:.1f}%\")\n", "    print(f\"  Centroid error: {centroid_error:.3f}m\")\n", "    \n", "    return results\n", "\n", "# Apply learned transformation\n", "transformed_drone, learned_transform = apply_pcrnet_transformation(model, drone_points, ifc_points)\n", "pcrnet_results = evaluate_alignment(transformed_drone, ifc_points, \"GCP-PCRNet\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Baseline Comparisons:\n", "Coordinate-Only Alignment Results:\n", "  RMSE: 30.18m\n", "  Median distance: 11.09m\n", "  Good points (<2m): 1.6%\n", "  Centroid error: 0.000m\n", "Insufficient GCPs for rigid transformation, using coordinate baseline\n", "GCP-Rigid Alignment Results:\n", "  RMSE: 30.55m\n", "  Median distance: 10.60m\n", "  Good points (<2m): 1.5%\n", "  Centroid error: 0.000m\n"]}], "source": ["def coordinate_baseline_alignment(drone_pts, ifc_pts):\n", "    \"\"\"Simple coordinate-based alignment for comparison\"\"\"\n", "    drone_center = np.mean(drone_pts, axis=0)\n", "    ifc_center = np.mean(ifc_pts, axis=0)\n", "    offset = ifc_center - drone_center\n", "    \n", "    aligned_drone = drone_pts + offset\n", "    return aligned_drone, offset\n", "\n", "def gcp_baseline_alignment(drone_pts, ifc_pts, drone_gcps, ifc_gcps):\n", "    \"\"\"GCP-based rigid transformation using Procrustes analysis\"\"\"\n", "    if len(drone_gcps) < 3:\n", "        print(\"Insufficient GCPs for rigid transformation, using coordinate baseline\")\n", "        return coordinate_baseline_alignment(drone_pts, ifc_pts)\n", "    \n", "    # Center the GCP sets\n", "    drone_gcp_centered = drone_gcps - np.mean(drone_gcps, axis=0)\n", "    ifc_gcp_centered = ifc_gcps - np.mean(ifc_gcps, axis=0)\n", "    \n", "    # Compute optimal rotation using SVD\n", "    H = drone_gcp_centered.T @ ifc_gcp_centered\n", "    U, S, Vt = np.linalg.svd(H)\n", "    R = Vt.T @ U.T\n", "    \n", "    # Ensure proper rotation (det(R) = 1)\n", "    if np.linalg.det(R) < 0:\n", "        Vt[-1, :] *= -1\n", "        R = Vt.T @ U.T\n", "    \n", "    # Compute translation\n", "    t = np.mean(ifc_gcps, axis=0) - R @ np.mean(drone_gcps, axis=0)\n", "    \n", "    # Apply transformation\n", "    aligned_drone = (R @ drone_pts.T).T + t\n", "    \n", "    return aligned_drone, (R, t)\n", "\n", "# Compare with baselines\n", "print(\"\\nBaseline Comparisons:\")\n", "\n", "# Coordinate-only baseline\n", "coord_aligned, coord_offset = coordinate_baseline_alignment(drone_points, ifc_points)\n", "coord_results = evaluate_alignment(coord_aligned, ifc_points, \"Coordinate-Only\")\n", "\n", "# GCP-based rigid transformation baseline\n", "gcp_aligned, gcp_transform = gcp_baseline_alignment(drone_points, ifc_points, drone_gcps, ifc_gcps)\n", "gcp_results = evaluate_alignment(gcp_aligned, ifc_points, \"GCP-Rigid\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Results Analysis and Visualization"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"image/png": "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*******************************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", "text/plain": ["<Figure size 1000x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Method Comparison Summary:\n", "            method          rmse  median_distance  good_points_pct  \\\n", "0  Coordinate-Only  3.018100e+01     1.108600e+01             1.64   \n", "1        GCP-Rigid  3.055100e+01     1.060200e+01             1.48   \n", "2       GCP-PCRNet  5.033489e+11     5.033531e+11             0.00   \n", "\n", "   centroid_error  \n", "0    0.000000e+00  \n", "1    0.000000e+00  \n", "2    5.033489e+11  \n"]}], "source": ["# Plot training loss\n", "if enable_visualization:\n", "    plt.figure(figsize=(10, 6))\n", "    \n", "    plt.subplot(1, 2, 1)\n", "    plt.plot(training_losses)\n", "    plt.title('PCRNet Training Loss')\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('Loss')\n", "    plt.grid(True)\n", "    \n", "    # Compare alignment methods\n", "    plt.subplot(1, 2, 2)\n", "    methods = ['Coordinate-Only', 'GCP-Rigid', 'GCP-PCRNet']\n", "    rmse_values = [coord_results['rmse'], gcp_results['rmse'], pcrnet_results['rmse']]\n", "    \n", "    plt.bar(methods, rmse_values, color=['blue', 'orange', 'green'])\n", "    plt.title('Alignment Method Comparison')\n", "    plt.ylabel('RMSE (m)')\n", "    plt.xticks(rotation=45)\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig(output_path / f\"{site_name}_gcp_pcrnet_analysis.png\", dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "\n", "# Summary comparison\n", "print(\"\\nMethod Comparison Summary:\")\n", "comparison_df = pd.DataFrame([\n", "    coord_results,\n", "    gcp_results,\n", "    pcrnet_results\n", "])\n", "print(comparison_df[['method', 'rmse', 'median_distance', 'good_points_pct', 'centroid_error']].round(3))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Save Results"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Results saved to: ../../../data/output_runs/gcp_pcrnet_alignment/ransac_pmf\n", "Best performing method: Coordinate-Only\n", "\n", "GCP-Anchored PCRNet Alignment Complete\n", "Final PCRNet RMSE: 503348927262.47m\n", "Training converged with loss: 136216270646215230619648.000000\n"]}], "source": ["if save_results:\n", "    # Save aligned point cloud\n", "    aligned_pcd = o3d.geometry.PointCloud()\n", "    aligned_pcd.points = o3d.utility.Vector3dVector(transformed_drone)\n", "    o3d.io.write_point_cloud(str(output_path / f\"{site_name}_gcp_pcrnet_aligned.ply\"), aligned_pcd)\n", "    \n", "    # Save comprehensive results\n", "    results_data = {\n", "        'experiment_info': {\n", "            'site_name': site_name,\n", "            'ground_method': ground_method,\n", "            'timestamp': datetime.now().isoformat(),\n", "            'gcp_search_radius': gcp_search_radius,\n", "            'pcrnet_epochs': pcrnet_epochs,\n", "            'learning_rate': learning_rate\n", "        },\n", "        'gcp_info': {\n", "            'num_gcps_loaded': len(gcp_coords) if gcp_coords is not None else 0,\n", "            'num_correspondences': len(drone_gcps),\n", "            'drone_gcp_coords': drone_gcps.tolist(),\n", "            'ifc_gcp_coords': ifc_gcps.tolist()\n", "        },\n", "        'learned_transformation': learned_transform.tolist(),\n", "        'training_losses': training_losses,\n", "        'final_loss': training_losses[-1],\n", "        'alignment_results': {\n", "            'coordinate_only': coord_results,\n", "            'gcp_rigid': gcp_results,\n", "            'gcp_pcrnet': pcrnet_results\n", "        },\n", "        'best_method': min([coord_results, gcp_results, pcrnet_results], key=lambda x: x['rmse'])['method']\n", "    }\n", "    \n", "    with open(output_path / f\"{site_name}_gcp_pcrnet_results.json\", 'w') as f:\n", "        json.dump(results_data, f, indent=2)\n", "    \n", "    print(f\"\\nResults saved to: {output_path}\")\n", "    print(f\"Best performing method: {results_data['best_method']}\")\n", "\n", "print(\"\\nGCP-Anchored PCRNet Alignment Complete\")\n", "print(f\"Final PCRNet RMSE: {pcrnet_results['rmse']:.2f}m\")\n", "print(f\"Training converged with loss: {training_losses[-1]:.6f}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}