import sys
from pathlib import Path

# Add the `notebooks` folder to sys.path
notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]

if str(notebooks_root) not in sys.path:
    sys.path.insert(0, str(notebooks_root))

# Now import from shared package
from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path

# Papermill parameters - these will be injected by Papermill
site_name = "trino_enel"  # Site name for output file naming
project_type = "ENEL"  # Options: "ENEL", "USA"
method_name = "csf"  # Options: "csf", "pmf", "ransac"
timestamp = None  # Auto-generated if None

# Standardized paths
input_data_path = get_processed_data_path(site_name, "denoising")
output_path = get_output_path(f"ground_segmentation_{method_name}", site_name, timestamp)
mlflow_tracking_uri = get_mlflow_tracking_uri()
coordinate_system = "EPSG:32632"

# Find input file dynamically
input_point_cloud = find_latest_file(input_data_path, "*denoised*.ply")
point_cloud_path = str(input_point_cloud)

# MLflow configuration
mlflow_experiment_name = f"ground_segmentation_{project_type}"
mlflow_run_name = f"{method_name}_{site_name}_{timestamp or 'auto'}"


# Papermill parameters - these will be injected by Papermill

# === CSF (Cloth Simulation Filter) Parameters ===
# These control how the cloth behaves and how the ground is identified in point cloud data.

buffer_radius = 50.0  # Buffer radius for spatial filtering (meters)

cloth_resolution = 0.25  
# Size of each grid cell in the cloth mesh (in meters).
# Smaller values → finer cloth mesh → better detail capture but higher compute time.
# Recommended: 0.1 – 0.5 for high-resolution drone data.

max_iterations = 500  
# Maximum number of iterations for cloth simulation convergence.
# Higher values improve accuracy but slow down processing.
# Recommended: 200 – 1000 depending on scene complexity.

classification_threshold = 0.4  
# Vertical distance (in meters) used to classify a point as ground.
# Points below this threshold from the cloth surface are marked as ground.
# Tuning this helps avoid misclassifying low-lying vegetation or panels as ground.

rigidness = 3  
# Stiffness of the cloth. Values: 1 (soft) → 3 (rigid).
# Soft cloth adapts to local terrain variation (e.g., small slopes or trenches),
# while rigid cloth gives a smoother approximation of large-scale terrain.

time_step = 0.65  
# Simulation timestep size. Affects cloth stability and convergence speed.
# Should generally stay between 0.5 and 1.0.

neighbor_search_radius = 1.2  
# Radius (in meters) used to refine classification using local neighborhood context.
# Helps reduce noise in ground/non-ground labels by smoothing small misclassifications.


# Import libraries
import laspy
import numpy as np
import os
import json
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import time
import logging
from pathlib import Path
from datetime import datetime
from sklearn.neighbors import NearestNeighbors

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Standardized paths
from shared.utils import resolve_point_cloud_path
# Raw data path
raw_path = get_data_path(site_name, data_type="raw")
logger.info(f"Checking raw data path: {raw_path}, Exists: {raw_path.exists()}")

# Timestamped output directory
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
output_path = get_output_path(notebook_type="denoising", site_name=site_name, timestamp=timestamp)

# Processed data path for this stage
processed_path = get_processed_data_path(site_name, processing_stage="denoising")

# Analysis output path 
analysis_output_path = get_data_path(site_name, data_type="analysis_output")

# Determine input path
input_path = processed_path / f"{site_name}_denoised.ply"
point_cloud_file = resolve_point_cloud_path(input_path)

# Ground segmentation output directory (organized by site/project)
ground_seg_path = get_processed_data_path(site_name, processing_stage="ground_segmentation")
ground_seg_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Ground segmentation output path: {ground_seg_path.resolve()}")


!ls -lh /Users/<USER>/Documents/GitHub/asbuilt-foundation-analysis/data/processed/trino_enel/ground_segmentation

import open3d as o3d

# Use the resolved and validated file
logger.info(f"Reading PLY file: {point_cloud_file.resolve()}")

pcd = o3d.io.read_point_cloud(str(point_cloud_file))

if not pcd.has_points():
    raise ValueError("Loaded PLY file contains no points.")

points = np.asarray(pcd.points)
logger.info(f"Loaded {points.shape[0]} points from {point_cloud_file}")


# ## Simplified CSF (Mock logic, replaceable with physics sim)
z_median = np.median(points[:, 2])
logger.info(f"Estimated ground threshold (Z median): {z_median:.2f}")

ground_mask = points[:, 2] < (z_median + classification_threshold)
ground_points = points[ground_mask]
nonground_points = points[~ground_mask]

logger.info(f"Classified {ground_points.shape[0]} ground and {nonground_points.shape[0]} non-ground points.")

# ## Save Output .PLY
def save_ply(path, points_array):
    pc = o3d.geometry.PointCloud()
    pc.points = o3d.utility.Vector3dVector(points_array)
    o3d.io.write_point_cloud(str(path), pc)
    logger.info(f"Saved: {path}")

# Save the point clouds to the appropriate output paths
analysis_output = output_path / 'analysis_output'
analysis_output.mkdir(parents=True, exist_ok=True)

# Save ground and nonground points to the analysis_output directory
save_ply(analysis_output / f"{site_name}_ground.ply", ground_points)
save_ply(analysis_output / f"{site_name}_nonground.ply", nonground_points)

# Ground/Non-Ground ratio
ground_count = ground_points.shape[0]
nonground_count = nonground_points.shape[0]
total_points = ground_count + nonground_count

ground_ratio = ground_count / total_points
logger.info(f"Ground Ratio: {ground_ratio:.4f}")

nonground_ratio = nonground_count / total_points
logger.info(f"Non-Ground Ratio: {nonground_ratio:.4f}")

ground_z_mean = ground_points[:, 2].mean()
nonground_z_mean = nonground_points[:, 2].mean()
z_separation = nonground_z_mean - ground_z_mean

logger.info("ground_z_mean:{ground_z_mean}")
logger.info(f"nonground_z_mean:{nonground_z_mean}")
logger.info(f"z_separation: {z_separation}")


# Bounding Box Stats
def bounding_box_stats(points):
    min_bound = np.min(points, axis=0)
    max_bound = np.max(points, axis=0)
    return max_bound - min_bound

ground_bbox = bounding_box_stats(ground_points)
nonground_bbox = bounding_box_stats(nonground_points)

logger.info("  Bounding Box Sizes (X, Y, Z):")
logger.info("-" * 50)
logger.info(f"  Ground:     {ground_bbox}")
logger.info(f"  Non-Ground: {nonground_bbox}")

# Height (Z) Distribution Plot
plt.figure(figsize=(10, 5))
plt.hist(ground_points[:, 2], bins=100, alpha=0.6, label='Ground Z')
plt.hist(nonground_points[:, 2], bins=100, alpha=0.6, label='Non-Ground Z')
plt.legend()
plt.title("Z-Height Distribution")
plt.xlabel("Z (Elevation)")
plt.ylabel("Point Count")
plt.grid(True)
plt.show()

# Final readiness logger.info
logger.info("CSF Ground Segmentation - Ready!")
logger.info("=" * 50)
logger.info(f"Project: {project_type}/{site_name}")
logger.info("-" * 50)
logger.info(f"Input path: {point_cloud_file}")
logger.info(f"Output path: {ground_seg_path}")
logger.info(f"Current run output: {output_path}")
logger.info(f"CSF Parameters: resolution={cloth_resolution}m, threshold={classification_threshold}m, rigidness={rigidness}")
logger.info(f"Ground points: {ground_points.shape[0]}")
logger.info(f"Non-ground points: {nonground_points.shape[0]}")
logger.info(f"Total: {ground_points.shape[0] + nonground_points.shape[0]}")


# Visualize the results
import open3d as o3d
import numpy as np

# Create point cloud for ground
pcd_ground = o3d.geometry.PointCloud()
pcd_ground.points = o3d.utility.Vector3dVector(ground_points)
pcd_ground.paint_uniform_color([0.0, 1.0, 0.0])  # Green

# Create point cloud for non-ground
pcd_nonground = o3d.geometry.PointCloud()
pcd_nonground.points = o3d.utility.Vector3dVector(nonground_points)
pcd_nonground.paint_uniform_color([1.0, 0.0, 0.0])  # Red

# Show both together
o3d.visualization.draw_geometries([pcd_ground, pcd_nonground],
                                  window_name="Ground vs Non-Ground",
                                  point_show_normal=False)


# Satisfied with the results save the final output for next stage 
save_ply(ground_seg_path / f"{method_name}/{site_name}_ground.ply", ground_points)
save_ply(ground_seg_path / f"{method_name}/{site_name}_nonground.ply", nonground_points)