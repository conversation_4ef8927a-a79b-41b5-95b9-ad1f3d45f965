import sys
from pathlib import Path

# Add the `notebooks` folder to sys.path
notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]

if str(notebooks_root) not in sys.path:
    sys.path.insert(0, str(notebooks_root))

# Now import from shared package
from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path

# Papermill parameters - these will be injected by Papermill
site_name = "trino_enel"  # Site name for output file naming
project_type = "ENEL"  # Options: "ENEL", "USA"
method_name = "ransac_pmf"  # Options: "csf", "pmf", "ransac"
timestamp = None  # Auto-generated if None

# Standardized paths
input_data_path = get_processed_data_path(site_name, "denoising")
output_path = get_output_path(f"ground_segmentation_{method_name}", site_name, timestamp)
mlflow_tracking_uri = get_mlflow_tracking_uri()
coordinate_system = "EPSG:32633"

# Find input file dynamically
# input_point_cloud = find_latest_file(input_data_path, "*denoised*.ply")
input_point_cloud = Path("../../../data/processed/trino_enel/denoising/trino_enel_denoised_for_registration.ply")
point_cloud_path = str(input_point_cloud)

# MLflow configuration
mlflow_experiment_name = f"ground_segmentation_{project_type}"
mlflow_run_name = f"{method_name}_{site_name}_{timestamp or 'auto'}"

# === RANSAC (Plane Segmentation) Parameters ===
# These parameters control how RANSAC detects planar surfaces in noisy point clouds.

distance_threshold = 0.2  
# Maximum distance (in meters) from a point to a candidate plane for it to be considered an inlier.
# Smaller values yield tighter fitting planes but may miss noisy or partially flat regions.

num_iterations = 1000  
# Number of random sampling iterations to attempt.
# More iterations increase the chance of finding the best-fitting plane.

min_inliers_ratio = 0.05  
# Minimum ratio of inliers (as a percentage of total points) required to accept a plane.
# Helps filter out spurious or small patch detections.

early_stop_ratio = 0.6  
# If a plane is found that covers at least this ratio of total points, RANSAC will stop early.
# Speeds up processing when large planar surfaces (e.g., ground or slabs) dominate.


# === PMF (Progressive Morphological Filter) Parameters ===
# These parameters control how the morphological filter is applied to identify ground points 
# by simulating terrain smoothing across increasing window sizes.

cell_size = 1.0  
# Size of each grid cell when rasterizing the point cloud (in meters).
# Smaller values retain finer surface detail but increase computation.
# Recommended: 0.5 – 2.0 based on point density and terrain complexity.

max_window_size = 33  
# Maximum size (in raster units) of the morphological structuring element.
# Determines the scale of features that can be removed (e.g., buildings, vegetation).
# Larger values capture broader terrain variation but may oversmooth.

slope = 0.15  
# Maximum local slope (in radians) allowed during filtering.
# Points exceeding this elevation change across a window are treated as non-ground.
# Typical values: 0.1 – 0.3 for natural terrain.

max_distance = 2.5  
# Maximum elevation difference (in meters) between a point and the estimated ground surface 
# to still be classified as ground.
# Helps in removing high outliers like trees and rooftops.

initial_distance = 0.5  
# Initial threshold (in meters) for elevation difference during early filtering iterations.
# A tighter threshold avoids early misclassifications and stabilizes the progressive process.

height_threshold_ratio = 0.1  
# Proportion of the lowest height range used to seed initial ground estimation (0–1).
# Typically set between 0.05 and 0.15 to capture the base terrain while ignoring outliers.


# === Processing Control Parameters ===
# These help manage memory usage and performance for large point clouds.

max_points_processing = 1000000  
# Maximum number of points to process in memory at once.
# If exceeded, the point cloud should be downsampled or processed in chunks.

save_intermediate_results = True # Save RANSAC-only and PMF-additional results
create_visualizations = True     # Generate comparison plots

# Import libraries
import numpy as np
import json
import time
import logging
from pathlib import Path
from datetime import datetime
from tqdm import tqdm
from scipy import ndimage
from scipy.ndimage import grey_erosion, grey_dilation
import open3d as o3d
import laspy

# Set random seed for reproducibility
np.random.seed(42)
print("Libraries imported")

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
print("Logging configured")

# Standardized paths
from shared.utils import resolve_point_cloud_path
# Raw data path
raw_path = get_data_path(site_name, data_type="raw")
logger.info(f"Checking raw data path: {raw_path}, Exists: {raw_path.exists()}")

# Timestamped output directory
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
output_path = get_output_path(notebook_type="denoising", site_name=site_name, timestamp=timestamp)

# Processed data path for this stage
processed_path = get_processed_data_path(site_name, processing_stage="denoising")

# Analysis output path 
analysis_output_path = get_data_path(site_name, data_type="analysis_output")

# Determine input path
#input_path = processed_path / f"{site_name}_denoised.ply"
input_path = processed_path / f"{site_name}_denoised_for_registration.ply"  

point_cloud_file = resolve_point_cloud_path(input_path)

# Ground segmentation output directory (organized by site/project)
ground_seg_path = get_processed_data_path(site_name, processing_stage="ground_segmentation")
ground_seg_path.mkdir(parents=True, exist_ok=True)

logger.info(f"Ground segmentation output path: {ground_seg_path.resolve()}")


# Save parameters for reproducibility
parameters = {
    "run_info": {
        "timestamp": timestamp,
        "site_name": site_name,
        "project_type": project_type,
        "method": "RANSAC+PMF Sequential"
    },
    "ransac_parameters": {
        "distance_threshold": distance_threshold,
        "num_iterations": num_iterations,
        "min_inliers_ratio": min_inliers_ratio,
        "early_stop_ratio": early_stop_ratio
    },
    "pmf_parameters": {
        "cell_size": cell_size,
        "max_window_size": max_window_size,
        "slope": slope
    }
}

with open(analysis_output_path / "parameters.json", 'w') as f:
    json.dump(parameters, f, indent=2)

print(f"Parameters saved to: {analysis_output_path /method_name / 'parameters.json'}")
logger.info(f"RANSAC+PMF processing initialized for site: {site_name}")

import open3d as o3d

# Use the resolved and validated file
logger.info(f"Reading PLY file: {point_cloud_file.resolve()}")

pcd = o3d.io.read_point_cloud(str(point_cloud_file))

if not pcd.has_points():
    raise ValueError("Loaded PLY file contains no points.")

points = np.asarray(pcd.points)
logger.info(f"Loaded {points.shape[0]} points from {point_cloud_file}")



# Load Point Cloud data
if not pcd.has_points():
    raise ValueError("Loaded PLY file contains no points.")

points = np.asarray(pcd.points)

# Display basic statistics
logger.info(f"Point cloud statistics:")
logger.info("-" * 50)
logger.info(f"  Loaded {points.shape[0]} points")

x, y, z = points[:, 0], points[:, 1], points[:, 2]

logger.info(f"  X range: {x.min():.2f} to {x.max():.2f} ({x.max()-x.min():.2f}m)")
logger.info(f"  Y range: {y.min():.2f} to {y.max():.2f} ({y.max()-y.min():.2f}m)")
logger.info(f"  Z range: {z.min():.2f} to {z.max():.2f} ({z.max()-z.min():.2f}m)")

# Downsample for processing efficiency if needed
original_point_count = len(points)

if  original_point_count > max_points_processing:
    indices = np.random.choice(original_point_count, max_points_processing, replace=False)
    points = points[indices]
    logger.info(f"Downsampled to {len(points):,} points for processing")
    logger.info(f"Downsampled from {original_point_count:,} to {len(points):,} points")
else:
    logger.info(f"Using all {len(points):,} points for processing")

# Visualize original point cloud
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from matplotlib import cm
from matplotlib.colors import ListedColormap
from matplotlib.colors import Normalize

if create_visualizations:
    print("Creating original point cloud visualization...")
    
    # Subsample for visualization
    vis_sample_size = 50000
    if len(points) > vis_sample_size:
        vis_indices = np.random.choice(len(points), vis_sample_size, replace=False)
        vis_points = points[vis_indices]
    else:
        vis_points = points
    
    fig = plt.figure(figsize=(12, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # Color by height
    scatter = ax.scatter(vis_points[:, 0], vis_points[:, 1], vis_points[:, 2], 
                        c=vis_points[:, 2], cmap='viridis', s=1, alpha=0.6)
    
    ax.set_title(f'Original Point Cloud ({len(vis_points):,} points)')
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Z (m)')
    
    # Add colorbar
    plt.colorbar(scatter, ax=ax, label='Height (m)', shrink=0.8)
    
    plt.tight_layout()
    plt.show()
    
    logger.info("Original point cloud visualization complete")
else:
    logger.info("Visualization disabled - skipping original point cloud plot")

# Initialize RANSAC parameters
n_points = len(points)
min_inliers = int(n_points * min_inliers_ratio)
early_stop_inliers = int(n_points * early_stop_ratio)

logger.info(f"RANSAC Configuration:")
logger.info(f"- Distance threshold: {distance_threshold}m")
logger.info(f"- Iterations: {num_iterations}")
logger.info(f"- Min inliers: {min_inliers:,} ({min_inliers_ratio*100:.1f}%)")
logger.info(f"- Early stop: {early_stop_inliers:,} ({early_stop_ratio*100:.1f}%)")

# RANSAC algorithm - iterative plane fitting
from tqdm import tqdm
from datetime import datetime
import time
import numpy as np

best_plane_params = None
best_inliers = []
max_inliers = 0
start_time = time.time()

logger.info("Running RANSAC iterations...")
for i in tqdm(range(num_iterations), desc="RANSAC"):
    # Sample 3 random points to define plane
    sample_indices = np.random.choice(n_points, 3, replace=False)
    p1, p2, p3 = points[sample_indices]
    
    # Calculate plane normal using cross product
    v1, v2 = p2 - p1, p3 - p1
    normal = np.cross(v1, v2)
    
    # Skip degenerate cases (collinear points)
    if np.linalg.norm(normal) < 1e-6:
        continue
    
    # Normalize and ensure upward-facing normal
    normal = normal / np.linalg.norm(normal)
    if normal[2] < 0:
        normal = -normal
    
    # Plane equation: ax + by + cz + d = 0
    d = -np.dot(normal, p1)
    plane_params = np.append(normal, d)
    
    # Calculate point-to-plane distances
    distances = np.abs(np.dot(points, plane_params[:3]) + d)
    inliers = np.where(distances < distance_threshold)[0]
    n_inliers = len(inliers)
    
    # Update best plane if this one is better
    if n_inliers > max_inliers and n_inliers >= min_inliers:
        best_plane_params = plane_params
        best_inliers = inliers
        max_inliers = n_inliers
        
        # Early stopping if enough inliers found
        if n_inliers >= early_stop_inliers:
            print(f"\nEarly stopping at iteration {i+1} with {n_inliers:,} inliers")
            break

ransac_time = time.time() - start_time

if best_plane_params is None:
    raise RuntimeError("RANSAC failed to find a valid ground plane")

print(f"RANSAC completed in {ransac_time:.2f} seconds")

# Extract RANSAC ground and non-ground points
ransac_ground_mask = np.zeros(len(points), dtype=bool)
ransac_ground_mask[best_inliers] = True
ransac_nonground_mask = ~ransac_ground_mask

ransac_ground_points = points[ransac_ground_mask]
ransac_nonground_points = points[ransac_nonground_mask]

logger.info(f"RANSAC Results:")
logger.info(f"- Ground points: {len(ransac_ground_points):,} ({len(ransac_ground_points)/len(points)*100:.1f}%)")
logger.info(f"- Non-ground points: {len(ransac_nonground_points):,} ({len(ransac_nonground_points)/len(points)*100:.1f}%)")
logger.info(f"- Plane: {best_plane_params[0]:.3f}x + {best_plane_params[1]:.3f}y + {best_plane_params[2]:.3f}z + {best_plane_params[3]:.3f} = 0")

logger.info(f"RANSAC found {len(ransac_ground_points):,} ground points in {ransac_time:.2f}s")

# Visualize RANSAC results
if create_visualizations:
    print("Creating RANSAC results visualization...")
    
    # Use same visualization points as before
    if len(points) > vis_sample_size:
        vis_ransac_ground = ransac_ground_mask[vis_indices]
    else:
        vis_ransac_ground = ransac_ground_mask
        vis_points = points
    
    fig = plt.figure(figsize=(12, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # Plot ground points in brown, non-ground in green
    ground_vis = vis_points[vis_ransac_ground]
    nonground_vis = vis_points[~vis_ransac_ground]
    
    if len(ground_vis) > 0:
        ax.scatter(ground_vis[:, 0], ground_vis[:, 1], ground_vis[:, 2], 
                  c='brown', s=1, alpha=0.7, label=f'Ground ({len(ground_vis):,})')
    
    if len(nonground_vis) > 0:
        ax.scatter(nonground_vis[:, 0], nonground_vis[:, 1], nonground_vis[:, 2], 
                  c='green', s=1, alpha=0.7, label=f'Non-ground ({len(nonground_vis):,})')
    
    ax.set_title(f'RANSAC Ground Segmentation Results')
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Z (m)')
    ax.legend()
    
    plt.tight_layout()
    plt.show()
    
    logger.info("RANSAC results visualization complete")
else:
    logger.info("Visualization disabled - skipping RANSAC results plot")

# PMF configuration
logger.info(f"PMF Configuration:")
logger.info(f"- Processing {len(ransac_nonground_points):,} non-ground points from RANSAC")
logger.info(f"- Cell size: {cell_size}m")
logger.info(f"- Max window: {max_window_size}")
logger.info(f"- Slope threshold: {slope} radians")

if len(ransac_nonground_points) == 0:
    print("No non-ground points to process with PMF")
    pmf_additional_ground_points = np.array([]).reshape(0, 3)
    pmf_remaining_nonground_points = np.array([]).reshape(0, 3)
    pmf_time = 0.0
else:
    print("Starting PMF processing...")

# PMF algorithm - grid-based morphological filtering
import numpy as np
from scipy.ndimage import grey_erosion, grey_dilation

if len(ransac_nonground_points) > 0:
    pmf_start_time = time.time()
    
    # Create 2D grid from non-ground points
    min_xy = np.min(ransac_nonground_points[:, :2], axis=0)
    max_xy = np.max(ransac_nonground_points[:, :2], axis=0)
    dims = np.ceil((max_xy - min_xy) / cell_size).astype(int)
    
    print(f"- Grid dimensions: {dims[0]} x {dims[1]} cells")
    
    if dims[0] <= 0 or dims[1] <= 0:
        print("Invalid grid dimensions, skipping PMF")
        pmf_ground_mask_subset = np.array([False] * len(ransac_nonground_points))
    else:
        # Initialize grid with NaN
        grid = np.full(dims, np.nan)
        
        # Populate grid with minimum Z values per cell
        for i, (x, y, z) in enumerate(ransac_nonground_points):
            xi = int((x - min_xy[0]) / cell_size)
            yi = int((y - min_xy[1]) / cell_size)
            if 0 <= xi < dims[0] and 0 <= yi < dims[1]:
                if np.isnan(grid[xi, yi]) or z < grid[xi, yi]:
                    grid[xi, yi] = z
        
        print(f"- Grid populated with {np.sum(~np.isnan(grid))} non-empty cells")
        
        # Fill holes in grid
        filled_grid = ndimage.grey_closing(np.nan_to_num(grid, nan=np.nanmin(grid)), size=3)
        
        # Morphological opening (erosion followed by dilation)
        opened = grey_dilation(
            grey_erosion(filled_grid, size= max_window_size), 
            size= max_window_size
        )
        
        # Calculate height differences
        z_diff = filled_grid - opened
        ground_mask_2d = z_diff < slope
        
        print(f"- Morphological filtering completed")
        print(f"- Ground cells: {np.sum(ground_mask_2d)} / {np.prod(dims)}")
        
        # Map grid results back to points
        pmf_ground_mask_subset = []
        for x, y, z in ransac_nonground_points:
            xi = int((x - min_xy[0]) / cell_size)
            yi = int((y - min_xy[1]) / cell_size)
            if 0 <= xi < dims[0] and 0 <= yi < dims[1]:
                pmf_ground_mask_subset.append(ground_mask_2d[xi, yi])
            else:
                pmf_ground_mask_subset.append(False)
        
        pmf_ground_mask_subset = np.array(pmf_ground_mask_subset)
    
    pmf_time = time.time() - pmf_start_time
    print(f"PMF completed in {pmf_time:.2f} seconds")
else:
    pmf_ground_mask_subset = np.array([False] * len(ransac_nonground_points))
    pmf_time = 0.0

# Extract PMF results
pmf_additional_ground_points = ransac_nonground_points[pmf_ground_mask_subset]
pmf_remaining_nonground_points = ransac_nonground_points[~pmf_ground_mask_subset]

print(f"PMF Results:")
print(f"- Additional ground points: {len(pmf_additional_ground_points):,}")
print(f"- Remaining non-ground: {len(pmf_remaining_nonground_points):,}")
if len(ransac_nonground_points) > 0:
    print(f"  PMF recovery rate: {len(pmf_additional_ground_points)/len(ransac_nonground_points)*100:.1f}%")

logger.info(f"PMF found {len(pmf_additional_ground_points):,} additional ground points in {pmf_time:.2f}s")

# Visualize PMF additional ground points (if any found)
if create_visualizations and len(pmf_additional_ground_points) > 0:
    print("Creating PMF additional ground visualization...")
    
    fig = plt.figure(figsize=(12, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # Show only PMF additional ground points
    ax.scatter(pmf_additional_ground_points[:, 0],
               pmf_additional_ground_points[:, 1],
               pmf_additional_ground_points[:, 2],
               c='orange', s=2, alpha=0.8, label=f'PMF Additional Ground ({len(pmf_additional_ground_points):,})')
    
    ax.set_title(f'PMF Recovered Ground Points')
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Z (m)')
    ax.legend()
    
    plt.tight_layout()
    plt.show()
    
    print("PMF additional ground visualization complete")
elif create_visualizations:
    print("No PMF additional points to visualize")
else:
    print("Visualization disabled - skipping PMF results plot")

# Combine RANSAC and PMF ground points
if len(pmf_additional_ground_points) > 0:
    combined_ground_points = np.vstack([ransac_ground_points, pmf_additional_ground_points])
else:
    combined_ground_points = ransac_ground_points

combined_nonground_points = pmf_remaining_nonground_points
total_processing_time = ransac_time + pmf_time

print(f"Combined Results (RANSAC + PMF):")
print(f"- Total ground points: {len(combined_ground_points):,} ({len(combined_ground_points)/len(points)*100:.1f}%)")
print(f"- Final non-ground: {len(combined_nonground_points):,} ({len(combined_nonground_points)/len(points)*100:.1f}%)")
print(f"- Total processing time: {total_processing_time:.2f} seconds")
print(f"")
print(f"Breakdown:")
print(f"- RANSAC ground: {len(ransac_ground_points):,} ({len(ransac_ground_points)/len(points)*100:.1f}%)")
print(f"- PMF additional: {len(pmf_additional_ground_points):,} ({len(pmf_additional_ground_points)/len(points)*100:.1f}%)")
print(f"- Improvement: +{len(pmf_additional_ground_points)/len(points)*100:.1f}% ground coverage")

logger.info(f"Combined segmentation: {len(combined_ground_points):,} ground, {len(combined_nonground_points):,} non-ground")

# Visualize final combined results
if create_visualizations:
    print("Creating final combined results visualization...")
    
    # Create combined ground mask for visualization
    if len(points) > vis_sample_size:
        vis_combined_ground = vis_ransac_ground.copy()
        # Add PMF points to visualization (approximate)
        if len(pmf_additional_ground_points) > 0:
            for i, point in enumerate(vis_points):
                if not vis_ransac_ground[i]:  # Only check non-RANSAC points
                    distances = np.linalg.norm(pmf_additional_ground_points - point, axis=1)
                    if len(distances) > 0 and np.min(distances) < 0.1:  # Small tolerance
                        vis_combined_ground[i] = True
    else:
        # Create full combined mask
        vis_combined_ground = np.zeros(len(points), dtype=bool)
        vis_combined_ground[ransac_ground_mask] = True
        # Add PMF points
        if len(pmf_additional_ground_points) > 0:
            for pmf_point in pmf_additional_ground_points:
                distances = np.linalg.norm(points - pmf_point, axis=1)
                closest_idx = np.argmin(distances)
                if distances[closest_idx] < 0.1:
                    vis_combined_ground[closest_idx] = True
        vis_points = points
    
    fig = plt.figure(figsize=(12, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # Plot combined results
    combined_ground_vis = vis_points[vis_combined_ground]
    combined_nonground_vis = vis_points[~vis_combined_ground]
    
    if len(combined_ground_vis) > 0:
        ax.scatter(combined_ground_vis[:, 0], combined_ground_vis[:, 1], combined_ground_vis[:, 2], 
                  c='brown', s=1, alpha=0.7, label=f'Final Ground ({len(combined_ground_vis):,})')
    
    if len(combined_nonground_vis) > 0:
        ax.scatter(combined_nonground_vis[:, 0], combined_nonground_vis[:, 1], combined_nonground_vis[:, 2], 
                  c='green', s=1, alpha=0.7, label=f'Final Non-ground ({len(combined_nonground_vis):,})')
    
    ax.set_title(f'Final RANSAC + PMF Results')
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Z (m)')
    ax.legend()
    
    plt.tight_layout()
    plt.show()
    
    print("Final combined results visualization complete")
else:
    print("Visualization disabled - skipping final results plot")

# Create height distribution comparison
if create_visualizations:
    print("Creating height distribution analysis...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Height distribution histogram
    bins = 50
    ax1.hist(ransac_ground_points[:, 2], bins=bins, alpha=0.7, 
             label=f'RANSAC Ground ({len(ransac_ground_points):,})', color='brown', edgecolor='black')
    
    if len(pmf_additional_ground_points) > 0:
        ax1.hist(pmf_additional_ground_points[:, 2], bins=bins, alpha=0.7, 
                 label=f'PMF Additional ({len(pmf_additional_ground_points):,})', color='orange', edgecolor='black')
    
    ax1.hist(combined_nonground_points[:, 2], bins=bins, alpha=0.7, 
             label=f'Non-ground ({len(combined_nonground_points):,})', color='green', edgecolor='black')
    
    ax1.set_title('Height Distribution by Method')
    ax1.set_xlabel('Z (meters)')
    ax1.set_ylabel('Point Count')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Processing time comparison
    methods = ['RANSAC', 'PMF', 'Total']
    times = [ransac_time, pmf_time, total_processing_time]
    colors = ['blue', 'orange', 'red']
    
    bars = ax2.bar(methods, times, color=colors, alpha=0.7, edgecolor='black')
    ax2.set_title('Processing Time Comparison')
    ax2.set_ylabel('Time (seconds)')
    
    # Add value labels on bars
    for bar, time_val in zip(bars, times):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(times)*0.01, 
                f'{time_val:.2f}s', ha='center', va='bottom', fontweight='bold')
    
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    print("Statistical analysis visualization complete")
else:
    print("Visualization disabled - skipping statistical analysis")

# Save function using Open3D (consistent with other notebooks)
def save_ply(path, points_array, description=""):
    """Save points to PLY file using Open3D."""
    pc = o3d.geometry.PointCloud()
    pc.points = o3d.utility.Vector3dVector(points_array)
    o3d.io.write_point_cloud(str(path), pc)
    logger.info(f"- Saved: {path}")
    if description:
        print(f"- Saved {description}: {len(points_array):,} points to {path.name}")

logger.info("Saving point cloud outputs...")

# Save the point clouds to the appropriate output paths
analysis_output = analysis_output_path / 'analysis_output'
analysis_output.mkdir(parents=True, exist_ok=True)

# Save ground and nonground points to the analysis_output directory
save_ply(analysis_output / f"{site_name}_ground.ply", combined_ground_points)
save_ply(analysis_output / f"{site_name}_nonground.ply", combined_nonground_points)

# Satisfied with the results save the final output for next stage 
save_ply(ground_seg_path / f"{method_name}/{site_name}_ground.ply", combined_ground_points)
save_ply(ground_seg_path / f"{method_name}/{site_name}_nonground.ply", combined_nonground_points)

logger.info(f"\nRANSAC PMF segmentation outputs saved:")
logger.info(f"  Ground points: {len(combined_ground_points):,}")
logger.info(f"  Non-ground points: {len(combined_nonground_points):,}")
logger.info(f"  Method identifier: {method_name}")


from laspy import LasData, LasHeader
from pyproj import CRS

def save_las(output_path, points, epsg_code=32632):
    header = LasHeader(version="1.4", point_format=3)
    crs = CRS.from_epsg(epsg_code)
    header.add_crs(crs)

    las = LasData(header)
    las.x, las.y, las.z = points[:, 0], points[:, 1], points[:, 2]
    las.write(str(output_path))
    print(f"Saved LAS: {output_path} (EPSG:{epsg_code})")

# Save intermediate results if requested
if save_intermediate_results:
    print("Saving intermediate results...")
    
    # Define EPSG from earlier (e.g., from LAS or project config)
    epsg_code = 32632  # Italy's UTM Zone 33N

    # Save with projection-aware writer
    save_ply(analysis_output / "ransac_only_ground.ply", ransac_ground_points, "RANSAC ground")
    save_las(analysis_output / "ransac_only_ground.las", ransac_ground_points, epsg_code)

    save_ply(analysis_output / "ransac_only_nonground.ply", ransac_nonground_points, "RANSAC non-ground")
    save_las(analysis_output / "ransac_only_nonground.las", ransac_nonground_points, epsg_code)

    if len(pmf_additional_ground_points) > 0:
        save_ply(analysis_output / "pmf_additional_ground.ply", pmf_additional_ground_points, "PMF additional ground")
        save_las(analysis_output / "pmf_additional_ground.las", pmf_additional_ground_points, epsg_code)

    print(f"- Intermediate results saved to: {analysis_output}")
else:
    print("- Intermediate results saving disabled")

# Save CRS metadata for downstream processing
save_ply(ground_seg_path / f"{method_name}/{site_name}_nonground.ply", combined_nonground_points)

#crs_metadata_path = analysis_output / "crs_metadata.json"
crs_metadata_path = ground_seg_path / f"{method_name}/{site_name}_crs_metadata.json"
with open(crs_metadata_path, 'w') as f:
    json.dump({
        'epsg': epsg_code,
        'utm_zone': f"UTM Zone {epsg_code - 32600}N",
        'description': "CRS used for ground segmentation results",
        'source': "Set manually based on project location (Italy)"
    }, f, indent=2)

print(f"CRS metadata saved: {crs_metadata_path}")


# Save processing results summary
results_summary = {
    "processing_info": {
        "total_points": len(points),
        "original_points": original_point_count,
        "total_time_seconds": total_processing_time,
        "ransac_time_seconds": ransac_time,
        "pmf_time_seconds": pmf_time
    },
    "ransac_results": {
        "ground_points": len(ransac_ground_points),
        "ground_ratio": len(ransac_ground_points) / len(points),
        "plane_equation": best_plane_params.tolist()
    },
    "pmf_results": {
        "additional_ground_points": len(pmf_additional_ground_points),
        "additional_ground_ratio": len(pmf_additional_ground_points) / len(points)
    },
    "combined_results": {
        "final_ground_points": len(combined_ground_points),
        "final_nonground_points": len(combined_nonground_points),
        "final_ground_ratio": len(combined_ground_points) / len(points)
    }
}
# Construct the output path
summary_dir = analysis_output / method_name
summary_dir.mkdir(parents=True, exist_ok=True)  # ✅ ensure directory exists

summary_path = summary_dir / "results_summary.json"

# Write the summary JSON
with open(summary_path, 'w') as f:
    json.dump(results_summary, f, indent=2)

print(f"Results summary saved to: {summary_path}")


# Comprehensive final processing summary
print("\n" + "="*70)
print("RANSAC + PMF GROUND SEGMENTATION COMPLETE")
print("="*70)
print(f"Site: {site_name} ({project_type})")
print(f"Method: {method_name}")
print(f"Timestamp: {timestamp}")
print("")
print(f"Data Processing:")
print(f"- Input: {original_point_count:,} points")
print(f"- Processed: {len(points):,} points")
print(f"- Downsampling: {'Yes' if len(points) < original_point_count else 'No'}")
print("")
print(f"RANSAC Results:")
print(f"- Ground points: {len(ransac_ground_points):,} ({len(ransac_ground_points)/len(points)*100:.1f}%)")
print(f"- Processing time: {ransac_time:.2f} seconds")
print(f"- Plane equation: {best_plane_params[0]:.3f}x + {best_plane_params[1]:.3f}y + {best_plane_params[2]:.3f}z + {best_plane_params[3]:.3f} = 0")
print("")
print(f"PMF Results:")
print(f"- Additional ground: {len(pmf_additional_ground_points):,} ({len(pmf_additional_ground_points)/len(points)*100:.1f}%)")
print(f"- Processing time: {pmf_time:.2f} seconds")
if len(ransac_nonground_points) > 0:
    print(f"  Recovery rate: {len(pmf_additional_ground_points)/len(ransac_nonground_points)*100:.1f}% of non-ground points")
print("")
print(f"Final Results:")
print(f"- Total ground: {len(combined_ground_points):,} ({len(combined_ground_points)/len(points)*100:.1f}%)")
print(f"- Final non-ground: {len(combined_nonground_points):,} ({len(combined_nonground_points)/len(points)*100:.1f}%)")
print(f"- Improvement: +{len(pmf_additional_ground_points)/len(points)*100:.1f}% ground coverage")
print(f"- Total processing time: {total_processing_time:.2f} seconds")
print("")
print(f"Outputs:")
print(f"- Main results: {ground_seg_path}")
print(f"- Run details: {analysis_output_path}")
print(f"- Parameters: {analysis_output_path / method_name/ 'parameters.json'}")
print(f"- Results summary: {analysis_output_path / method_name/'results_summary.json'}")
print("="*70)

logger.info(f"RANSAC+PMF processing completed successfully for {site_name}")