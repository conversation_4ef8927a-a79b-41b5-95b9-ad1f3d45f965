# Papermill parameters - standardized path handling
import sys
from pathlib import Path
# Add the `notebooks` folder to sys.path
notebooks_root = Path(__file__).resolve().parents[1] if '__file__' in globals() else Path.cwd().parents[1]

if str(notebooks_root) not in sys.path:
    sys.path.insert(0, str(notebooks_root))

# Now import from shared package
from shared.config import get_data_path, get_output_path, get_mlflow_tracking_uri, find_latest_file, get_processed_data_path

# Papermill parameters
site_name = "trino_enel"
project_type = "ENEL"
analysis_type = "ground_segmentation_comparison"
timestamp = None  # Auto-generated if None

# Methods to compare (based on executed notebooks)
methods_to_compare = ["csf", "pmf", "ransac", "ransac_pmf"]

# Standardized paths
ground_seg_data_path = get_processed_data_path(site_name, "ground_segmentation")
output_path = get_output_path(analysis_type, site_name, timestamp)
mlflow_tracking_uri = get_mlflow_tracking_uri()

# Analysis parameters
visualization_sample_ratio = 0.1  # Sample ratio for visualization (10% of points)
statistical_analysis_enabled = True
generate_comparison_plots = True
export_results = True

# MLflow configuration
mlflow_experiment_name = f"ground_segmentation_comparison_{project_type}"
mlflow_run_name = f"comparison_analysis_{site_name}_{timestamp or 'auto'}"

# Import required libraries
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import open3d as o3d
import json
import logging
from datetime import datetime
from pathlib import Path
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# MLflow for metrics retrieval
import mlflow
import mlflow.sklearn

# Statistical analysis
from scipy import stats
from sklearn.metrics import classification_report

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Set plotting style
plt.style.use('default')
sns.set_palette("husl")

logger.info("Ground Segmentation Comparison Analysis - Environment Ready!")
logger.info(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
logger.info(f"Site: {site_name}")
logger.info(f"Methods to Compare: {methods_to_compare}")
logger.info(f"Data Path: {ground_seg_data_path}")
logger.info(f"Output Path: {output_path}")

# Discover available data from executed notebooks
def discover_ground_segmentation_results(base_path, methods):
    """Discover and validate ground segmentation results from executed notebooks"""
    
    results_inventory = {}
    
    for method in methods:
        method_path = base_path / method
        
        if method_path.exists():
            # Look for ground and non-ground files
            ground_file = method_path / f"{site_name}_ground.ply"
            nonground_file = method_path / f"{site_name}_nonground.ply"
            
            results_inventory[method] = {
                'method_path': method_path,
                'ground_file': ground_file if ground_file.exists() else None,
                'nonground_file': nonground_file if nonground_file.exists() else None,
                'available': ground_file.exists() and nonground_file.exists()
            }
            
            # Get file statistics
            if results_inventory[method]['available']:
                ground_size = ground_file.stat().st_size / (1024 * 1024)  # MB
                nonground_size = nonground_file.stat().st_size / (1024 * 1024)  # MB
                
                results_inventory[method].update({
                    'ground_file_size_mb': round(ground_size, 2),
                    'nonground_file_size_mb': round(nonground_size, 2),
                    'total_size_mb': round(ground_size + nonground_size, 2)
                })
        else:
            results_inventory[method] = {
                'method_path': method_path,
                'available': False,
                'error': f"Method directory not found: {method_path}"
            }
    
    return results_inventory

# Discover available results
results_inventory = discover_ground_segmentation_results(ground_seg_data_path, methods_to_compare)

# Display inventory
logger.info("Ground Segmentation Results Inventory:")
for method, info in results_inventory.items():
    if info['available']:
        logger.info(f"{method.upper()}: Available ({info['total_size_mb']} MB total)")
        logger.info(f"   Ground: {info['ground_file_size_mb']} MB | Non-ground: {info['nonground_file_size_mb']} MB")
    else:
        logger.error(f"{method.upper()}: {info.get('error', 'Not available')}")

# Filter to available methods only
available_methods = [method for method, info in results_inventory.items() if info['available']]
logger.info(f"\nAvailable methods for comparison: {available_methods}")

if not available_methods:
    raise ValueError("No ground segmentation results found. Please execute notebooks 01-04 first.")

# Load point cloud data for each method
def load_point_cloud_data(results_inventory, available_methods, sample_ratio=0.1):
    """Load and sample point cloud data from each method"""
    
    point_cloud_data = {}
    
    for method in available_methods:
        logger.info(f"Loading point cloud data for {method.upper()}...")
        
        try:
            # Load ground points
            ground_pcd = o3d.io.read_point_cloud(str(results_inventory[method]['ground_file']))
            ground_points = np.asarray(ground_pcd.points)
            
            # Load non-ground points
            nonground_pcd = o3d.io.read_point_cloud(str(results_inventory[method]['nonground_file']))
            nonground_points = np.asarray(nonground_pcd.points)
            
            # Sample points for visualization and analysis
            if len(ground_points) > 0:
                ground_sample_size = max(1000, int(len(ground_points) * sample_ratio))
                ground_indices = np.random.choice(len(ground_points), 
                                                min(ground_sample_size, len(ground_points)), 
                                                replace=False)
                ground_sample = ground_points[ground_indices]
            else:
                ground_sample = ground_points
            
            if len(nonground_points) > 0:
                nonground_sample_size = max(1000, int(len(nonground_points) * sample_ratio))
                nonground_indices = np.random.choice(len(nonground_points), 
                                                   min(nonground_sample_size, len(nonground_points)), 
                                                   replace=False)
                nonground_sample = nonground_points[nonground_indices]
            else:
                nonground_sample = nonground_points
            
            # Store data and statistics
            point_cloud_data[method] = {
                'ground_points_full': ground_points,
                'nonground_points_full': nonground_points,
                'ground_points_sample': ground_sample,
                'nonground_points_sample': nonground_sample,
                'total_points': len(ground_points) + len(nonground_points),
                'ground_points_count': len(ground_points),
                'nonground_points_count': len(nonground_points),
                'ground_percentage': len(ground_points) / (len(ground_points) + len(nonground_points)) * 100,
                'nonground_percentage': len(nonground_points) / (len(ground_points) + len(nonground_points)) * 100
            }
            
            logger.info(f"{method.upper()}: {point_cloud_data[method]['total_points']:,} total points")
            logger.info(f"     Ground: {point_cloud_data[method]['ground_points_count']:,} ({point_cloud_data[method]['ground_percentage']:.1f}%)")
            logger.info(f"     Non-ground: {point_cloud_data[method]['nonground_points_count']:,} ({point_cloud_data[method]['nonground_percentage']:.1f}%)")
            
        except Exception as e:
            logger.error(f"Failed to load {method}: {e}")
            continue
    
    return point_cloud_data

# Load point cloud data
logger.info("Loading point cloud data from executed results...")
point_cloud_data = load_point_cloud_data(results_inventory, available_methods, visualization_sample_ratio)

# Update available methods based on successful loading
successfully_loaded_methods = list(point_cloud_data.keys())
logger.info(f"\nSuccessfully loaded methods: {successfully_loaded_methods}")

# Create comparison statistics
def create_comparison_statistics(point_cloud_data):
    """Create comprehensive comparison statistics"""
    
    comparison_stats = []
    
    for method, data in point_cloud_data.items():
        # Basic statistics
        ground_points = data['ground_points_full']
        nonground_points = data['nonground_points_full']
        
        # Height statistics
        if len(ground_points) > 0:
            ground_z_stats = {
                'ground_z_mean': np.mean(ground_points[:, 2]),
                'ground_z_std': np.std(ground_points[:, 2]),
                'ground_z_min': np.min(ground_points[:, 2]),
                'ground_z_max': np.max(ground_points[:, 2]),
                'ground_z_range': np.max(ground_points[:, 2]) - np.min(ground_points[:, 2])
            }
        else:
            ground_z_stats = {k: 0 for k in ['ground_z_mean', 'ground_z_std', 'ground_z_min', 'ground_z_max', 'ground_z_range']}
        
        if len(nonground_points) > 0:
            nonground_z_stats = {
                'nonground_z_mean': np.mean(nonground_points[:, 2]),
                'nonground_z_std': np.std(nonground_points[:, 2]),
                'nonground_z_min': np.min(nonground_points[:, 2]),
                'nonground_z_max': np.max(nonground_points[:, 2]),
                'nonground_z_range': np.max(nonground_points[:, 2]) - np.min(nonground_points[:, 2])
            }
        else:
            nonground_z_stats = {k: 0 for k in ['nonground_z_mean', 'nonground_z_std', 'nonground_z_min', 'nonground_z_max', 'nonground_z_range']}
        
        # Combine all statistics
        method_stats = {
            'method': method,
            'total_points': data['total_points'],
            'ground_points': data['ground_points_count'],
            'nonground_points': data['nonground_points_count'],
            'ground_percentage': data['ground_percentage'],
            'nonground_percentage': data['nonground_percentage'],
            **ground_z_stats,
            **nonground_z_stats
        }
        
        comparison_stats.append(method_stats)
    
    return pd.DataFrame(comparison_stats)

# Create comparison statistics
comparison_df = create_comparison_statistics(point_cloud_data)

# Display comparison table
logger.info("\nComparison Statistics:")
logger.info("\n=== GROUND SEGMENTATION COMPARISON SUMMARY ===")
logger.info(comparison_df[['method', 'total_points', 'ground_points', 'nonground_points', 
                    'ground_percentage', 'nonground_percentage']].to_string(index=False))

logger.info("\n=== HEIGHT STATISTICS ===")
logger.info(comparison_df[['method', 'ground_z_mean', 'ground_z_std', 'ground_z_range',
                    'nonground_z_mean', 'nonground_z_std', 'nonground_z_range']].round(3).to_string(index=False))

import matplotlib.pyplot as plt
from matplotlib.ticker import MaxNLocator, ScalarFormatter

def plot_top_view(ax, ground_points, nonground_points, method, colors):
    """Scatter plot of X-Y top view with clearer tick labels."""
    if len(ground_points) > 0:
        ax.scatter(ground_points[:, 0], ground_points[:, 1],
                   c=colors['ground'], s=0.5, alpha=0.6, label=f'{method.upper()} Ground')
    if len(nonground_points) > 0:
        ax.scatter(nonground_points[:, 0], nonground_points[:, 1],
                   c=colors['nonground'], s=0.5, alpha=0.6, label=f'{method.upper()} Non-Ground')
    
    ax.set_xlabel('X [m]', fontsize=12)
    ax.set_ylabel('Y [m]', fontsize=12)
    ax.set_title(f'{method.upper()}: Top View', fontsize=13, weight='bold')
    ax.set_aspect('equal')
    ax.tick_params(labelsize=10)

    # Apply formatter and tick spacing
    ax.xaxis.set_major_locator(MaxNLocator(nbins=5))
    ax.yaxis.set_major_locator(MaxNLocator(nbins=5))
    ax.xaxis.set_major_formatter(ScalarFormatter(useMathText=True))
    ax.yaxis.set_major_formatter(ScalarFormatter(useMathText=True))

    ax.grid(True, alpha=0.3)

def plot_height_distribution(ax, ground_points, nonground_points, method, colors):
    """Plot height distribution for ground and non-ground points."""
    if len(ground_points) > 0:
        ax.hist(ground_points[:, 2], bins=50, alpha=0.7, color=colors['ground'],
                label=f'{method.upper()} Ground', density=True)
    if len(nonground_points) > 0:
        ax.hist(nonground_points[:, 2], bins=50, alpha=0.7, color=colors['nonground'],
                label=f'{method.upper()} Non-Ground', density=True)
    ax.set_xlabel('Height (Z) [m]', fontsize=11)
    ax.set_ylabel('Density', fontsize=11)
    ax.set_title(f'{method.upper()}: Height Distribution', fontsize=13, weight='bold')
    ax.legend(fontsize=9)
    ax.tick_params(labelsize=9)
    ax.grid(True, alpha=0.3)

def plot_key_metrics(ax, stats_row, method):
    """Bar plot of key metrics."""
    metrics = {
        'Ground Ratio': stats_row['ground_percentage'] / 100,
        'Z Separation': stats_row['nonground_z_mean'] - stats_row['ground_z_mean'],
        'Ground Z Std': stats_row['ground_z_std']
    }
    names = list(metrics.keys())
    values = list(metrics.values())

    bars = ax.bar(names, values, color=['#4CAF50', '#9E9E9E', '#2196F3'], alpha=0.8)
    for bar, val in zip(bars, values):
        ax.text(bar.get_x() + bar.get_width()/2., val, f'{val:.3f}',
                ha='center', va='bottom', fontsize=10, weight='bold')
    ax.set_title(f'{method.upper()}: Key Metrics', fontsize=13, weight='bold')
    ax.set_ylabel('Value', fontsize=11)
    ax.tick_params(labelsize=9)
    ax.grid(True, alpha=0.3)
    plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

def create_comparison_visualizations(point_cloud_data, comparison_df, output_path):
    """Create comprehensive comparison visualizations for ground segmentation."""
    plt.style.use('default')
    
    method_colors = {
        'csf': {'ground': '#2E8B57', 'nonground': '#DC143C'},
        'pmf': {'ground': '#4169E1', 'nonground': '#FF8C00'},
        'ransac': {'ground': '#9932CC', 'nonground': '#FFD700'},
        'ransac_pmf': {'ground': '#008B8B', 'nonground': '#FF6347'}
    }

    methods = list(point_cloud_data.keys())
    n_methods = len(methods)
    
    fig, axes = plt.subplots(n_methods, 3, figsize=(18, 5 * n_methods), constrained_layout=True)
    fig.suptitle('Ground Segmentation Method Comparison', fontsize=20, fontweight='bold', y=1.01)

    for i, method in enumerate(methods):
        data = point_cloud_data[method]
        colors = method_colors.get(method, {'ground': '#2E8B57', 'nonground': '#DC143C'})
        stats_row = comparison_df[comparison_df['method'] == method].iloc[0]
        
        # Axes per row
        ax1, ax2, ax3 = axes[i]
        plot_height_distribution(ax1, data['ground_points_sample'], data['nonground_points_sample'], method, colors)
        plot_top_view(ax2, data['ground_points_sample'], data['nonground_points_sample'], method, colors)
        plot_key_metrics(ax3, stats_row, method)

    return fig


# Create visualizations
if generate_comparison_plots:
    logger.info("Creating comparison visualizations...")
    output_path.mkdir(parents=True, exist_ok=True)

    comparison_fig = create_comparison_visualizations(point_cloud_data, comparison_df, output_path)
    comparison_plot_path = output_path / f"ground_segmentation_comparison_{site_name}.png"
    comparison_fig.savefig(comparison_plot_path, dpi=300, bbox_inches='tight')
    logger.info(f"Saved comparison plot: {comparison_plot_path}")
    plt.show()
else:
    logger.info("Visualization generation disabled.")

# Setup MLflow for comparison analysis
def setup_comparison_mlflow():
    """Setup MLflow for comparison analysis tracking"""
    
    try:
        mlflow.set_tracking_uri(mlflow_tracking_uri)
        mlflow.set_experiment(mlflow_experiment_name)
        
        return mlflow.start_run(run_name=mlflow_run_name)
    except Exception as e:
        logger.warning(f"Could not setup MLflow: {e}")
        return None

# Log comparison results to MLflow
def log_comparison_results(comparison_df, point_cloud_data):
    """Log comparison analysis results to MLflow"""
    
    try:
        # Log parameters
        mlflow.log_param("site_name", site_name)
        mlflow.log_param("project_type", project_type)
        mlflow.log_param("analysis_type", analysis_type)
        mlflow.log_param("methods_compared", ",".join(successfully_loaded_methods))
        mlflow.log_param("total_methods", len(successfully_loaded_methods))
        
        # Log aggregate metrics
        total_points_all_methods = comparison_df['total_points'].sum()
        avg_ground_percentage = comparison_df['ground_percentage'].mean()
        std_ground_percentage = comparison_df['ground_percentage'].std()
        
        mlflow.log_metric("total_points_all_methods", total_points_all_methods)
        mlflow.log_metric("avg_ground_percentage", avg_ground_percentage)
        mlflow.log_metric("std_ground_percentage", std_ground_percentage)
        
        # Log method-specific metrics
        for _, row in comparison_df.iterrows():
            method = row['method']
            mlflow.log_metric(f"{method}_total_points", row['total_points'])
            mlflow.log_metric(f"{method}_ground_percentage", row['ground_percentage'])
            mlflow.log_metric(f"{method}_ground_z_range", row['ground_z_range'])
            mlflow.log_metric(f"{method}_nonground_z_range", row['nonground_z_range'])
        
        # Log comparison summary as artifact
        comparison_summary_path = output_path / "comparison_summary.csv"
        comparison_df.to_csv(comparison_summary_path, index=False)
        mlflow.log_artifact(str(comparison_summary_path))
        
        logger.info("Successfully logged comparison results to MLflow")
        
    except Exception as e:
        logger.warning(f"Could not log to MLflow: {e}")

# Setup MLflow and log results
mlflow_run = setup_comparison_mlflow()

if mlflow_run:
    with mlflow_run:
        log_comparison_results(comparison_df, point_cloud_data)
        
        # Log visualization if created
        if generate_comparison_plots:
            comparison_plot_path = output_path / f"ground_segmentation_comparison_{site_name}.png"
            if comparison_plot_path.exists():
                mlflow.log_artifact(str(comparison_plot_path))
else:
    logger.info("MLflow logging skipped")

def export_comparison_results(comparison_df, point_cloud_data, output_path, site_name, project_type, successfully_loaded_methods):
    """Export structured comparison results with stats, rankings, and recommendations."""

    results = {
        'analysis_metadata': {
            'site_name': site_name,
            'project_type': project_type,
            'analysis_date': datetime.now().isoformat(),
            'methods_analyzed': successfully_loaded_methods,
            'total_methods': len(successfully_loaded_methods)
        },
        'summary_statistics': comparison_df.to_dict('records'),
        'method_rankings': {
            'by_total_points': comparison_df.nlargest(len(comparison_df), 'total_points')['method'].tolist(),
            'by_ground_percentage': comparison_df.nlargest(len(comparison_df), 'ground_percentage')['method'].tolist(),
            'by_ground_z_range': comparison_df.nsmallest(len(comparison_df), 'ground_z_range')['method'].tolist()
        },
        'recommendations': {
            'most_conservative': comparison_df.loc[comparison_df['ground_percentage'].idxmax(), 'method'],
            'most_aggressive': comparison_df.loc[comparison_df['ground_percentage'].idxmin(), 'method'],
            'most_consistent_ground': comparison_df.loc[comparison_df['ground_z_range'].idxmin(), 'method'],
            'largest_dataset': comparison_df.loc[comparison_df['total_points'].idxmax(), 'method']
        }
    }

    # Save JSON
    json_path = output_path / f"ground_segmentation_comparison_results_{site_name}.json"
    with open(json_path, 'w') as f:
        json.dump(results, f, indent=2)

    # Save CSV
    csv_path = output_path / f"ground_segmentation_comparison_summary_{site_name}.csv"
    comparison_df.to_csv(csv_path, index=False)

    logger.info(f"✓ Exported JSON: {json_path}")
    logger.info(f"✓ Exported CSV : {csv_path}")

    return results

output_path = Path("../../data/processed/ground_segmentation")  # update as needed
output_path.mkdir(parents=True, exist_ok=True)

if export_results:
    logger.info("Exporting comparison results...")

    final_results = export_comparison_results(
        comparison_df,
        point_cloud_data,
        output_path,
        site_name,
        project_type,
        successfully_loaded_methods
    )
else:
    logger.info("Exporting comparison results skipped")

from pprint import pprint

logger.info("=" * 80)
logger.info("GROUND SEGMENTATION COMPARISON ANALYSIS - FINAL SUMMARY")
logger.info("=" * 80)
logger.info(f"Site: {site_name}")
logger.info(f"Methods Analyzed: {', '.join([m.upper() for m in successfully_loaded_methods])}")
logger.info(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


logger.info("STATISTICS SUMMARY:")
logger.info(f"  • Average Ground Percentage: {comparison_df['ground_percentage'].mean():.1f}%")
logger.info(f"  • Standard Deviation        : {comparison_df['ground_percentage'].std():.1f}%")
logger.info(f"  • Total Points (all methods): {comparison_df['total_points'].sum():,}")

logger.info("=" * 80)
logger.info("Ground Segmentation Comparison Complete")

